{"name": "智慧后勤系统", "appid": "__UNI__2F310F5", "description": "智慧后勤系统", "versionName": "1.1.14", "versionCode": 1114, "transformPx": false, "app-plus": {"compatible": {"ignoreVersion": true}, "usingComponents": true, "nvueCompiler": "uni-app", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Barcode": {}, "Bluetooth": {}, "Camera": {}, "Fingerprint": {}, "Record": {}, "VideoPlayer": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"dSYMs": false, "privacyDescription": {"NSMicrophoneUsageDescription": "需要访问麦克风进行语音识别"}}, "sdkConfigs": {"ad": {}, "maps": {}, "push": {}}, "splashscreen": {"useOriginalMsgbox": true}}}, "quickapp": {}, "mp-weixin": {"appid": "wxff05e780e7f9d189", "setting": {"urlCheck": false, "es6": false, "minified": true, "postcss": true}, "optimization": {"subPackages": true}, "usingComponents": true, "permission": {"scope.record": {"desc": "需要使用您的录音功能进行语音识别"}}}, "vueVersion": "2", "h5": {"template": "static/index.html", "devServer": {"port": 8080, "disableHostCheck": true, "proxy": {"/": {"target": "https://zhhq.gzzlyy.com:10008", "changeOrigin": true}}}, "title": "RuoYi-App", "router": {"mode": "hash", "base": "./"}}, "mp-alipay": {"appid": "3d81a844-550f-488b-adce-836428a5fa8d"}, "fallbackLocale": "zh-Hans", "locale": "zh-Hans"}