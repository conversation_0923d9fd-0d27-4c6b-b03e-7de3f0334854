<!-- components/parser-nuoyi-form/child-components/FormDataSelect.vue -->
<template>
  <view class="form-data-select">
    <uni-data-select
        v-model="selectedValue"
        :localdata="formattedOptions"
        :placeholder="placeholder"
        :clearable="clearable"
        :disabled="field.disabled"
        :filterable="filterable"
        @change="handleChange"/>
  </view>
</template>

<script>
import {
  deepClone
} from "@/utils/comUtil";
export default {
  name: 'FormDataSelect',

  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    },
  },
  watch: {
    field: {
      immediate: true, // 立即执行
      handler(newVal, oldVal) {
        //console.log('FormDataSelect--field--handler',newVal, oldVal)
        this.initOptions()
      }
    },
  },
  computed: {
    formattedOptions() {
      // 将选项格式化为 { value, text } 结构
      return this.options.map(option => ({
        value: option.value,
        text: option.label
      }));
    }
  },

  data() {
    return {
      selectedValue: this.field.__config__?.defaultValue || '',  // 获取当前选中的值
      options: this.field.__slot__.options || [],  // 从 __slot__ 中获取选项
      placeholder: this.field.placeholder || '请选择',  // 占位符
      clearable: this.field.clearable || false,  // 是否可清除
      filterable: this.field.filterable || false,  // 是否可过滤
      multiple: this.field.multiple || false  // 是否多选
    }
  },

  methods: {
    initOptions(){
      this.options = deepClone(this.field.__slot__.options);
    },
    handleChange(value) {
      this.selectedValue = value;  // 更新选中的值
      //this.$emit('update:value', this.selectedValue);  // 触发更新事件
      this.$emit('update:value', {
        curField:this.field,
        curVal:this.selectedValue
      });
    }
  }
}
</script>

<style scoped>
/* 样式保持不变 */
.form-data-select {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
}
</style>