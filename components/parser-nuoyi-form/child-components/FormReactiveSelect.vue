<script>
import {
  getWaterElectricENG,
  getITENG,
  getDeviceDeptENG,
  getSecurityStaff,
  getCarpenter,
  getElevatorO2Staff,
  getDahuaENG
} from '@/api/repair.js'

// API方法映射
const API_METHODS = {
  getWaterElectricENG,
  getITENG,
  getDeviceDeptENG,
  getSecurityStaff,
  getCarpenter,
  getElevatorO2Staff,
  getDahuaENG
}

export default {
  name: "FormReactiveSelect",
  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      selectedValue: this.field.__config__?.defaultValue || '',
      options: [],
      loading: false
    }
  },
  computed: {
    localData() {
      return this.options.map(item => ({
        value: item[this.field.valueField || 'value'],
        text: item[this.field.labelField || 'label']
      }))
    }
  },
  watch: {
    field: {
      immediate: true,
      handler() {
        this.fetchOptions()
      }
    }
  },
  created() {
    console.log('FormReactiveSelect created field', this.field)
  },
  methods: {
    handleChange(value) {
      this.selectedValue = value
      this.$emit('update:value', {
        curField: this.field,
        curVal: this.selectedValue
      })
    },
    async fetchOptions() {
      if (!this.field.dataSource) {
        console.warn('未配置数据源')
        return
      }

      const apiMethod = API_METHODS[this.field.dataSource]
      if (!apiMethod) {
        console.warn(`未找到API方法: ${this.field.dataSource}`)
        return
      }
      
      try {
        this.loading = true
        const response = await apiMethod(this.field.apiParams || {})
        
        if (response && response.data) {
          this.options = response.data
        } else {
          this.options = []
          console.warn('API响应格式无效')
        }
      } catch (error) {
        console.error('获取下拉框选项失败:', error)
        this.options = []
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<template>
  <view class="form-reactive-select">
    <uni-data-select
      v-model="selectedValue"
      :localdata="localData"
      :placeholder="field.placeholder || '请选择'"
      :clearable="field.clearable"
      :disabled="field.disabled || loading"
      @change="handleChange"
    />
    <view v-if="loading" class="loading-tip">加载中...</view>
  </view>
</template>

<style scoped>
.form-reactive-select {
  width: 100%;
  position: relative;
}
.loading-tip {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
}
</style>