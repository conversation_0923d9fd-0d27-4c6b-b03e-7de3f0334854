<template>
  <view class="upload-container">
    <!-- 上传按钮 -->
    <view class="upload-box" @click="chooseImage" :class="{ disabled: field.disabled }">
      <text class="upload-icon">+</text>
      <text class="upload-text">{{ field.__config__.buttonText || '点击上传' }}</text>
    </view>

    <!-- 图片预览区域 -->
    <view v-if="imageList && imageList.length > 0" class="preview-box">
      <!-- 单个图片直接展示 -->
      <view v-if="imageList.length === 1" class="single-image">
        <view class="image-container">
          <image
              :src="imageList[0].url"
              class="uploaded-image"
              mode="widthFix"
              @error="handleImageError(0)"
          />
          <view class="image-actions">
            <text class="action-btn preview" @click="previewImage(0)">预览</text>
            <text class="action-btn delete" @click="deleteImage(0)">删除</text>
          </view>
        </view>
      </view>
      
      <!-- 多个图片遍历展示 -->
      <view v-else class="multiple-images">
        <view v-for="(item, index) in imageList" :key="index" class="image-item">
          <view class="image-container">
            <image
                :src="item.url"
                class="uploaded-image"
                mode="widthFix"
                @error="handleImageError(index)"
            />
            <view class="image-actions">
              <text class="action-btn preview" @click="previewImage(index)">预览</text>
              <text class="action-btn delete" @click="deleteImage(index)">删除</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 调试信息 -->
    <view v-if="showDebug" class="debug-info">
      <text>imageList的值：{{ JSON.stringify(imageList, null, 2) }}</text>
    </view>
  </view>
</template>

<script>
import { uploadImage } from '@/api/system/user'

export default {
  name: 'FormUpload',
  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      imageList: [],
      showDebug: false, // 是否显示调试信息
      maxCount: 9, // 最大上传数量
      uploading: false // 是否正在上传
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      if (this.field.__config__.defaultValue) {
        this.imageList = Array.isArray(this.field.__config__.defaultValue) 
          ? this.field.__config__.defaultValue 
          : JSON.parse(this.field.__config__.defaultValue || '[]')
      }
    },

    // 选择图片
    async chooseImage() {
      if (this.field.disabled || this.uploading) return
      
      try {
        this.uploading = true
        const res = await new Promise((resolve, reject) => {
          uni.chooseImage({
            count: this.maxCount - this.imageList.length,
            sizeType: ['original', 'compressed'],
            sourceType: ['album', 'camera'],
            success: resolve,
            fail: reject
          })
        })

        // 上传选中的图片
        for (const tempFilePath of res.tempFilePaths) {
          const uploadRes = await this.uploadImage(tempFilePath)
          this.imageList.push({
            name: uploadRes.fileName,
            url: uploadRes.url
          })
        }

        // 更新父组件数据
       // this.$emit('update:value', JSON.stringify(this.imageList))
        this.$emit('update:value', {
          curField:this.field,
          curVal:JSON.stringify(this.imageList)
        });
        
      } catch (err) {
        console.error('操作失败：', err)
        uni.showToast({
          title: err.message || '操作失败',
          icon: 'none'
        })
      } finally {
        this.uploading = false
      }
    },

    // 上传图片
    async uploadImage(filePath) {
      const data = {
        name: 'file',
        filePath: filePath
      }
      try {
        const res = await uploadImage(data)
        if (res.code === 200) {
          return {
            fileName: res.fileName,
            url: res.url
          }
        }
        throw new Error(res.msg || '上传失败')
      } catch (error) {
        console.error('上传失败:', error)
        throw new Error('图片上传失败')
      }
    },

    // 预览图片
    previewImage(index) {
      const urls = this.imageList.map(item => item.url)
      uni.previewImage({
        urls,
        current: index
      })
    },

    // 删除图片
    deleteImage(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这张图片吗？',
        success: (res) => {
          if (res.confirm) {
            this.imageList.splice(index, 1)
            //this.$emit('update:value', JSON.stringify(this.imageList))
            this.$emit('update:value', {
              curField:this.field,
              curVal:JSON.stringify(this.imageList)
            });
          }
        }
      })
    },

    // 处理图片加载错误
    handleImageError(index) {
      console.error(`图片加载失败，索引：${index}，URL：${this.imageList[index].url}`)
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss">
.upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20rpx;

  .upload-box {
    width: 280rpx;
    height: 250rpx;
    border: 1px dashed #ccc;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;

    &:active {
      background-color: #f5f5f5;
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .upload-icon {
      font-size: 45px;
      color: #8c939d;
    }

    .upload-text {
      font-size: 24rpx;
      color: #999;
      margin-top: 10rpx;
    }
  }

  .preview-box {
    width: 100%;
    
    .single-image {
      width: 100%;
      position: relative;
    }

    .multiple-images {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;
    }

    .image-item {
      position: relative;
      width: 100%;
    }

    .image-container {
      position: relative;
      width: 100%;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4rpx;
      overflow: hidden;
    }

    .uploaded-image {
      width: 100%;
      display: block;
    }

    .image-actions {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-around;
      background: rgba(0, 0, 0, 0.5);
      padding: 10rpx;
      opacity: 0;
      transition: opacity 0.3s;

      .image-item:hover & {
        opacity: 1;
      }

      .action-btn {
        color: #fff;
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
        cursor: pointer;

        &.preview {
          background-color: #409eff;
        }

        &.delete {
          background-color: #f56c6c;
        }

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .debug-info {
    margin-top: 20rpx;
    padding: 20rpx;
    background-color: #f5f5f5;
    border-radius: 4rpx;
    font-size: 24rpx;
    color: #666;
    word-break: break-all;
  }
}

/* 条件编译：小程序端样式 */
/* #ifdef MP */
.upload-container {
  .preview-box {
    .image-actions {
      opacity: 0;
      
      .image-container:active & {
        opacity: 1;
      }
    }
  }
}
/* #endif */
</style>