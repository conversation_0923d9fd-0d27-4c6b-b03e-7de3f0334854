<template>
  <view class="form-hand-sign">
    <!-- #ifdef VUE2 -->
    <jp-signature-popup
        ref="signaturePopup"
        :value="signatureValue"
        @input="onSignChange"
        :required="field.__config__.required"
        :buttonText="field.__config__.buttonText"
        :readonly="field.disabled"
        :showUpload="false"
        :showPreview="false"
    />
    <!-- #endif -->
  </view>
</template>

<script>
import { uploadMinio, getUserEsignUrl } from '@/api/system/user'
import { getInfo } from '@/api/login'

export default {
  name: 'FormHandSign',

  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },
  
  data(){
    return {
      previewUrl: '',
      signatureData: '',
      uploadTimer: null,
      uploading: false,
      esignUrl: '',
      currentValue: ''
    }
  },

  created() {
    // 初始化默认值，类似 FormSwitch
    this.currentValue = this.field.__config__?.defaultValue || ''
    console.log("this.currentValue", this.currentValue)
    
    // 如果没有默认值，尝试获取用户签名
    if (!this.currentValue) {
      this.getUserSignature()
    }
  },
  
  mounted() {
    console.log("FormHandSign mounted 触发")
    // 在mounted阶段再次发送初始值，确保父组件能接收到
    if (this.currentValue) {
      setTimeout(() => {
        console.log("在mounted中发送初始值:", this.currentValue)
        this.$emit('update:value', {
          curField: this.field,
          curVal: this.currentValue
        })
      }, 100) // 延迟100ms，确保父组件已准备好接收事件
    }
  },
  
  computed: {
    signatureValue() {
      // 直接使用内部状态 currentValue，类似 FormSwitch
      return this.currentValue ? String(this.currentValue) : ''
    }
  },

  methods: {
    async getUserSignature() {
      console.log("getUserSignature 开始执行")
      try {
        const res = await getInfo()
        console.log("getInfo 结果:", res)
        if (res && res.user && res.user.userId) {
          const userId = res.user.userId
          console.log("获取到用户ID:", userId)
          const response = await getUserEsignUrl(userId)
          console.log("getUserEsignUrl 结果:", response)
          
          if (response.data && response.data.esignUrl) {
            this.esignUrl = response.data.esignUrl
            this.currentValue = this.esignUrl
            
            // 使用setTimeout确保父组件已准备好接收事件
            setTimeout(() => {
              console.log("发送签名URL:", this.currentValue)
              this.$emit('update:value', {
                curField: this.field,
                curVal: this.currentValue
              })
            }, 100)
          }
        }
      } catch (e) {
        console.error('获取用户签名失败', e)
      }
    },
    
    onSignChange(signatureData) {
      console.log('签名数据变化:', signatureData)
      this.signatureData = signatureData
      
      // 如果签名数据为空，说明签名被删除，重新打开签名板
      if (!signatureData) {
        this.previewUrl = ''
        this.esignUrl = ''
        this.currentValue = '' // 更新内部状态
        this.$emit('update:value', {
          curField: this.field,
          curVal: ''
        })
        this.$refs.signaturePopup.open()
        return
      }
      
      // 清除之前的定时器
      if (this.uploadTimer) {
        clearTimeout(this.uploadTimer)
      }
      
      // 设置新的定时器，2秒后上传
      this.uploadTimer = setTimeout(() => {
        this.handleUpload()
      }, 1000)
    },

    async handleUpload() {
      if (!this.signatureData) {
        uni.showToast({title: '请先进行签名', icon: 'none'})
        return
      }

      if (this.uploading) return

      try {
        this.uploading = true
        
        // 检查文件是否存在
        await new Promise((resolve, reject) => {
          uni.getFileInfo({
            filePath: this.signatureData,
            success: resolve,
            fail: reject
          })
        })

        // 上传签名
        const res = await uploadMinio({
          name: 'file',
          filePath: this.signatureData,
          fileType: 'image' //  钉钉小程序上传必须加上fileType属性，钉钉小程序只支持image  video  audio
        })

        if (res.code === 200) {
          this.previewUrl = res.url
          this.currentValue = res.url // 更新内部状态
          
          // 只通过 update:value 事件更新数据，不直接修改 field 配置
          this.$emit('update:value', {
            curField: this.field,
            curVal: res.url
          })
          this.$refs.signaturePopup.close()

          uni.showToast({
            title: '签名上传成功',
            icon: 'success'
          })
        } else {
          throw new Error(res.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传失败:', error)
        // uni.showToast({
        //   title: error.message || '上传失败',
        //   icon: 'none'
        // })
      } finally {
        this.uploading = false
      }
    }
  },

  // 组件销毁时清除定时器
  beforeDestroy() {
    if (this.uploadTimer) {
      clearTimeout(this.uploadTimer)
    }
  }
}
</script>

<style lang="scss">
.form-hand-sign {
  .signature-preview {
    margin-top: 20rpx;
    
    .signature-image {
      width: 100%;
      height: 200rpx;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4rpx;
      object-fit: contain;
    }
  }
}
</style>