<template>
  <view class="form-switch">
    <!-- #ifdef APP-PLUS -->
    <view 
      class="custom-switch" 
      :class="[currentValue ? 'switch-active' : 'switch-inactive', field.disabled ? 'switch-disabled' : '']"
      @click="!field.disabled && toggleSwitch()"
    >
      <view class="switch-slider" :class="{ 'slider-active': currentValue }"></view>
    </view>
    <!-- #endif -->
    
    <!-- #ifndef APP-PLUS -->
    <switch
        :checked="currentValue"
        :disabled="field.disabled"
        :color="currentValue ? '#409eff' : '#dcdfe6'"
        @change="handleChange"
    />
    <!-- #endif -->
    
    <view class="switch-text">
      <text v-if="field['active-text'] && currentValue" class="active-text">
        {{ field['active-text'] }}
      </text>
      <text v-if="field['inactive-text'] && !currentValue" class="inactive-text">
        {{ field['inactive-text'] }}
      </text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormSwitch',

  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      currentValue: false
    }
  },

  created() {
    // 初始化默认值
      this.currentValue = this.field.__config__.defaultValue || false
      this.updateDefaultValue(this.currentValue)
  },

  methods: {
    handleChange(e) {
      if (this.field.disabled) return

      const checked = e.detail.value
      this.currentValue = checked

      //this.$emit('update:value', checked)
      this.$emit('update:value', {
        curField:this.field,
        curVal:checked
      });
    },
    updateDefaultValue(checked) {
      if (this.field.disabled)
          return
      //this.$emit('update:value', checked)
      this.$emit('update:value', {
        curField:this.field,
        curVal:checked
      });
    },
    toggleSwitch() {
      this.currentValue = !this.currentValue;
      this.$emit('update:value', {
        curField:this.field,
        curVal:this.currentValue
      });
    }
  }
}
</script>

<style lang="scss">
.form-switch {
  display: flex;
  align-items: center;

  .switch-text {
    margin-left: 10rpx;
    font-size: 28rpx;

    .active-text {
      color: #409eff;
    }

    .inactive-text {
      color: #909399;
    }
  }
}

/* #ifdef APP-PLUS */
.custom-switch {
  position: relative;
  width: 104rpx;
  height: 60rpx;
  border-radius: 30rpx;
  transition: all 0.3s;
  
  &.switch-active {
    background-color: #2196F3;
  }
  
  &.switch-inactive {
    background-color: #CCCCCC;
  }
  
  &.switch-disabled {
    opacity: 0.6;
  }
  
  .switch-slider {
    position: absolute;
    width: 52rpx;
    height: 52rpx;
    border-radius: 50%;
    background-color: #FFFFFF;
    top: 4rpx;
    left: 4rpx;
    transition: all 0.3s;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
    
    &.slider-active {
      transform: translateX(44rpx);
    }
  }
}
/* #endif */
</style>