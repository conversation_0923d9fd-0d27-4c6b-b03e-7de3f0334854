<template>
  <view class="checkbox-group-container">
    <!-- #ifdef APP-PLUS -->
    <view class="checkbox-list">
      <view
          v-for="(optItem, index) in options"
          :key="String(optItem.value)"
          class="checkbox-item"
          :class="{ 'disabled': field.disabled }"
          @tap="handleItemTap(optItem)"
      >
        <view class="checkbox-wrapper">
          <view class="checkbox" :class="{ 'checked': isSelected(optItem.value) }"></view>
        </view>
        <text class="checkbox-label">{{ optItem.label }}</text>
      </view>
    </view>
    <!-- #endif -->
    
    <!-- #ifndef APP-PLUS -->
    <checkbox-group
        v-model="selectedValues"
        :disabled="field.disabled"
        @change="handleChange"
    >
      <label
          class="uni-list-cell uni-list-cell-pd checkbox-group-item-vertical-direction"
          v-for="(optItem, index) in options"
          :key="String(optItem.value)"
      >
        <view>
          <checkbox
              :value="String(optItem.value)"
              :checked="isSelected(optItem.value)"
              :disabled="field.disabled"
          />
          <text>{{ optItem.label }}</text>
        </view>
      </label>
    </checkbox-group>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  name: 'FormCheckboxGroup',

  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      selectedValues: [],
      options: this.field.__slot__.options || []
    }
  },

  computed: {
    isSelected() {
      return (value) => {
        return this.selectedValues.includes(String(value));
      }
    }
  },

  created() {
    this.initSelectedValues();
  },

  methods: {
    initSelectedValues() {
      const defaultValue = this.field.__config__?.defaultValue;
      console.log("获取值=",defaultValue);
      console.log("获取类型=",typeof defaultValue);
      // 如果默认值是字符串，则转换为数组
      if (defaultValue) {
        this.selectedValues = typeof defaultValue === 'string' 
          ? defaultValue.split(',')
          : Array.isArray(defaultValue) ? defaultValue.map(String) : [String(defaultValue)];
      }
    },

    handleChange(e) {
      this.selectedValues = e.detail.value;
      console.log("值=",this.selectedValues);
      console.log("传递类型=",typeof this.selectedValues);
      // 将数组转换为逗号分隔的字符串
      const stringValue = this.selectedValues.join(',');
      console.log("转换后的值=",stringValue);
      //this.$emit('update:value', stringValue);
      this.$emit('update:value', {
        curField:this.field,
        curVal:stringValue
       });
    },

    handleItemTap(optItem) {
      if (this.field.disabled) return;
      
      const value = String(optItem.value);
      const index = this.selectedValues.indexOf(value);
      
      if (index === -1) {
        this.selectedValues.push(value);
      } else {
        this.selectedValues.splice(index, 1);
      }
      
      // 将数组转换为逗号分隔的字符串
      // const stringValue = this.selectedValues.join(',');
      //this.$emit('update:value', stringValue);
      this.$emit('update:value', {
        curField:this.field,
        curVal:this.selectedValues
      });
    }
  },

  watch: {
    'field.__config__.defaultValue': {
      handler(newVal) {
        this.initSelectedValues();
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss">
.checkbox-group-container {
  width: 100%;
}

.checkbox-list {
  width: 100%;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #ebeef5;
  background-color: #fff;
  
  &:active {
    background-color: #f5f7fa;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .checkbox-wrapper {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .checkbox {
      width: 36rpx;
      height: 36rpx;
      border: 2rpx solid #dcdfe6;
      border-radius: 4rpx;
      background-color: #fff;
      
      &.checked {
        background-color: #409EFF;
        border-color: #409EFF;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          left: 12rpx;
          top: 6rpx;
          width: 8rpx;
          height: 16rpx;
          border: 2rpx solid #fff;
          border-top: 0;
          border-left: 0;
          transform: rotate(45deg);
        }
      }
    }
  }

  .checkbox-label {
    margin-left: 20rpx;
    flex: 1;
    font-size: 28rpx;
    color: #333;
  }
}

.checkbox-group-item-vertical-direction {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  
  view {
    display: flex;
    align-items: center;
    
    checkbox {
      margin-right: 10rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style> 