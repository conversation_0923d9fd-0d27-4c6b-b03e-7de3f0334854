<template>
  <view class="price-container">
    <view class="price-input-section">
      <input
        class="uni-input"
        :value="inputValue"
        :disabled="field.disabled"
        :placeholder="field.placeholder"
        :type="field.type || 'text'"
        @input="handleInputChange"
      />
      <view v-if="showError" class="error-message">
        输入金额超出范围，请输入1-99999999之间的数字
      </view>
    </view>
    <view class="price-chinese-section">
      <view class="chinese-row">
        <view class="chinese-item chinese-item-wan">
          <input v-model="digits.wan" disabled class="digit-input" />
          <view class="unit-box">万</view>
        </view>
        <view class="chinese-item chinese-item-other">
          <input v-model="digits.qian" disabled class="digit-input" />
          <view class="unit-box">仟</view>
        </view>
        <view class="chinese-item chinese-item-other">
          <input v-model="digits.bai" disabled class="digit-input" />
          <view class="unit-box">佰</view>
        </view>
        <view class="chinese-item chinese-item-other">
          <input v-model="digits.shi" disabled class="digit-input" />
          <view class="unit-box">拾</view>
        </view>
        <view class="chinese-item chinese-item-other">
          <input v-model="digits.yuan" disabled class="digit-input" />
          <view class="unit-box">元</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormHandPrice',
  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isInternalUpdate: false,
      inputValue: '',
      priceValue: '',
      showError: false,
      digits: {
        wan: '零',
        qian: '零',
        bai: '零',
        shi: '零',
        yuan: '零'
      },
      chineseMap: {
        '0': '零',
        '1': '壹',
        '2': '贰',
        '3': '叁',
        '4': '肆',
        '5': '伍',
        '6': '陆',
        '7': '柒',
        '8': '捌',
        '9': '玖'
      }
    }
  },
  watch: {
    'field.__config__.defaultValue': {
      handler(newVal) {
        if (!this.isInternalUpdate && newVal) {
          try {
            let parsedValue = newVal
            if (typeof newVal === 'string') {
              parsedValue = JSON.parse(newVal)
            }

            this.priceValue = parsedValue.repaircost || ''
            this.inputValue = String(this.priceValue || '')
            
            this.digits = {
              wan: parsedValue.repaircostWan || '零',
              qian: parsedValue.repaircostQian || '零',
              bai: parsedValue.repaircostBai || '零',
              shi: parsedValue.repaircostShi || '零',
              yuan: parsedValue.repaircostGe || '零'
            }
          } catch (err) {
            console.warn('解析传入的 value 失败:', err)
            this.resetValues()
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleInputChange(event) {
      const val = event.detail.value || event.target.value
      const cleanValue = val.replace(/[^\d]/g, '')
      this.inputValue = cleanValue

      if (cleanValue === '') {
        this.priceValue = ''
        this.showError = false
        this.resetDigits()
        this.updateValue()
        return
      }

      const numValue = parseInt(cleanValue)

      if (numValue < 1 || numValue > 99999999) {
        this.showError = true
        this.inputValue = ''  // 清空输入栏
        this.priceValue = ''
        return
      }

      this.showError = false
      this.priceValue = numValue
      this.convertToChineseDigits(numValue)
      this.updateValue()
    },
    convertToChineseDigits(price) {
      if (!price) {
        this.resetDigits()
        return
      }

      const intPrice = Math.floor(price)
      let priceStr = String(intPrice)

      while (priceStr.length < 5) {
        priceStr = '0' + priceStr
      }

      let wanDigit = '零'
      if (priceStr.length > 5) {
        const wanPart = priceStr.slice(0, priceStr.length - 4)
        const wanNum = parseInt(wanPart)

        if (wanNum > 0) {
          const wanDigits = wanPart.split('').map(d => parseInt(d))
          const units = ['', '拾', '佰', '仟']
          wanDigit = ''
          let hasValue = false
          let lastNonZero = -1

          for (let i = wanDigits.length - 1; i >= 0; i--) {
            if (wanDigits[i] !== 0) {
              lastNonZero = i
              break
            }
          }

          for (let i = 0; i < wanDigits.length; i++) {
            const digit = wanDigits[i]
            if (digit === 0) {
              if (hasValue && i < lastNonZero) {
                wanDigit += '零'
                hasValue = false
              }
            } else {
              if (i === 1 && digit === 1 && wanDigits.length === 2) {
                wanDigit += '拾'
              } else {
                wanDigit += this.chineseMap[digit] + units[wanDigits.length - 1 - i]
              }
              hasValue = true
            }
          }

          if (!wanDigit) {
            wanDigit = '零'
          }
        }
      } else {
        wanDigit = this.chineseMap[priceStr[priceStr.length - 5]] || '零'
      }

      const lastFiveDigits = priceStr.slice(-5)

      this.digits = {
        wan: wanDigit,
        qian: this.chineseMap[lastFiveDigits[1]] || '零',
        bai: this.chineseMap[lastFiveDigits[2]] || '零',
        shi: this.chineseMap[lastFiveDigits[3]] || '零',
        yuan: this.chineseMap[lastFiveDigits[4]] || '零'
      }
    },
    resetDigits() {
      this.digits = {
        wan: '零',
        qian: '零',
        bai: '零',
        shi: '零',
        yuan: '零'
      }
    },
    resetValues() {
      this.inputValue = ''
      this.priceValue = ''
      this.showError = false
      this.resetDigits()
    },
    updateValue() {
      if (this.isInternalUpdate) return

      this.isInternalUpdate = true
      
      const value = {
        repaircost: this.priceValue,
        repaircostWan: this.digits.wan,
        repaircostQian: this.digits.qian,
        repaircostBai: this.digits.bai,
        repaircostShi: this.digits.shi,
        repaircostGe: this.digits.yuan
      }
      
      //this.$emit('update:value', JSON.stringify(value))
      this.$emit('update:value', {
        curField:this.field,
        curVal:JSON.stringify(value)
      });


      this.$nextTick(() => {
        this.isInternalUpdate = false
      })
    }
  }
}
</script>

<style lang="scss">
.price-container {
  width: 100%;
  padding: 20rpx;

  .price-input-section {
    margin-bottom: 30rpx;
    
    .uni-input {
      width: 100%;
      height: 70rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
      color: #606266;
      border: 1rpx solid #dcdfe6;
      border-radius: 4rpx;

      &[disabled] {
        background-color: #f5f7fa;
      }
    }

    .error-message {
      color: #F56C6C;
      font-size: 24rpx;
      margin-top: 10rpx;
      line-height: 1.2;
    }
  }

  .price-chinese-section {
    .chinese-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      justify-content: flex-start;
      align-items: center;

      .chinese-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        &.chinese-item-wan {
          min-width: 280rpx;
          flex: 2;

          @media screen and (max-width: 768px) {
            min-width: 100%;
            flex: 0 0 100%;
          }
        }

        &.chinese-item-other {
          min-width: 170rpx;
          flex: 1;

          @media screen and (max-width: 768px) {
            min-width: calc(50% - 8rpx);
            flex: 0 0 calc(50% - 8rpx);
          }
        }

        @media screen and (max-width: 480px) {
          min-width: 100% !important;
          flex: 0 0 100% !important;
        }

        .digit-input {
          flex: 1;
          height: 70rpx;
          text-align: center;
          border: 1rpx solid #dcdfe6;
          border-right: none;
          border-radius: 4rpx 0 0 4rpx;
          padding: 0 10rpx;
          font-size: 28rpx;
          color: #606266;
          background-color: #f5f7fa;
        }

        .unit-box {
          width: 64rpx;
          height: 70rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #F5F7FA;
          border: 1rpx solid #DCDFE6;
          border-left: none;
          color: #606266;
          font-size: 28rpx;
          border-radius: 0 4rpx 4rpx 0;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style> 