<template>
  <view class="material-container" :class="{ 'is-disabled': isDisabled }" :data-form-key="formKey">
    <!-- 区域一和区域二的容器 -->
    <view class="upper-section">
      <!-- 切换按钮 -->
      <view class="nav-tabs">
        <view
            class="tab-item"
            :class="{ active: currentTab === 0 }"
            @tap="switchTab(0)"
        >耗材类型</view>
        <view
            class="tab-item"
            :class="{ active: currentTab === 1 }"
            @tap="switchTab(1)"
        >耗材列表</view>
      </view>

      <!-- 滑动区域 -->
      <swiper
          class="swiper-content"
          :current="currentTab"
          @change="handleSwiperChange"
      >
        <!-- 区域一：耗材类型选择 -->
        <swiper-item>
          <view class="section-container">
            <view class="section-header">
              <view class="title">
                耗材类型
                <text class="mini-btn" :class="{ disabled: isDisabled }" @tap="handleAddType">+新增</text>
              </view>
              <view class="search-box">
                <input
                    type="text"
                    v-model="typeQueryParams.name"
                    placeholder="搜索类型"
                    :disabled="isDisabled"
                    @input="handleTypeSearch"
                />
                <text class="search-icon" @tap="handleTypeSearch">🔍</text>
              </view>
            </view>

            <scroll-view
                scroll-y
                class="scroll-area"
                @scrolltolower="handleTypeLoadMore"
            >
              <radio-group
                  @change="handleTypeChange"
                  :disabled="isDisabled"
                  class="type-radio-group"
              >
                <label
                    v-for="type in materialTypes"
                    :key="type.id"
                    class="radio-item"
                >
                  <radio
                      :value="type.id.toString()"
                      :checked="selectedMaterialTypeId === type.id"
                      :disabled="isDisabled"
                  />
                  <text>{{ type.name }}</text>
                </label>
              </radio-group>
            </scroll-view>
            <!-- 添加类型分页条 -->
            <view class="pagination">
              <text
                  class="page-btn"
                  :class="{ disabled: typeCurrentPage <= 1 }"
                  @tap="handleTypePrevPage"
              >上一页</text>
              <text class="page-info">{{ typeCurrentPage }}/{{ typeTotalPages }}</text>
              <text
                  class="page-btn"
                  :class="{ disabled: typeCurrentPage >= typeTotalPages }"
                  @tap="handleTypeNextPage"
              >下一页</text>
            </view>
          </view>
        </swiper-item>

        <!-- 区域二：耗材列表 -->
        <swiper-item>
          <view class="section-container">
            <view class="section-header">
              <view class="title">
                耗材列表
                <text class="mini-btn" :class="{ disabled: isDisabled }" @tap="handleAddMaterial">+新增</text>
              </view>
              <view class="search-box">
                <input
                    type="text"
                    v-model="queryParams.code"
                    placeholder="搜索编码"
                    :disabled="isDisabled"
                    @input="handleMaterialSearch"
                />
                <input
                    type="text"
                    v-model="queryParams.name"
                    placeholder="搜索名称"
                    :disabled="isDisabled"
                    @input="handleMaterialSearch"
                />
                <text class="search-icon" @tap="handleMaterialSearch">🔍</text>
              </view>
            </view>

            <scroll-view
                scroll-y
                class="scroll-area"
                @scrolltolower="handleMaterialLoadMore"
            >
              <!-- #ifdef APP-PLUS -->
              <view class="material-list">
                <view
                    v-for="item in materials"
                    :key="item.id"
                    class="material-item"
                    @tap="handleMaterialTap(item)"
                >
                  <view class="checkbox-wrapper">
                    <view class="checkbox" :class="{ 'checked': isSelected(item) }"></view>
                  </view>
                  <text class="material-info">
                    {{ item.code }} - {{ item.name }} - {{ item.unit }} - ¥{{ item.unitPrice }}
                  </text>
                </view>
              </view>
              <!-- #endif -->
              
              <!-- #ifndef APP-PLUS -->
              <checkbox-group @change="handleSelectionChange">
                <view
                    v-for="item in materials"
                    :key="item.id"
                    class="material-item"
                >
                  <checkbox
                      :value="item.id.toString()"
                      :checked="isSelected(item)"
                      :disabled="isDisabled"
                  />
                  <text class="material-info">
                    {{ item.code }} - {{ item.name }} - {{ item.unit }} - ¥{{ item.unitPrice }}
                  </text>
                </view>
              </checkbox-group>
              <!-- #endif -->
            </scroll-view>
            <!-- 添加材料分页条 -->
            <view class="pagination">
              <text
                  class="page-btn"
                  :class="{ disabled: materialCurrentPage <= 1 }"
                  @tap="handleMaterialPrevPage"
              >上一页</text>
              <text class="page-info">{{ materialCurrentPage }}/{{ materialTotalPages }}</text>
              <text
                  class="page-btn"
                  :class="{ disabled: materialCurrentPage >= materialTotalPages }"
                  @tap="handleMaterialNextPage"
              >下一页</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 区域三：已选耗材 -->
    <view class="selected-section">
      <view class="section-header">
        <view class="title-row">
          <text class="title">已选耗材</text>
          <text class="total-price">总价：¥{{ totalPrice }}</text>
        </view>
      </view>

      <scroll-view
          scroll-y
          class="scroll-area"
      >
        <view class="selected-table">
          <view class="table-header">
            <text class="th name">名称</text>
            <text class="th unit">单位</text>
            <text class="th price">单价</text>
            <text class="th quantity">数量</text>
            <text class="th action">操作</text>
          </view>

          <view class="table-body">
            <view
                v-for="(item, index) in selectedMaterials"
                :key="index"
                class="table-row"
            >
              <text class="td name">{{ item.name }}</text>
              <text class="td unit">{{ item.unit }}</text>
              <text class="td price">¥{{ item.unitPrice }}</text>
              <view class="td quantity">
                <view class="number-input">
                  <text
                      class="control-btn"
                      :class="{ 'is-disabled': isDisabled }"
                      @tap="isDisabled ? null : decreaseQuantity(item)"
                  >-</text>
                  <input
                      type="number"
                      v-model="item.quantity"
                      class="quantity-input"
                      :disabled="isDisabled"
                  />
                  <text
                      class="control-btn"
                      :class="{ 'is-disabled': isDisabled }"
                      @tap="isDisabled ? null : increaseQuantity(item)"
                  >+</text>
                </view>
              </view>
              <text
                  class="td action delete-btn"
                  :class="{ 'is-disabled': isDisabled }"
                  @tap="isDisabled ? null : handleDelete(index)"
              >×</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 新增耗材类型弹窗 -->
    <uni-popup ref="typePopup" type="center">
      <view class="popup-content">
        <view class="popup-title">新增耗材类型</view>
        <uni-forms ref="typeForm" :model="typeForm" :rules="typeRules">
          <uni-forms-item label="类型名称" name="name">
            <uni-easyinput
                v-model="typeForm.name"
                placeholder="请输入耗材类型名称"
            />
          </uni-forms-item>
          <uni-forms-item label="类型描述" name="description">
            <uni-easyinput
                v-model="typeForm.description"
                placeholder="请输入耗材类型描述"
            />
          </uni-forms-item>
        </uni-forms>
        <view class="popup-buttons">
          <button class="btn cancel" @tap="cancelTypeDialog">取消</button>
          <button class="btn confirm" @tap="submitTypeForm">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 新增耗材弹窗 -->
    <uni-popup ref="materialPopup" type="center">
      <view class="popup-content">
        <view class="popup-title">新增耗材</view>
        <uni-forms ref="materialForm" :model="materialForm" :rules="materialRules">
          <uni-forms-item label="耗材编码" name="code">
            <uni-easyinput
                v-model="materialForm.code"
                placeholder="请输入耗材编码"
            />
          </uni-forms-item>
          <uni-forms-item label="耗材名称" name="name">
            <uni-easyinput
                v-model="materialForm.name"
                placeholder="请输入耗材名称"
            />
          </uni-forms-item>
          <uni-forms-item label="单位" name="unit">
            <uni-easyinput
                v-model="materialForm.unit"
                placeholder="请输入单位"
            />
          </uni-forms-item>
          <uni-forms-item label="单价" name="unitPrice">
            <uni-easyinput
                v-model="materialForm.unitPrice"
                type="number"
                placeholder="请输入单价"
            />
          </uni-forms-item>
          <uni-forms-item label="所属类型">
            <uni-easyinput
                :value="selectedTypeInfo.name"
                disabled
            />
          </uni-forms-item>
        </uni-forms>
        <view class="popup-buttons">
          <button class="btn cancel" @tap="cancelMaterialDialog">取消</button>
          <button class="btn confirm" @tap="submitMaterialForm">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { listRepairMaterialTypes, listRepairMaterials, addRepairMaterialTypes, addRepairMaterials } from '@/api/repair.js'
import { deepClone } from '@/utils/comUtil'

export default {
  name: 'FormMaterialSelect',
  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      materialTypes: [],
      selectedMaterialTypeId: null,
      materials: [],
      selectedMaterials: [],
      isInternalUpdate: false,
      originalMaterialTypes: [], // 保存原始耗材类型数据
      originalMaterials: [], // 保存原始耗材数据
      typeCurrentPage: 1,
      typeTotalPages: 1,
      typePageSize: 10,
      materialCurrentPage: 1,
      materialTotalPages: 1,
      materialPageSize: 10,
      value: this.field.__config__?.defaultValue || {},
      typeQueryParams: {
        pageNum: 1,
        pageSize: 10,
        name: '', // 添加类型搜索关键词
      },
      queryParams: {
        code: '',
        name: '',
        pageNum: 1,
        pageSize: 10,
        typeId: '', // 添加类型ID
      },
      typesTotal: 0,
      total: 0,
      currentTab: 0, // 当前激活的标签页

      // 新增类型表单数据
      typeForm: {
        name: '',
        description: ''
      },
      // 类型表单校验规则
      typeRules: {
        name: {
          rules: [
            { required: true, errorMessage: '请输入耗材类型名称' },
            { maxLength: 20, errorMessage: '类型名称不能超过20个字符' }
          ]
        },
        description: {
          rules: [
            { required: true, errorMessage: '请输入耗材类型描述' },
            { maxLength: 100, errorMessage: '类型描述不能超过100个字符' }
          ]
        }
      },

      // 新增耗材表单数据
      materialForm: {
        code: '',
        name: '',
        unit: '',
        unitPrice: '',
        typeId: '',
        typeName: '',
        imgUrl: ''
      },
      // 耗材表单校验规则
      materialRules: {
        code: {
          rules: [
            { required: true, errorMessage: '请输入耗材编码' },
            { maxLength: 20, errorMessage: '编码不能超过20个字符' }
          ]
        },
        name: {
          rules: [
            { required: true, errorMessage: '请输入耗材名称' },
            { maxLength: 20, errorMessage: '名称不能超过20个字符' }
          ]
        },
        unit: {
          rules: [
            { required: true, errorMessage: '请输入单位' },
            { maxLength: 20, errorMessage: '单位不能超过20个字符' }
          ]
        },
        unitPrice: {
          rules: [
            { required: true, errorMessage: '请输入单价' },
            {
              validateFunction: (rule, value, data, callback) => {
                if (value <= 0) {
                  callback('单价必须大于0');
                }
                return true;
              }
            }
          ]
        }
      }
    }
  },
  computed: {
    totalPrice() {
      return this.selectedMaterials.reduce((sum, item) => {
        return sum + (Number(item.unitPrice) * Number(item.quantity));
      }, 0).toFixed(2);
    },
    filteredMaterialTypes() {
      if (!this.typeQueryParams.name) return this.materialTypes;
      return this.materialTypes.filter(type =>
          type.name.toLowerCase().includes(this.typeQueryParams.name.toLowerCase())
      );
    },
    filteredMaterials() {
      if (!this.queryParams.name) return this.materials;
      const searchTerm = this.queryParams.name.toLowerCase();
      return this.materials.filter(material =>
          material.code.toLowerCase().includes(searchTerm) ||
          material.name.toLowerCase().includes(searchTerm) ||
          material.typeId.toString().includes(searchTerm)
      );
    },
    // 获取当前表单的唯一标识
    formKey() {
      return this.field.__vModel__ || '';
    },
    // 从field和field.__config__中获取禁用状态
    isDisabled() {
      return this.field.disabled === true || (this.field.__config__ && this.field.__config__.disabled === true);
    },
    // 获取当前选中的耗材类型信息
    selectedTypeInfo() {
      if (!this.selectedMaterialTypeId) return { id: null, name: '' };
      const selectedType = this.materialTypes.find(type => type.id === this.selectedMaterialTypeId);
      return selectedType || { id: null, name: '' };
    }
  },
  watch: {
    selectedMaterials: {
      handler(newVal) {
        this.updateValue();
      },
      deep: true
    },
    // 监听 field 中的默认值变化
    'field.__config__.defaultValue': {
      handler(newVal) {
        if (!this.isInternalUpdate && newVal) {
          try {
            let parsedValue = newVal;
            if (typeof newVal === 'string') {
              parsedValue = JSON.parse(newVal);
            }
            if (parsedValue && parsedValue.materials) {
              // 使用深拷贝更新数据
              this.selectedMaterials = deepClone(parsedValue.materials);
            }
          } catch (e) {
            console.warn('解析默认值失败:', e);
          }
        }
      },
      immediate: true,
      deep: true
    },
    // 监听field变化以更新禁用状态
    'field.disabled': {
      handler(newVal) {
        if (newVal === true) {
          // 当field被禁用时，可以在这里添加一些额外的处理
          console.log('Field disabled state changed:', newVal);
        }
      },
      immediate: true
    },
    'field.__config__.disabled': {
      handler(newVal) {
        if (newVal === true) {
          // 当config被禁用时，可以在这里添加一些额外的处理
          console.log('Config disabled state changed:', newVal);
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.loadMaterialTypes();
  },
  methods: {
    async loadMaterialTypes() {
      try {
        const res = await listRepairMaterialTypes({
          ...this.typeQueryParams
        });
        this.materialTypes = res.rows;
        this.originalMaterialTypes = [...res.rows];
        this.typeTotalPages = Math.ceil(res.total / this.typePageSize);
        this.typesTotal = res.total;

        if (this.materialTypes.length > 0 && !this.selectedMaterialTypeId) {
          this.selectedMaterialTypeId = this.materialTypes[0].id;
          this.loadMaterials();
        }
      } catch (error) {
        console.error("加载耗材类型失败:", error);
      }
    },
    async loadMaterials() {
      if (!this.selectedMaterialTypeId) return;
      try {
        const res = await listRepairMaterials({
          ...this.queryParams,
          typeId: this.selectedMaterialTypeId
        });
        this.materials = res.rows;
        this.originalMaterials = [...res.rows];
        this.materialTotalPages = Math.ceil(res.total / this.materialPageSize);
        this.total = res.total;
        this.syncSelectedState();
      } catch (error) {
        console.error("加载耗材失败:", error);
      }
    },
    handleMaterialSearch() {
      this.queryParams.pageNum = 1; // 重置页码
      this.loadMaterials();
    },
    handleTypeChange(e) {
      this.selectedMaterialTypeId = Number(e.detail.value);
      this.materialCurrentPage = 1; // 重置耗材列表页码
      this.loadMaterials();
    },
    isSelected(item) {
      return this.selectedMaterials.some(selected => selected.id === item.id);
    },
    handleSelectionChange(e) {
      if (this.isDisabled) return;

      const selectedIds = e.detail.value;
      // 深拷贝当前选中的材料
      let updatedSelectedMaterials = deepClone(this.selectedMaterials);

      // 处理新选中的材料
      selectedIds.forEach(id => {
        const numId = parseInt(id);
        const material = this.materials.find(m => m.id === numId);
        if (material && !this.isSelected(material)) {
          // 深拷贝新选中的材料
          updatedSelectedMaterials.push(deepClone({
            ...material,
            quantity: 1
          }));
        }
      });

      // 处理取消选中的材料
      this.materials.forEach(material => {
        if (!selectedIds.includes(material.id.toString()) && this.isSelected(material)) {
          updatedSelectedMaterials = updatedSelectedMaterials.filter(
              selected => selected.id !== material.id
          );
        }
      });

      // 更新选中材料
      this.selectedMaterials = deepClone(updatedSelectedMaterials);
      this.updateValue();
    },
    handleDelete(index) {
      if (this.isDisabled) return;

      // 深拷贝并更新数据
      const materials = deepClone(this.selectedMaterials);
      materials.splice(index, 1);
      this.selectedMaterials = materials;
      this.updateValue();
    },
    increaseQuantity(item) {
      if (this.isDisabled) return;

      // 深拷贝并更新数据
      const materials = deepClone(this.selectedMaterials);
      const targetItem = materials.find(m => m.id === item.id);
      if (targetItem) {
        targetItem.quantity = (targetItem.quantity || 0) + 1;
        this.selectedMaterials = materials;
        this.updateValue();
      }
    },
    decreaseQuantity(item) {
      if (this.isDisabled) return;

      // 深拷贝并更新数据
      const materials = deepClone(this.selectedMaterials);
      const targetItem = materials.find(m => m.id === item.id);
      if (targetItem && targetItem.quantity > 1) {
        targetItem.quantity--;
        this.selectedMaterials = materials;
        this.updateValue();
      }
    },
    syncSelectedState() {
      this.materials.forEach(material => {
        material.selected = this.selectedMaterials.some(selected => selected.id === material.id);
      });
    },
    updateValue() {
      if (this.isInternalUpdate) return;

      this.isInternalUpdate = true;
      try {
        // 深拷贝要发送的数据
        const value = {
          materials: deepClone(this.selectedMaterials),
          totalPrice: this.totalPrice
        };

       // this.$emit('update:value', JSON.stringify(value));
        this.$emit('update:value', {
          curField:this.field,
          curVal:JSON.stringify(value)
        });
      } finally {
        setTimeout(() => {
          this.isInternalUpdate = false;
        }, 0);
      }
    },
    handleAddType() {
      this.typeForm = {
        name: '',
        description: ''
      };
      setTimeout(() => {
        this.$refs.typeForm && this.$refs.typeForm.resetFields();
        this.$refs.typePopup.open();
      }, 100);
    },
    handleAddMaterial() {
      if (!this.selectedMaterialTypeId) {
        uni.showToast({
          title: '请先选择一个耗材类型',
          icon: 'none'
        });
        return;
      }

      // 使用计算属性获取当前选中的类型信息
      const selectedType = this.selectedTypeInfo;
      if (!selectedType.id) {
        uni.showToast({
          title: '无法获取当前选中的耗材类型信息',
          icon: 'none'
        });
        return;
      }

      this.materialForm = {
        code: '',
        name: '',
        unit: '',
        unitPrice: '',
        typeId: selectedType.id,
        typeName: selectedType.name,
        imgUrl: ''
      };

      setTimeout(() => {
        this.$refs.materialForm && this.$refs.materialForm.resetFields();
        // 强制更新视图
        this.$forceUpdate();
        this.$refs.materialPopup.open();
      }, 100);
    },
    // 切换标签页
    switchTab(index) {
      this.currentTab = index;
    },

    // 处理滑动切换
    handleSwiperChange(e) {
      this.currentTab = e.detail.current;
    },
    // 处理类型列表加载更多
    handleTypeLoadMore() {
      if (this.typeQueryParams.pageNum < Math.ceil(this.typesTotal / this.typeQueryParams.pageSize)) {
        this.typeQueryParams.pageNum++;
        this.loadMaterialTypes();
      }
    },

    // 处理材料列表加载更多
    handleMaterialLoadMore() {
      if (this.queryParams.pageNum < Math.ceil(this.total / this.queryParams.pageSize)) {
        this.queryParams.pageNum++;
        this.loadMaterials();
      }
    },
    handleTypeSearch() {
      this.typeQueryParams.pageNum = 1; // 重置页码
      this.loadMaterialTypes();
    },
    // 处理类型列表上一页
    handleTypePrevPage() {
      if (this.typeCurrentPage > 1) {
        this.typeCurrentPage--;
        this.typeQueryParams.pageNum = this.typeCurrentPage;
        this.loadMaterialTypes();
      }
    },
    // 处理类型列表下一页
    handleTypeNextPage() {
      if (this.typeCurrentPage < this.typeTotalPages) {
        this.typeCurrentPage++;
        this.typeQueryParams.pageNum = this.typeCurrentPage;
        this.loadMaterialTypes();
      }
    },
    // 处理材料列表上一页
    handleMaterialPrevPage() {
      if (this.materialCurrentPage > 1) {
        this.materialCurrentPage--;
        this.queryParams.pageNum = this.materialCurrentPage;
        this.loadMaterials();
      }
    },
    // 处理材料列表下一页
    handleMaterialNextPage() {
      if (this.materialCurrentPage < this.materialTotalPages) {
        this.materialCurrentPage++;
        this.queryParams.pageNum = this.materialCurrentPage;
        this.loadMaterials();
      }
    },
    // 提交新增耗材类型
    async submitTypeForm() {
      try {
        await this.$refs.typeForm.validate();

        uni.showLoading({
          title: '正在提交...',
          mask: true
        });

        const response = await addRepairMaterialTypes(this.typeForm);

        uni.hideLoading();

        if (response.code === 200) {
          uni.showToast({
            title: '耗材类型新增成功',
            icon: 'success'
          });

          this.$refs.typePopup.close();
          await this.loadMaterialTypes();

          // 选择新创建的类型
          if (response.data && response.data.id) {
            this.selectedMaterialTypeId = response.data.id;
            this.handleTypeChange(response.data.id);
          }
        } else {
          uni.showToast({
            title: response.msg || '耗材类型新增失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '耗材类型新增失败',
          icon: 'none'
        });
        console.error('耗材类型新增失败:', error);
      }
    },
    // 取消新增耗材类型
    cancelTypeDialog() {
      this.$refs.typePopup.close();
    },
    // 提交新增耗材
    async submitMaterialForm() {
      try {
        await this.$refs.materialForm.validate();

        uni.showLoading({
          title: '正在提交...',
          mask: true
        });

        const submitData = {
          ...this.materialForm,
          typeId: this.selectedTypeInfo.id,
          typeName: this.selectedTypeInfo.name
        };

        const response = await addRepairMaterials(submitData);

        uni.hideLoading();

        if (response.code === 200) {
          uni.showToast({
            title: '耗材新增成功',
            icon: 'success'
          });

          this.$refs.materialPopup.close();
          await this.loadMaterials();
        } else {
          uni.showToast({
            title: response.msg || '耗材新增失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '耗材新增失败',
          icon: 'none'
        });
        console.error('耗材新增失败:', error);
      }
    },
    // 取消新增耗材
    cancelMaterialDialog() {
      this.$refs.materialPopup.close();
    },
    // 初始化组件数据
    initData() {
      const defaultValue = this.field.__config__?.defaultValue;
      if (defaultValue) {
        try {
          let parsedValue = defaultValue;
          if (typeof defaultValue === 'string') {
            parsedValue = JSON.parse(defaultValue);
          }
          if (parsedValue && parsedValue.materials) {
            // 使用深拷贝初始化数据
            this.selectedMaterials = deepClone(parsedValue.materials);
          }
        } catch (e) {
          console.warn('解析默认值失败:', e);
        }
      }
    },
    handleMaterialTap(item) {
      if (this.isDisabled) return;
      
      // 深拷贝当前选中的材料
      let updatedSelectedMaterials = deepClone(this.selectedMaterials);
      
      if (this.isSelected(item)) {
        // 如果已经选中，则移除
        updatedSelectedMaterials = updatedSelectedMaterials.filter(
          selected => selected.id !== item.id
        );
      } else {
        // 如果未选中，则添加
        updatedSelectedMaterials.push(deepClone({
          ...item,
          quantity: 1
        }));
      }
      
      // 更新选中材料
      this.selectedMaterials = deepClone(updatedSelectedMaterials);
      this.updateValue();
    }
  },
  created() {
    this.initData();
  }
}
</script>

<style lang="scss">
.material-container {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .upper-section {
    flex: 1;
    min-height: 0; // 重要：允许flex子项收缩

    .swiper-content {
      height: calc(100% - 80rpx); // 减去导航标签的高度
    }
  }

  .section-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .section-header {
      padding: 20rpx;
    }

    .scroll-area {
      flex: 1;
      height: 0; // 重要：配合flex: 1使用
    }

    // 添加分页条样式
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20rpx;
      background-color: #fff;
      border-top: 2rpx solid #ebeef5;

      .page-btn {
        padding: 10rpx 30rpx;
        font-size: 24rpx;
        color: #409eff;
        background: rgba(64, 158, 255, 0.1);
        border-radius: 4rpx;
        margin: 0 20rpx;

        &.disabled {
          color: #c0c4cc;
          background: #f5f7fa;
          cursor: not-allowed;
        }
      }

      .page-info {
        font-size: 24rpx;
        color: #606266;
      }
    }
  }

  .selected-section {
    height: 40vh; // 可以调整这个值来改变已选区域的高度
    display: flex;
    flex-direction: column;
    border-top: 2rpx solid #ebeef5;

    .section-header {
      padding: 20rpx;
    }

    .scroll-area {
      flex: 1;
      height: 0; // 重要：配合flex: 1使用
    }
  }

  &.is-disabled {
    .control-btn,
    .delete-btn,
    .search-btn,
    .mini-btn {
      opacity: 0.6;
      pointer-events: none !important;
      cursor: not-allowed;
    }

    input[disabled],
    checkbox[disabled],
    radio[disabled] {
      opacity: 0.6;
      background-color: #f5f5f5;
    }

    .number-input {
      .control-btn.is-disabled {
        background-color: #f5f5f5;
        color: #c0c4cc;
      }
    }
  }
}

.nav-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #ebeef5;
  margin-bottom: 20rpx;

  .tab-item {
    flex: 1;
    text-align: center;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 28rpx;
    color: #606266;
    position: relative;

    &.active {
      color: #409eff;
      font-weight: bold;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40%;
        height: 4rpx;
        background-color: #409eff;
      }
    }
  }
}

.mini-btn {
  display: inline-block;
  font-size: 24rpx;
  color: #409eff;
  margin-left: 16rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  background: rgba(64, 158, 255, 0.1);

  &.disabled {
    color: #a0cfff;
    background: rgba(160, 207, 255, 0.1);
  }
}

.search-box {
  position: relative;
  margin: 20rpx 0;
  display: flex;
  gap: 10rpx;

  input {
    flex: 1;
    height: 64rpx;
    padding: 0 20rpx;
    border-radius: 32rpx;
    background: #f5f7fa;
    font-size: 28rpx;
    border: 2rpx solid #dcdfe6;

    &:focus {
      border-color: #409eff;
    }
  }

  .search-icon {
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 32rpx;
    color: #909399;
    padding: 0 20rpx;
  }
}

.selected-table {
  .table-header {
    display: flex;
    background-color: #f5f7fa;
    font-size: 24rpx;
    color: #909399;
    padding: 16rpx 0;

    .th {
      text-align: center;

      &.name {
        flex: 2;
      }

      &.unit {
        flex: 1;
      }

      &.price {
        flex: 1;
      }

      &.quantity {
        flex: 1;
      }

      &.action {
        width: 80rpx;
      }
    }
  }

  .table-body {
    .table-row {
      display: flex;
      align-items: center;
      padding: 16rpx 0;
      border-bottom: 2rpx solid #ebeef5;
      font-size: 24rpx;

      .td {
        text-align: center;

        &.name {
          flex: 2;
        }

        &.unit {
          flex: 1;
        }

        &.price {
          flex: 1;
        }

        &.quantity {
          flex: 1;
        }

        &.action {
          width: 80rpx;
          color: #f56c6c;
          font-size: 32rpx;
        }
      }

      .number-input {
        display: inline-flex;
        align-items: center;
        border: 2rpx solid #dcdfe6;
        border-radius: 6rpx;
        width: 160rpx;
        height: 48rpx;

        .control-btn {
          width: 48rpx;
          height: 48rpx;
          line-height: 48rpx;
          text-align: center;
          background: #f5f7fa;
          color: #606266;
          font-size: 24rpx;

          &.minus {
            border-right: 2rpx solid #dcdfe6;
          }

          &.plus {
            border-left: 2rpx solid #dcdfe6;
          }

          &:active {
            background: #e4e7ed;
          }
        }

        .quantity-input {
          flex: 1;
          height: 100%;
          text-align: center;
          font-size: 24rpx;
          min-height: 0; // 避免输入框最小高度限制
        }
      }
    }
  }
}

.material-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #ebeef5;

  .material-info {
    margin-left: 20rpx;
    flex: 1;
    font-size: 28rpx;
  }
}

// 弹窗样式
.popup-content {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  width: 600rpx;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .popup-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 40rpx;

    .btn {
      flex: 1;
      margin: 0 20rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 40rpx;
      font-size: 28rpx;

      &.cancel {
        background: #f5f7fa;
        color: #909399;
      }

      &.confirm {
        background: #409eff;
        color: #fff;
      }
    }
  }
}

.material-list {
  width: 100%;
}

.material-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #ebeef5;
  background-color: #fff;
  
  &:active {
    background-color: #f5f7fa;
  }

  .checkbox-wrapper {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .checkbox {
      width: 36rpx;
      height: 36rpx;
      border: 2rpx solid #dcdfe6;
      border-radius: 4rpx;
      background-color: #fff;
      
      &.checked {
        background-color: #409EFF;
        border-color: #409EFF;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          left: 12rpx;
          top: 6rpx;
          width: 8rpx;
          height: 16rpx;
          border: 2rpx solid #fff;
          border-top: 0;
          border-left: 0;
          transform: rotate(45deg);
        }
      }
    }
  }

  .material-info {
    margin-left: 20rpx;
    flex: 1;
    font-size: 28rpx;
    color: #333;
  }
}
</style>