<template>
  <view class="form-hrp-material-select">
    <HrpMaterialSelect
      @materials-changed="handleMaterialsChanged"
      ref="hrpMaterialSelectRef"
      :disabled="isDisabled"
    />
  </view>
</template>

<script>
import HrpMaterialSelect from './HrpMaterialSelect.vue'

export default {
  name: 'FormHrpMaterialSelect',
  components: {
    HrpMaterialSelect: HrpMaterialSelect
  },
  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isDisabled: false
    }
  },
  created() {
    console.log('FormHrpMaterialSelect created - field:', this.field)
    console.log('FormHrpMaterialSelect created - formData:', this.formData)
  },
  watch: {
    field: {
      handler(newVal) {
        console.log('FormHrpMaterialSelect - field prop changed:', newVal)
      },
      deep: true
    },
    formData: {
      handler(newVal) {
        console.log('FormHrpMaterialSelect - formData prop changed:', newVal)
      },
      deep: true
    }
  },
  mounted() {
    console.log('FormHrpMaterialSelect mounted - field:', this.field)
    console.log('FormHrpMaterialSelect mounted - formData:', this.formData)
    
    // 如果表单数据中已经存在该字段值，则需要初始化组件状态
    const fieldKey = this.field?.__vModel__
    if (!fieldKey) {
      console.warn('FormHrpMaterialSelect - field.__vModel__ is undefined:', this.field)
      return
    }

    // 检查是否有默认值
    if (this.field.__config__?.defaultValue) {
      let defaultData
      try {
        // 尝试解析默认值，可能是字符串也可能是对象
        defaultData = typeof this.field.__config__.defaultValue === 'string'
          ? JSON.parse(this.field.__config__.defaultValue)
          : this.field.__config__.defaultValue

        console.log('Using default value:', defaultData)
        
        // 检查是否为非空数据（不是空数组且总价不为0）
        if (defaultData.hrpMats && 
            Array.isArray(defaultData.hrpMats) && 
            defaultData.hrpMats.length > 0 && 
            defaultData.totalPrice > 0) {
          this.isDisabled = true
          setTimeout(() => {
            this.$refs.hrpMaterialSelectRef.userSelectedMaterials = [...defaultData.hrpMats]
          }, 100)
        } else {
          console.log('Skipping empty default value')
        }
      } catch (error) {
        console.error('Error parsing default value:', error)
      }
    } else if (this.formData[fieldKey] && this.formData[fieldKey].hrpMats && Array.isArray(this.formData[fieldKey].hrpMats)) {
      // 如果没有默认值，则使用原来的逻辑
      setTimeout(() => {
        this.$refs.hrpMaterialSelectRef.userSelectedMaterials = [...this.formData[fieldKey].hrpMats]
      }, 100)
    }
  },
  methods: {
    handleMaterialsChanged(data) {
      // 当材料选择变化时，更新表单数据
      const fieldKey = this.field.__vModel__
      
      // 格式化数据并转换为JSON字符串
      const formattedData = JSON.stringify({
        hrpMats: data.hrpMats,
        totalPrice: Number((data.totalPrice || 0).toFixed(2))
      });
      
      console.log('FormHrpMaterialSelect - formattedData type:', typeof formattedData);
      console.log('FormHrpMaterialSelect - formattedData:', formattedData);
      
      // 更新表单数据
      this.$set(this.formData, fieldKey, formattedData)
      
      // 触发更新事件
      this.$emit('update:value', {
        curField: this.field,
        curVal: formattedData
      })
    }
  }
}
</script>

<style lang="scss">
.form-hrp-material-select {
  width: 100%;
}
</style> 