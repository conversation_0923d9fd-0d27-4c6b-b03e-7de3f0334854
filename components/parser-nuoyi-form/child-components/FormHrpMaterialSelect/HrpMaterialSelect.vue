<template>
  <view class="page">
    <view class="header">
      <view class="add-btn" 
        :class="{ 'disabled': disabled }"
        @click="handleAddClick">
        <text class="btn-text">添加耗材</text>
      </view>
    </view>

    <!-- 已选仓库和分类信息展示 -->
    <view class="selector-display" v-if="currentWarehouseName">
      <view class="selector-info">
        <view class="selector-item">
          <text class="label">当前仓库：</text>
          <text class="value">{{currentWarehouseName || '请选择仓库'}}</text>
        </view>
        <view class="selector-item" v-if="currentFirstLevelName">
          <text class="label">当前分类：</text>
          <text class="value">
            {{currentFirstLevelName}}
            <text v-if="currentSecondLevelName"> > {{currentSecondLevelName}}</text>
            <text v-if="currentThirdLevelName"> > {{currentThirdLevelName}}</text>
          </text>
        </view>
      </view>
    </view>

    <!-- 已选耗材展示区域 -->
    <view class="selected-materials-container" v-if="userSelectedMaterials.length > 0">
      <view class="section-title">已选耗材</view>
      <view class="cards-container">
        <template v-if="paginatedMaterials.length > 0">
          <view v-for="(item, index) in paginatedMaterials" :key="index" class="material-card">
            <view class="card-header">
              <text class="card-title">{{item.productName}}</text>
              <text class="delete-btn" 
                :class="{ 'disabled': disabled }"
                @click="handleDeleteClick(index)">删除</text>
            </view>
            <view class="card-content">
              <view class="info-row">
                <text class="label">规格：</text>
                <text class="value">{{item.productSpec}}</text>
              </view>
              <view class="info-row">
                <text class="label">单价：</text>
                <text class="value">¥{{item.productPrice}}</text>
              </view>
              <view class="info-row">
                <text class="label">数量：</text>
                <text class="value">{{item.totalQuantity}}</text>
              </view>
              <view class="info-row total-row">
                <text class="label">小计：</text>
                <text class="value">¥{{(item.productPrice * item.totalQuantity).toFixed(2)}}</text>
              </view>
            </view>
          </view>
        </template>
      </view>
      <view class="footer-container">
        <view class="total-price">
          <text class="total">总价：¥{{userSelectedTotalPrice}}</text>
        </view>
        <view class="pagination-control">
          <view class="pagination-buttons">
            <text
              class="pagination-btn prev"
              :class="{ disabled: currentPage <= 1 }"
              @click="changePage(currentPage - 1)"
            >&lt;</text>
            <view class="pagination-pages">
              <text
                v-if="currentPage > 3"
                class="pagination-page"
                @click="changePage(1)"
              >1</text>
              <text v-if="currentPage > 3" class="pagination-ellipsis">...</text>
              <text
                v-for="page in 5"
                :key="page"
                v-if="currentPage - 3 + page > 0 && currentPage - 3 + page <= totalPages"
                :class="['pagination-page', { active: currentPage === currentPage - 3 + page }]"
                @click="changePage(currentPage - 3 + page)"
              >{{currentPage - 3 + page}}</text>
              <text v-if="currentPage < totalPages - 2" class="pagination-ellipsis">...</text>
              <text
                v-if="currentPage < totalPages - 2"
                class="pagination-page"
                @click="changePage(totalPages)"
              >{{totalPages}}</text>
            </view>
            <text
              class="pagination-btn next"
              :class="{ disabled: currentPage >= totalPages }"
              @click="changePage(currentPage + 1)"
            >&gt;</text>
          </view>
          <view class="pagination-info">
            <text>{{currentPage}}/{{totalPages}}页，共{{userSelectedMaterials.length}}条</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 暂无已选耗材提示 -->
    <view class="empty-tip" v-else>
      <text>暂无已选耗材，请点击"添加耗材"按钮进行添加</text>
    </view>

    <!-- 仓库和分类选择弹窗 -->
    <uni-popup ref="selectorPopup" type="center">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择分类</text>
          <text class="popup-close" @click="closeSelectorPopup">×</text>
        </view>

        <view class="popup-body">
          <!-- 原来的仓库和分类选择器 -->
          <view class="selector-container">
                          <view class="warehouse-selector">
              <text class="label">选择仓库：</text>
              <view class="select-wrapper">
                <uni-data-select
                    v-model="selectedWarehouse"
                    :localdata="warehouseOptions"
                    :clear="false"
                    @change="handleWarehouseChange"
                    placeholder="请选择仓库"
                ></uni-data-select>
              </view>
            </view>

            <view class="department-selector" v-if="departmentTree.length > 0">
              <text class="label">选择分类：</text>
              <view class="sort-tree">
                <!-- 一级分类 -->
                <view class="select-wrapper">
                  <uni-data-select
                      v-model="selectedFirstLevel"
                      :localdata="firstLevelOptions"
                      :clear="false"
                      @change="handleFirstLevelChange"
                      placeholder="请选择一级分类"
                  ></uni-data-select>
                </view>

                <!-- 二级分类 -->
                <view class="select-wrapper" v-if="secondLevelOptions.length > 0">
                  <uni-data-select
                      v-model="selectedSecondLevel"
                      :localdata="secondLevelOptions"
                      :clear="false"
                      @change="handleSecondLevelChange"
                      placeholder="请选择二级分类"
                      class="mt-2"
                  ></uni-data-select>
                </view>

                <!-- 三级分类 -->
                <view class="select-wrapper" v-if="thirdLevelOptions.length > 0">
                  <uni-data-select
                      v-model="selectedThirdLevel"
                      :localdata="thirdLevelOptions"
                      :clear="false"
                      @change="handleThirdLevelChange"
                      placeholder="请选择三级分类"
                      class="mt-2"
                  ></uni-data-select>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="popup-footer">
          <text class="popup-btn popup-btn-cancel" @click="closeSelectorPopup">取消</text>
          <text class="popup-btn popup-btn-confirm" @click="confirmSelector">确认</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import {
  listWareHouse,    // 获取仓库列表
  setHrpMatTree,    // 获取部门数据
  listHrpMat        // 获取耗材数据
} from '@/api/HRP/hrpmat.js'

export default {
  name: "HrpMaterialSelect",
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 仓库相关
      warehouseList: [],
      selectedWarehouse: '', // 存储dwbh值

      // 分类树相关
      departmentTree: [], // 完整的分类树
      selectedFirstLevel: '', // 存储sortCode值
      selectedSecondLevel: '', // 存储sortCode值
      selectedThirdLevel: '', // 存储sortCode值

      // 弹窗选择后的展示数据
      currentWarehouseName: '', // 当前选中的仓库名称
      currentFirstLevelName: '', // 当前选中的一级分类名称
      currentSecondLevelName: '', // 当前选中的二级分类名称
      currentThirdLevelName: '', // 当前选中的三级分类名称

      // 耗材相关
      selectedMaterials: [],
      userSelectedMaterials: [],

      // 加载状态
      loading: false,

      // 分页相关
      pageSize: 10, // 每页显示数量，固定10条
      currentPage: 1 // 当前页码
    }
  },
  computed: {
    // 转换仓库数据为uni-data-select需要的格式
    warehouseOptions() {
      return this.warehouseList.map(item => ({
        value: item.dwbh,
        text: item.dwbhName
      }))
    },

    // 转换一级分类数据为uni-data-select需要的格式
    firstLevelOptions() {
      return this.departmentTree.map(item => ({
        value: item.sortCode,
        text: item.sortName
      }))
    },

    // 二级分类列表
    secondLevelList() {
      if (!this.selectedFirstLevel) return []
      const firstLevel = this.departmentTree.find(item => item.sortCode === this.selectedFirstLevel)
      return firstLevel?.children || []
    },

    // 转换二级分类数据为uni-data-select需要的格式
    secondLevelOptions() {
      return this.secondLevelList.map(item => ({
        value: item.sortCode,
        text: item.sortName
      }))
    },

    // 三级分类列表
    thirdLevelList() {
      if (!this.selectedSecondLevel) return []
      const secondLevel = this.secondLevelList.find(item => item.sortCode === this.selectedSecondLevel)
      return secondLevel?.children || []
    },

    // 转换三级分类数据为uni-data-select需要的格式
    thirdLevelOptions() {
      return this.thirdLevelList.map(item => ({
        value: item.sortCode,
        text: item.sortName
      }))
    },

    userSelectedTotalPrice() {
      return this.userSelectedMaterials.reduce((total, item) => {
        const quantity = Number(item.totalQuantity) || 0
        const price = Number(item.productPrice) || 0
        return total + (quantity * price)
      }, 0).toFixed(2)
    },

    getEmptyTipText() {
      if (!this.selectedWarehouse) {
        return '请先选择仓库'
      }
      if (this.departmentTree.length > 0 && !this.selectedFirstLevel) {
        return '请选择分类'
      }
      if (this.loading) {
        return '正在加载数据...'
      }
      return '暂无耗材数据'
    },

    // 计算当前页的耗材列表
    paginatedMaterials() {
      // 如果总数据量小于等于页面大小，直接返回所有数据
      if (this.userSelectedMaterials.length <= this.pageSize) {
        return this.userSelectedMaterials;
      }

      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.userSelectedMaterials.slice(startIndex, endIndex);
    },

    // 计算总页数
    totalPages() {
      // 防止除以零
      if (this.pageSize <= 0) return 1;

      // 如果没有数据，返回1页
      if (this.userSelectedMaterials.length === 0) return 1;

      return Math.max(1, Math.ceil(this.userSelectedMaterials.length / this.pageSize));
    }
  },
  methods: {
    handleAddClick() {
      if (!this.disabled) {
        this.openSelectorPopup()
      }
    },
    
    handleDeleteClick(index) {
      if (!this.disabled) {
        this.removeFromSelected(index)
      }
    },

    // 打开仓库和分类选择弹窗
    openSelectorPopup() {
      // 重置弹窗内的选择为当前已确认的选择
      this.resetPopupSelections()

      // 打开弹窗
      this.$refs.selectorPopup.open('center')
    },

    // 关闭仓库和分类选择弹窗
    closeSelectorPopup() {
      // 关闭弹窗
      this.$refs.selectorPopup.close()

      // 重置弹窗内的选择为当前已确认的选择
      this.resetPopupSelections()
    },

    // 重置弹窗内的选择器值为当前已确认的选择
    resetPopupSelections() {
      // 重置仓库选择
      const warehouseItem = this.warehouseList.find(item => item.dwbhName === this.currentWarehouseName)
      this.selectedWarehouse = warehouseItem ? warehouseItem.dwbh : ''

      // 如果有分类树数据且当前有选择的分类，重置分类选择
      if (this.departmentTree.length > 0) {
        // 重置一级分类
        const firstLevelItem = this.departmentTree.find(item => item.sortName === this.currentFirstLevelName)
        this.selectedFirstLevel = firstLevelItem ? firstLevelItem.sortCode : ''

        // 重置二级分类
        if (this.currentSecondLevelName && this.secondLevelList.length > 0) {
          const secondLevelItem = this.secondLevelList.find(item => item.sortName === this.currentSecondLevelName)
          this.selectedSecondLevel = secondLevelItem ? secondLevelItem.sortCode : ''
        } else {
          this.selectedSecondLevel = ''
        }

        // 重置三级分类
        if (this.currentThirdLevelName && this.thirdLevelList.length > 0) {
          const thirdLevelItem = this.thirdLevelList.find(item => item.sortName === this.currentThirdLevelName)
          this.selectedThirdLevel = thirdLevelItem ? thirdLevelItem.sortCode : ''
        } else {
          this.selectedThirdLevel = ''
        }
      }
    },

    // 确认选择并关闭弹窗
    async confirmSelector() {
      // 确保仓库已选择
      if (!this.selectedWarehouse) {
        uni.showToast({
          title: '请先选择仓库',
          icon: 'none'
        });
        return;
      }

      // 更新当前选中的分类名称
      this.updateCurrentSelectionNames()

      // 关闭弹窗
      this.closeSelectorPopup()

      // 获取选择的参数
      let sortCode = ''
      if (this.selectedThirdLevel) {
        sortCode = this.selectedThirdLevel
      } else if (this.selectedSecondLevel) {
        sortCode = this.selectedSecondLevel
      } else if (this.selectedFirstLevel) {
        sortCode = this.selectedFirstLevel
      }

      // 跳转到耗材选择页面
      uni.navigateTo({
        url: '/components/parser-nuoyi-form/child-components/FormHrpMaterialSelect/materialSelection?dwbh=' + this.selectedWarehouse +
            '&sortCode=' + sortCode +
            '&warehouseName=' + encodeURIComponent(this.currentWarehouseName) +
            '&firstLevelName=' + encodeURIComponent(this.currentFirstLevelName || '') +
            '&secondLevelName=' + encodeURIComponent(this.currentSecondLevelName || '') +
            '&thirdLevelName=' + encodeURIComponent(this.currentThirdLevelName || ''),
        events: {
          // 监听材料选择完成事件
          updateSelectedMaterials: (data) => {
            console.log('收到选择的材料:', data);
            if (data && Array.isArray(data.hrpMats)) {
              this.userSelectedMaterials = data.hrpMats;
              // 触发材料变化事件
              this.$emit('materials-changed', data);
            }
          }
        },
        success: (res) => {
          // 设置全局事件监听（备用方案）
          const eventChannel = res.eventChannel;
          eventChannel.on('updateSelectedMaterials', (data) => {
            console.log('通过事件通道收到选择的材料:', data);
            if (data && Array.isArray(data.hrpMats)) {
              this.userSelectedMaterials = data.hrpMats;
              // 触发材料变化事件
              this.$emit('materials-changed', data);
            }
          });
        }
      });

      // 添加全局事件总线监听（第三种方案）
      uni.$on('materialSelectionComplete', (data) => {
        console.log('通过全局事件总线收到选择的材料:', data);
        if (data && Array.isArray(data.hrpMats)) {
          this.userSelectedMaterials = data.hrpMats;
          // 触发材料变化事件
          this.$emit('materials-changed', data);
        }
      });
    },

    // 更新当前选中的仓库和分类名称
    updateCurrentSelectionNames() {
      // 更新仓库名称
      const selectedWarehouseItem = this.warehouseList.find(item => item.dwbh === this.selectedWarehouse)
      this.currentWarehouseName = selectedWarehouseItem ? selectedWarehouseItem.dwbhName : ''

      // 更新一级分类名称
      if (this.selectedFirstLevel) {
        const firstLevelItem = this.departmentTree.find(item => item.sortCode === this.selectedFirstLevel)
        this.currentFirstLevelName = firstLevelItem ? firstLevelItem.sortName : ''
      } else {
        this.currentFirstLevelName = ''
      }

      // 更新二级分类名称
      if (this.selectedSecondLevel) {
        const secondLevelItem = this.secondLevelList.find(item => item.sortCode === this.selectedSecondLevel)
        this.currentSecondLevelName = secondLevelItem ? secondLevelItem.sortName : ''
      } else {
        this.currentSecondLevelName = ''
      }

      // 更新三级分类名称
      if (this.selectedThirdLevel) {
        const thirdLevelItem = this.thirdLevelList.find(item => item.sortCode === this.selectedThirdLevel)
        this.currentThirdLevelName = thirdLevelItem ? thirdLevelItem.sortName : ''
      } else {
        this.currentThirdLevelName = ''
      }
    },

    // 初始化数据
    async initData() {
      try {
        console.log('初始化数据')
        await this.loadWarehouseList()
      } catch (error) {
        console.error('初始化数据失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
      }
    },

    // 加载仓库列表
    async loadWarehouseList() {
      try {
        console.log('开始加载仓库列表')
        const res = await listWareHouse()
        console.log('仓库列表返回:', res)

        if (res.code === 200) {
          this.warehouseList = res.data || []
          console.log('仓库列表设置成功:', this.warehouseList)
          console.log('仓库选项:', this.warehouseOptions)

          // 自动选择第一个仓库
          if (this.warehouseList.length > 0) {
            this.selectedWarehouse = this.warehouseList[0].dwbh
            this.currentWarehouseName = this.warehouseList[0].dwbhName

            // 自动加载该仓库的分类树
            await this.loadDepartmentTree(this.selectedWarehouse)
          }
        } else {
          throw new Error(res.msg)
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error)
        throw error
      }
    },

    // 加载分类树数据
    async loadDepartmentTree(dwbh) {
      try {
        console.log('加载分类树数据，仓库编号:', dwbh)
        if (!dwbh) {
          console.error('仓库编号为空')
          return
        }

        const res = await setHrpMatTree({ dwbh })
        console.log('分类树数据返回:', res)

        if (res.code === 200) {
          this.departmentTree = res.data || []
          console.log('设置分类树数据成功:', this.departmentTree)
          console.log('一级分类选项:', this.firstLevelOptions)

          // 重置所有分类选择
          this.selectedFirstLevel = ''
          this.selectedSecondLevel = ''
          this.selectedThirdLevel = ''
          this.selectedMaterials = [] // 清空耗材数据
        } else {
          throw new Error(res.msg)
        }
      } catch (error) {
        console.error('获取分类树数据失败:', error)
        throw error
      }
    },

    // 加载耗材数据
    async loadMaterialsData() {
      try {
        this.loading = true
        console.log('加载耗材数据')
        console.log('当前仓库:', this.selectedWarehouse)
        console.log('当前一级分类:', this.selectedFirstLevel)
        console.log('当前二级分类:', this.selectedSecondLevel)
        console.log('当前三级分类:', this.selectedThirdLevel)

        let sortCode = ''

        // 获取最后选择的有效分类编码
        if (this.selectedThirdLevel) {
          sortCode = this.selectedThirdLevel
        } else if (this.selectedSecondLevel) {
          sortCode = this.selectedSecondLevel
        } else if (this.selectedFirstLevel) {
          sortCode = this.selectedFirstLevel
        }

        console.log('使用的分类编码:', sortCode)

        const params = {
          dwbh: this.selectedWarehouse,
          sortCode
        }

        console.log('API请求参数:', params)

        const res = await listHrpMat(params)
        console.log('耗材数据返回:', res)

        if (res.code === 200) {
          // 处理从API获取的数据
          const apiMaterials = (res.data || []).map(material => ({
            ...material,
            // 保存原始库存数量
            originalQuantity: material.quantity !== undefined ? material.quantity : 0
          }));

          // 将API数据与已选数据合并
          this.selectedMaterials = apiMaterials.map(apiItem => {
            // 在已选列表中查找该物品
            const selectedItem = this.userSelectedMaterials.find(
                selItem => selItem.productId === apiItem.productId
            );

            // 如果已选列表中有该物品，返回包含用户选择数量的信息
            if (selectedItem) {
              return {
                ...apiItem,
                selectedQuantity: selectedItem.quantity // 保存用户选择的数量
              };
            }

            // 如果没有找到，返回原始物品数据
            return {
              ...apiItem,
              selectedQuantity: 0
            };
          });

          console.log('设置耗材数据成功:', this.selectedMaterials);
        } else {
          throw new Error(res.msg)
        }
      } catch (error) {
        console.error('获取耗材数据失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 处理仓库选择变化
    async handleWarehouseChange(value) {
      console.log('仓库选择变化:', value)
      console.log('之前的仓库值:', this.selectedWarehouse)

      // 更新选中的仓库值
      this.selectedWarehouse = value
      console.log('selectedWarehouse', this.selectedWarehouse)

      // 确保仓库值有效
      if (!value) {
        console.error('选择的仓库值无效')
        return
      }

      try {
        await this.loadDepartmentTree(value)
      } catch (error) {
        console.error('加载分类数据失败:', error)
        uni.showToast({
          title: '加载分类数据失败',
          icon: 'none'
        })
      }
    },

    // 处理一级分类选择变化
    async handleFirstLevelChange(value) {
      console.log('一级分类选择变化:', value)
      console.log('之前的一级分类值:', this.selectedFirstLevel)

      // 更新选中的一级分类值
      this.selectedFirstLevel = value
      this.selectedSecondLevel = ''
      this.selectedThirdLevel = ''
    },

    // 处理二级分类选择变化
    async handleSecondLevelChange(value) {
      console.log('二级分类选择变化:', value)
      console.log('之前的二级分类值:', this.selectedSecondLevel)

      // 更新选中的二级分类值
      this.selectedSecondLevel = value
      this.selectedThirdLevel = ''
    },

    // 处理三级分类选择变化
    async handleThirdLevelChange(value) {
      console.log('三级分类选择变化:', value)
      console.log('之前的三级分类值:', this.selectedThirdLevel)

      // 更新选中的三级分类值
      this.selectedThirdLevel = value
    },

    // 添加到已选耗材
    addToSelected(item) {
      // 检查库存是否足够
      if (item.originalQuantity <= 0) {
        uni.showToast({
          title: '该物品库存不足',
          icon: 'none'
        })
        return
      }

      // 检查是否已经添加过该耗材
      const existingIndex = this.userSelectedMaterials.findIndex(m => m.productId === item.productId)

      if (existingIndex >= 0) {
        // 已存在，更新提示
        uni.showToast({
          title: '该物品已在已选列表中',
          icon: 'none'
        })
        return
      } else {
        // 不存在，添加新的，默认数量为1
        this.userSelectedMaterials.push({
          ...item,
          quantity: 1
        })

        // 更新原列表中的selectedQuantity
        const materialIndex = this.selectedMaterials.findIndex(m => m.productId === item.productId)
        if (materialIndex >= 0) {
          this.selectedMaterials[materialIndex].selectedQuantity = 1
        }

        uni.showToast({
          title: '已添加到已选列表',
          icon: 'success'
        })
      }

      this.$emit('materials-changed', this.userSelectedMaterials)
    },

    // 已选耗材相关方法
    // 以下方法已不再使用，因为用户不能编辑数量
    // decreaseSelectedQuantity(item) {
    // 	if (typeof item.quantity !== 'number') {
    // 		item.quantity = 0
    // 	}
    // 	if (item.quantity > 1) { // 最小值设为1
    // 		item.quantity--
    // 		this.updateSelectedQuantity(item)
    // 	}
    // },

    // increaseSelectedQuantity(item) {
    // 	if (typeof item.quantity !== 'number') {
    // 		item.quantity = 0
    // 	}
    //
    // 	// 检查是否超过库存
    // 	const materialItem = this.selectedMaterials.find(m => m.productId === item.productId)
    // 	if (materialItem && item.quantity >= materialItem.originalQuantity) {
    // 		uni.showToast({
    // 			title: '不能超过库存数量',
    // 			icon: 'none'
    // 		})
    // 		return
    // 	}
    //
    // 	item.quantity++
    // 	this.updateSelectedQuantity(item)
    // },

    // validateSelectedQuantity(item) {
    // 	let quantity = Number(item.quantity)
    // 	if (isNaN(quantity) || quantity < 1) {
    // 		quantity = 1
    // 	}
    //
    // 	// 检查是否超过库存
    // 	const materialItem = this.selectedMaterials.find(m => m.productId === item.productId)
    // 	if (materialItem && quantity > materialItem.originalQuantity) {
    // 		quantity = materialItem.originalQuantity
    // 		uni.showToast({
    // 			title: '已调整为最大库存数量',
    // 			icon: 'none'
    // 		})
    // 	}
    //
    // 	item.quantity = Math.floor(quantity)
    // 	this.updateSelectedQuantity(item)
    // },

    // 更新选择的数量并同步到展示区域
    // updateSelectedQuantity(item) {
    // 	// 在展示区域查找对应的物品并更新其selectedQuantity
    // 	const materialIndex = this.selectedMaterials.findIndex(m => m.productId === item.productId)
    // 	if (materialIndex >= 0) {
    // 		this.selectedMaterials[materialIndex].selectedQuantity = item.quantity
    // 	}
    //
    // 	this.$emit('materials-changed', this.userSelectedMaterials)
    // },

    removeFromSelected(index) {
      // 计算实际索引
      const realIndex = (this.currentPage - 1) * this.pageSize + index;
      const removedItem = this.userSelectedMaterials[realIndex]
      this.userSelectedMaterials.splice(realIndex, 1)

      // 在展示区域重置对应物品的selectedQuantity
      if (removedItem) {
        const materialIndex = this.selectedMaterials.findIndex(m => m.productId === removedItem.productId)
        if (materialIndex >= 0) {
          this.selectedMaterials[materialIndex].selectedQuantity = 0
        }
      }

      // 如果当前页没有数据了且不是第一页，则回到上一页
      if (this.paginatedMaterials.length === 0 && this.currentPage > 1) {
        this.currentPage--;
      }

      this.$emit('materials-changed', this.userSelectedMaterials)
    },
    onShow() {
      // 获取从耗材选择页面返回的数据
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.on('updateSelectedMaterials', (data) => {
          console.log('接收到选择的耗材数据:', data);
          if (data && data.selectedMaterials && data.selectedMaterials.length > 0) {
            // 合并已选耗材数据
            this.mergeSelectedMaterials(data.selectedMaterials);
          }
        });
      }
    },
    // 合并已选耗材数据
    mergeSelectedMaterials(newMaterials) {
      if (!newMaterials || newMaterials.length === 0) return;

      // 在控制台打印调试信息
      console.log('合并前的已选耗材:', JSON.stringify(this.userSelectedMaterials));
      console.log('新选择的耗材:', JSON.stringify(newMaterials));

      let hasNewItems = false;

      // 遍历新选择的耗材
      newMaterials.forEach(newItem => {
        // 检查是否已在已选列表中
        const existingIndex = this.userSelectedMaterials.findIndex(
            item => item.productId === newItem.productId
        );

        if (existingIndex >= 0) {
          // 已存在，直接使用新的数量，不再累加
          // 如果想保留用户之前的修改，可以根据需求决定如何处理
          const currentItem = this.userSelectedMaterials[existingIndex];
          console.log(`耗材 ${newItem.productName} 已存在，原数量:${currentItem.quantity}，新数量:${newItem.quantity}`);

          // 以下两种处理方式二选一：

          // 方式1：使用新选择的数量覆盖原有数量
          this.userSelectedMaterials[existingIndex].quantity = newItem.quantity;

          // 方式2：保留已有数量不变（如果需要保留用户之前的修改）
          // 不做任何处理
        } else {
          // 不存在，添加到列表
          console.log(`添加新耗材 ${newItem.productName}，数量:${newItem.quantity}`);
          this.userSelectedMaterials.push(newItem);
          hasNewItems = true;
        }
      });

      console.log('合并后的已选耗材:', JSON.stringify(this.userSelectedMaterials));

      // 如果有新增项目，重置到第一页
      if (hasNewItems) {
        this.currentPage = 1;
      }

      // 触发材料变更事件
      this.$emit('materials-changed', this.userSelectedMaterials);
    },
    changePage(page) {
      // 如果页码无效，直接返回
      if (page < 1 || page > this.totalPages) {
        return;
      }
      this.currentPage = page;
    },
    // 删除耗材方法
    deleteMaterial(material) {
        const index = this.materials.findIndex(item => item.productId === material.productId);
        if (index !== -1) {
            this.materials.splice(index, 1);
            // 发送删除事件
            uni.$emit('materialDeleted', material);
            
            // 更新表单值
            this.updateFormValue();
        }
    },
  },
  mounted() {
    this.initData()
  }
}
</script>

<style lang="scss">
/* 添加更具体的选择器 */
::v-deep .selected-materials-container .table .table-body {
  max-height: 6rem !important;
  overflow-y: auto;
}

.page {
  padding: 20rpx;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 0;
}

.header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  flex-shrink: 0;

  .add-btn {
    background-color: #409EFF;
    border-radius: 8rpx;
    padding: 12rpx 24rpx;

    .btn-text {
      color: #FFFFFF;
      font-size: 28rpx;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

/* 选择器展示区域样式 */
.selector-display {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.selector-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .warehouse-selector,
  .department-selector {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 28rpx;
      margin-right: 20rpx;
      min-width: 140rpx;
      padding-top: 10rpx;
    }

    .sort-tree {
      flex: 1;

      .select-wrapper {
        margin-bottom: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .select-wrapper {
    flex: 1;
  }
}

.mt-2 {
  margin-top: 20rpx;
}

/* 弹窗样式 */
.popup-content {
  width: 650rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  height: 65vh; /* 设置高度为视口高度的65% */
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  flex-shrink: 0; /* 防止头部被压缩 */

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #303133;
  }

  .popup-close {
    font-size: 40rpx;
    color: #909399;
    padding: 10rpx;
  }
}

.popup-body {
  padding: 30rpx;
  flex: 1; /* 让内容区域自动填充剩余空间 */
  overflow-y: auto; /* 内容超出时可滚动 */
}

.popup-footer {
  display: flex;
  border-top: 1rpx solid #eee;
  flex-shrink: 0; /* 防止底部被压缩 */

  .popup-btn {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    font-size: 30rpx;

    &.popup-btn-cancel {
      color: #606266;
      border-right: 1rpx solid #eee;
    }

    &.popup-btn-confirm {
      color: #409EFF;
      font-weight: bold;
    }
  }
}

// 深度选择器，用于修改组件内部样式
::v-deep .uni-data-select .uni-select--input {
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1px solid #DCDFE6;
  border-radius: 6rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 24rpx;
    background-color: #409EFF;
    border-radius: 3rpx;
  }
}

.selected-materials-container {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  height: auto;

  .section-title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
    font-weight: bold;
  }

  .cards-container {
    max-height: 400rpx; /* 设置固定高度 */
    overflow-y: auto; /* 添加垂直滚动 */
    padding-right: 10rpx; /* 为滚动条预留空间 */

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6rpx;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3rpx;
    }

    &::-webkit-scrollbar-thumb {
      background: #409EFF;
      border-radius: 3rpx;
    }
  }

  .material-card {
    margin-bottom: 20rpx;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }

    .card-header {
      padding: 16rpx 20rpx;
      background-color: #409EFF;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
    }

    .delete-btn {
      color: #fff;
      font-size: 24rpx;
      padding: 4rpx 12rpx;
      border-radius: 4rpx;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .delete-btn.disabled {
      opacity: 0.5;
      pointer-events: none;
    }

    .card-content {
      padding: 20rpx;
    }

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;
    }

    .info-row:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #666;
      font-size: 26rpx;
    }

    .value {
      color: #333;
      font-size: 26rpx;
    }

    .total-row {
      margin-top: 16rpx;
      padding-top: 16rpx;
      border-top: 1rpx solid #eee;
    }

    .total-row .value {
      color: #f56c6c;
      font-weight: 500;
    }
  }
}

.footer-container {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.total-price {
  margin-bottom: 20rpx;
}

.total {
  color: #f56c6c;
  font-size: 32rpx;
  font-weight: bold;
}

.pagination-control {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pagination-buttons {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.pagination-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 4rpx;
  margin: 0 8rpx;
}

.pagination-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.pagination-pages {
  display: flex;
  align-items: center;
}

.pagination-page {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8rpx;
  background-color: #f5f5f5;
  border-radius: 4rpx;
}

.pagination-page.active {
  background-color: #409EFF;
  color: #fff;
}

.pagination-ellipsis {
  margin: 0 8rpx;
}

.pagination-info {
  font-size: 24rpx;
  color: #999;
}

.empty-tip {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 40rpx;
  text-align: center;
  color: #909399;
  font-size: 28rpx;
  height: auto;
}


</style>