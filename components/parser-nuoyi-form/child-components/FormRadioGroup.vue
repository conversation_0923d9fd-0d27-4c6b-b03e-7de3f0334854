<!-- components/parser-nuoyi-form/child-components/FormRadioGroup.vue -->
<template>
  <view>
    <!-- #ifdef APP-PLUS -->
    <view class="radio-list">
      <view
          v-for="(optItem, index) in options"
          :key="optItem.value"
          class="radio-item"
          :class="{ 'disabled': field.disabled }"
          @tap="handleItemTap(optItem)"
      >
        <view class="radio-wrapper">
          <view class="radio" :class="{ 'checked': String(selectedValue) === String(optItem.value) }"></view>
        </view>
        <text class="radio-label">{{ optItem.label }}</text>
      </view>
    </view>
    <!-- #endif -->

    <!-- #ifndef APP-PLUS -->
    <radio-group
        :disabled="field.disabled"
        @change="handleChange">
      <label class="uni-list-cell uni-list-cell-pd radio-group-item-vertical-direction"
             v-for="(optItem, index) in options" 
             :key="String(optItem.value)">
        <view>
          <radio :value="String(optItem.value)"
                 :checked="String(selectedValue) === String(optItem.value)"
                 :disabled="field.disabled" />
        </view>
        <view>{{optItem.label}}</view>
      </label>
    </radio-group>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  name: 'FormRadioGroup',

  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    },
    disabled: {  // 添加 disabled prop
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      selectedValue: this.field.__config__?.defaultValue || '',  // 获取当前选中的值
      options: this.field.__slot__.options || []  // 从 __slot__ 中获取选项
    }
  },
	created() {
    console.log("获取类型=",typeof this.selectedValue);
	},
  methods: {
    handleChange(e) {
      this.selectedValue = e.detail.value;
      console.log("值=",this.selectedValue);
      console.log("传递类型=",typeof this.selectedValue);
      //this.$emit('update:value', this.selectedValue);
      this.$emit('update:value', {
        curField:this.field,
        curVal:this.selectedValue
      });
    },

    handleItemTap(optItem) {
      if (this.field.disabled) return;
      console.log("值=",this.selectedValue);
      console.log("传递类型=",typeof this.selectedValue);
      this.selectedValue = optItem.value;
      //this.$emit('update:value', optItem.value);
      this.$emit('update:value', {
        curField:this.field,
        curVal:this.selectedValue
      });
    }
  }
}
</script>

<style lang="scss">
.radio-group {
  margin: 10px 0;
}
.radio-group-item-vertical-direction {
  display: flex;
  justify-content: left;
  align-items: center;
}

.radio-list {
  width: 100%;
}

.radio-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  
  &:active {
    background-color: #f5f7fa;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .radio-wrapper {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .radio {
      width: 36rpx;
      height: 36rpx;
      border: 2rpx solid #dcdfe6;
      border-radius: 50%;
      background-color: #fff;
      
      &.checked {
        background-color: #409EFF;
        border-color: #409EFF;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 16rpx;
          height: 16rpx;
          background-color: #fff;
          border-radius: 50%;
        }
      }
    }
  }

  .radio-label {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #333;
  }
}
</style>