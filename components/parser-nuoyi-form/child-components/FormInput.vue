<template>
  <view class="form-input">
    <view class="input-wrap">
      <view v-if="field.__slot__ && field.__slot__.prepend" class="prepend">
        {{ field.__slot__.prepend }}
      </view>

      <input
          class="uni-input"
          :value="inputValue || ''"
          :type="field.type || 'text'"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          :maxlength="field.maxlength"
          :style="field.style"
          @input="handleInput"
          @blur="handleBlur"
          @focus="handleFocus"
      />

      <view v-if="field.__slot__ && field.__slot__.append" class="append">
        {{ field.__slot__.append }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormInput',

  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      inputValue: this.field.__config__?.defaultValue || ''
    }
  },

  created() {
    this.initValue()
  },

  watch: {
    // formData: {
    //   deep: true,
    //   immediate: true,
    //   handler() {
    //     this.initValue()
    //   }
    // }
  },

  methods: {
    initValue() {
      // const vModel = this.field.__vModel__
      // 获取formData中的值
      let value = this.field.__config__?.defaultValue

      // 如果值是对象或未定义，使用空字符串
      if (typeof value === 'object' || value === undefined || value === null) {
        value = ''
      }

      this.inputValue = String(value)
	    console.log("inputValue2",this.inputValue)

    },

    handleInput(event) {
      const value = event.target.value || event.detail.value || ''

      // // 如果输入的是[object Object]，则清空
      // if (value === '[object Object]') {
      //   this.inputValue = ''
      //   this.$emit('update:value', {
      //     key: this.field.__vModel__,
      //     value: ''
      //   })
      //   return
      // }
      // 更新本地值
      this.inputValue = value
      console.log("FormInput值发生改变",value)
      // 发送更新事件
      //this.$emit('update:value', value)
      this.$emit('update:value', {
        curField:this.field,
        curVal:value
      });
    },

    clearInput() {
      this.inputValue = ''
      // this.$emit('update:value', {
      //   key: this.field.__vModel__,
      //   value: ''
      // })
      this.$emit('update:value', {
        curField:this.field,
        curVal:''
      });
    },

    handleBlur(event) {
      // 如果值是[object Object]，则在失焦时清空
      if (this.inputValue === '[object Object]') {
        this.clearInput()
      }
      this.$emit('blur', event)
    },

    handleFocus(event) {
      this.$emit('focus', event)
    }
  }
}
</script>

<style lang="scss">
.form-input {
  .input-wrap {
    display: flex;
    align-items: center;
    width: 100%;

    .prepend,
    .append {
      padding: 0 20rpx;
      background: #f5f7fa;
      color: #909399;
      border: 1rpx solid #dcdfe6;
      height: 70rpx;
      line-height: 70rpx;
      white-space: nowrap;
    }

    .prepend {
      border-right: none;
      border-radius: 4rpx 0 0 4rpx;
    }

    .append {
      border-left: none;
      border-radius: 0 4rpx 4rpx 0;
    }

    .uni-input {
      flex: 1;
      height: 70rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
      color: #606266;
      border: 1rpx solid #dcdfe6;
      border-radius: 0;

      &[disabled] {
        background-color: #f5f7fa;
      }
    }

    .word-limit {
      margin-left: 10rpx;
      font-size: 24rpx;
      color: #909399;
    }
  }
}
</style>