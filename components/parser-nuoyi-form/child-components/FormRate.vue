<template>
  <view class="form-rate">
    <view class="rate">
      <view
          v-for="(star, index) in maxStars"
          :key="index"
          class="star"
          :class="{ active: currentValue >= (index + 1) }"
          @tap="handleRate(index + 1)"
      >
        <uni-icons
            :type="currentValue >= (index + 1) ? 'star-filled' : 'star'"
            :color="currentValue >= (index + 1) ? '#ffd21e' : '#dcdfe6'"
            size="24"
        ></uni-icons>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormRate',

  props: {
    field: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    }
  },

  data() {
    // 安全地获取默认值
    let defaultValue = 0;
    try {
      if (this.field && this.field.__config__ && this.field.__config__.defaultValue !== undefined) {
        defaultValue = this.field.__config__.defaultValue;
      }
    } catch (e) {
      console.error('获取默认值时出错:', e);
    }
    
    return {
      currentValue: defaultValue
    }
  },

  computed: {
    maxStars() {
      try {
        if (this.field && typeof this.field.max === 'number' && this.field.max > 0) {
          return this.field.max;
        }
        return 5; // 默认值
      } catch (e) {
        console.error('计算最大星级时错误:', e);
        return 5;
      }
    }
  },

  methods: {
    handleRate(value) {
      try {
        // 檢查 field 是否存在
        if (!this.field) {
          console.error('field 不存在');
          return;
        }
        
        if (this.field.disabled) return;

        // 如果点击当前值，則清除
        const newValue = this.currentValue === value ? 0 : value;

        // 更新本地值
        this.currentValue = newValue;

        // 发送更新事件
        this.$emit('update:value', {
          curField: this.field,
          curVal: newValue
        });
      } catch (e) {
        console.error('处理评分时出错:', e);
      }
    }
  },
  

  created() {
    try {
      // 如果 formData 中已有值，优先使用
      if (this.field && this.field.__vModel__ && 
          this.formData && this.formData[this.field.__vModel__] !== undefined) {
        this.currentValue = Number(this.formData[this.field.__vModel__]) || 0;
      }
    } catch (e) {
      console.error('初始化评分组件时出错:', e);
    }
  }
}
</script>

<style lang="scss">
.form-rate {
  .rate {
    display: flex;
    align-items: center;

    .star {
      margin-right: 10rpx;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }
    }
  }
}
</style>