<template>
  <view class="dynamic-form">
    <scroll-view  scroll-y="true" class="scroll-Y" :style="containerHeight ? {height: containerHeight} : {}">
      <uni-forms
          ref="form"
          :model="formDataCopy"
          :rules="formRules"
          validate-trigger="submit">
        <form-item-render
            v-for="field in formConfigCopy.fields"
            :key="field.__config__.renderKey"
            :field="field"
            :form-data="formDataCopy"
            :childrenLength="field.__config__.children?field.__config__.children.length:1"
            :layout="field.__config__.layout"
            @update:value="(val) => handleFieldChange(field, val)"/>
        <!-- 表单按钮 -->
        <view class="form-btns" v-if="formConfigCopy.formBtns">
          <button type="primary" @tap="submitForm">提交</button>
          <button type="default" @tap="handleReset">重置</button>
        </view>
      </uni-forms>
    </scroll-view>
  </view>
</template>

<script>
import {
  deepClone,
} from "@/utils/comUtil";
import FormItemRender from '@/components/parser-nuoyi-form/FormItemRender.vue'
import {CommUtil} from "@/utils/commUtil";

export default {
  name: 'DynamicForm',

  components: {
    FormItemRender
  },

  props: {
    formConfig: {
      type: Object,
      required: true
    },
    initialFormData: {
      type: Object,
      default: () => ({})
    },
    processVariables:{
      type: Object,
      default: () => ({})
    },
    containerHeight: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formConfigCopy:deepClone(this.formConfig),
      formData: {},
      formDataCopy:{},
      formRules: {}
    }
  },
  watch: {
    formConfig: {
      immediate: true, // 立即执行
      handler(newVal, oldVal) {
        //console.log('DynamicForm--formConfig--handler',newVal, oldVal)
        this.formConfigCopy = deepClone(newVal);
        this.initProcessVariables2FormConfigCopy();
        this.initFormData(); // 重新初始化表单数据
        this.initFormRules(); // 重新初始化表单规则
      }
    },
    processVariables: {
      immediate: true, // 立即执行
      handler(newVal, oldVal) {
        //console.log('DynamicForm--processVariables--handler',newVal, oldVal)
        this.initProcessVariables2FormConfigCopy();
      }
    },
    formConfigCopy: {
      immediate: true, // 立即执行
      handler(newVal, oldVal) {
        //console.log('DynamicForm--formConfigCopy--handler',newVal, oldVal)
      }
    },
    formData: {
      immediate: true, // 立即执行
      handler(newVal, oldVal) {
         //console.log('DynamicForm--formData--handler',newVal, oldVal)
      }
    },
  },

  created() {
    this.initProcessVariables2FormConfigCopy()
    this.initFormData()
    this.initFormRules()
  },

  methods: {
    // 把processVariables中的值赋值给formConfigCopy
    initProcessVariables2FormConfigCopy() {
      if (!this.processVariables)
        return;
      const self = this;
      if (this.formConfigCopy.fields)
        this.traverseFields(this.formConfigCopy.fields, field => {

          // 如果formConfigCopy.disabled为true，则设置field.disabled为true
          if (self.formConfigCopy.disabled && self.formConfigCopy.disabled == true){
            self.$set(field, 'disabled', true)
          }
          //把deviceUseDeptId传给 远程下拉框组
          //如果组件有listUsersType属性，则为远程下拉框组件，根据不同的listUsersType
          // 从deviceUsersData中获取对应的用户列表，并将其设置为组件的options。
          // [先读取某个下拉框的字段属性，XXX，根据某种方法识别对于的设备端，将此端的组件设置为可用]
          if(field.__config__.tag == 'el-select' && field.listUsersType !== undefined) {
            field.deviceUseDeptId = (self.processVariables.deviceUseDeptId)?self.processVariables.deviceUseDeptId:0;
            if (field.listUsersType==1 &&
                self.processVariables.listUsers4DeviceUseDept != undefined) {
              self.$set(field.__slot__, 'options', self.processVariables.listUsers4DeviceUseDept)
              //field.__slot__.options=(self.processVariables.listUsers4DeviceUseDept != undefined)?self.processVariables.listUsers4DeviceUseDept:field.__slot__.options;
            }
            else if(field.listUsersType==2 &&
                self.processVariables.listUsers4DeviceDept != undefined){
              self.$set(field.__slot__, 'options', self.processVariables.listUsers4DeviceDept)
             // field.__slot__.options=(self.processVariables.listUsers4DeviceDept != undefined)?self.processVariables.listUsers4DeviceDept:field.__slot__.options;
            }
            else if(field.listUsersType==3 &&
                self.processVariables.listUsers4FinanceDept != undefined){
              self.$set(field.__slot__, 'options', self.processVariables.listUsers4FinanceDept)
              //field.__slot__.options=(self.processVariables.listUsers4FinanceDept != undefined)? self.processVariables.listUsers4FinanceDept:field.__slot__.options;
            }
            else{
              self.$set(field.__slot__, 'options', self.processVariables.listUsers4AuditDept)
              //field.__slot__.options=(self.processVariables.listUsers4AuditDept != undefined)?self.processVariables.listUsers4AuditDept:field.__slot__.options;
            }
          }
        })
    },
    // 初始化表单数据
    initFormData() {
      const self = this;
      if (self.formConfigCopy.fields) {
        self.traverseFields(self.formConfigCopy.fields, field => {
          // 处理签名组件和上传组件的URL转换
          if (field.__config__.tag === 'handSign') {
            if (field.__config__.defaultValue) {
              field.__config__.defaultValue = self.$replaceUrlDomain(field.__config__.defaultValue);
            }
          }
          
          // 处理上传组件的URL转换
          if (field.__config__.tag === 'el-upload' || field.__config__.tag === 'handSign') {
            CommUtil.handleUrl4OSS(field.__config__);
          }

          // 默认值设置逻辑
          if (field.__config__.defaultValue == undefined) {
            if (this.formData[field.__vModel__] == undefined) {
              self.$set(self.formData, field.__vModel__, field.__config__.defaultValue);
            }
          } else {
            self.$set(self.formData, field.__vModel__, field.__config__.defaultValue);
          }
        });
      }
      
      this.formData = {
        ...this.formData,
        ...this.initialFormData
      }
      this.formDataCopy = deepClone(this.formData);
    },

    // 初始化表单校验规则
    initFormRules() {
      const rules = {}
      if (this.formConfigCopy.fields)
          this.traverseFields(this.formConfigCopy.fields, field => {
            // 如果字段是必填项，则设置校验规则;禁用字段忽视
            if(field.__config__.required && ( field.disabled == undefined || field.disabled == false)) {
              rules[field.__vModel__] = {
                rules: [{
                  required: true,
                  message: `${field.__config__.label}不能为空`
                }]
              }
            }
          })
      this.formRules = rules
    },

    // 遍历字段
    traverseFields(fields, callback) {
      if (!fields)
        return
      fields.forEach(field => {
        if(field.__config__.children) {
          this.traverseFields(field.__config__.children, callback)
        } else {
          callback(field)
        }
      })
    },
    // 遍历字段
    traverseFields4DefaultValue(fields,findField,value) {
      if(!fields || fields.length === 0)
        return;

      if (!findField)
        return;
      for (let i = 0; i < fields.length; i++) {
        const field = fields[i];
        if (field.__config__.renderKey == findField.__config__.renderKey &&
            field.__vModel__ == findField.__vModel__) {
            //console.log('Found field:', field);
            // 如果字段不是禁用的，设置默认值
            if (field.disabled == undefined || field.disabled == false){
               this.$set(field.__config__, 'defaultValue', value)
               return;
            }

        }

        // 如果字段有子字段，递归遍历子字段
       // if (field.children && field.children.length > 0)
       //   this.traverseFields4DefaultValue(field.children, findField,value);
        if(field.__config__.children)
          this.traverseConfigChildren(field.__config__.children, findField,value);
      }
    },
    // 遍历字段
    traverseConfigChildren(fields,findField,value) {
      if (!fields)
        return
      fields.forEach(field => {
        if(field.__config__.renderKey == findField.__config__.renderKey &&
            field.__vModel__ == findField.__vModel__) {
           // 如果字段不是禁用的，设置默认值
           if (field.disabled == undefined || field.disabled == false){
              this.$set(field.__config__, 'defaultValue', value)
              return;
           }

        }
        if(field.__config__.children) {
          this.traverseConfigChildren(field.__config__.children,findField,value)
        }
      })
    },
    // 处理字段值变化
    handleFieldChange(field, value) {
      console.log('value', value);
     // this.$set(this.formDataCopy, field.__vModel__, value)
      this.$set(this.formDataCopy,
          value.curField.__vModel__,
          value.curVal)
      console.log('value.curField', value.curField);
      console.log('value.curVal', value.curVal);
      //this.formData[field.__vModel__] = value
      //field.__config__.defaultValue = value


     // this.traverseFields4DefaultValue(this.formConfigCopy.fields,field,value)
      this.traverseFields4DefaultValue(this.formConfigCopy.fields,
          value.curField,
          value.curVal)

      // this.$emit('field-change', {
      //   field,
      //   value,
      //   formData: this.formData
      // })
    },

    // 提交表单
    async submitForm() {
		console.log("formData",this.formData)
      try {
        await this.$refs.form.validate()
        this.$emit('submit', {
          valData: this.formDataCopy,
          formData: this.formConfigCopy
        })
      } catch(e) {
        console.log('表单校验失败', e)
      }
    },

    // 重置表单
    handleReset() {
      // 调用 resetFields 方法重置表单
      if (this.$refs.form && typeof this.$refs.form.resetFields === 'function') {
        this.$refs.form.resetFields(); // 重置表单字段
      }
      this.initFormData()
      this.$emit('reset')
    }
  }
}
</script>

<style lang="scss">
.dynamic-form {
  padding: 20rpx;

  .form-btns {
    display: flex;
    justify-content: center;
    margin-top: 40rpx;

    button {
      margin: 0 20rpx;

      &[type="primary"] {
        background: #409eff;
        color: #fff;
      }
    }
  }
}
.scroll-Y {
  height: 1200rpx; /* 默认高度，当没有传入containerHeight时使用 */
}
//.uni-forms {
//  background-color: #fff;
//  padding: 0 20rpx;
//  display: flex;
//  //margin: 10rpx 0;
//}
//
//.uni-forms-item {
//  // display: flex;
//  // flex-direction: column;
//  // padding: 0 0 10rpx 0;
//  margin: 0 0 30rpx 0;
//}
//.radio-group-item-vertical-direction{
//  display: flex;
//  justify-content: left;
//  align-items: center;
//}
//.mPlaceholder {
//  color: rgb(192, 196, 204);
//}
//
//.buttons {
//  // display: flex;
//  // padding: 20rpx 0 0 20rpx;
//
//  .button {
//    background-color: #027eff;
//    color: #fff;
//    // margin-right: 20rpx;
//  }
//}
</style>