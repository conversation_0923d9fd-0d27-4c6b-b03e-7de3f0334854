<template>
  <view class="form-item-render">

    <!-- 行容器 -->
    <view v-if="field.__config__.children "
          :class="['form-row', {'flex-row': field.type === 'flex'}]">
      <view
          class="row-content">
          <view v-for="(child, index) in field.__config__.children"
              :key="child.__config__.renderKey"
              :class="(index % 2 === 0) ? 'flex-item' : 'flex-item second'">
                  <!-- 递归渲染子项 -->
                  <FormItemRender
                      :field="child"
                      :form-data="formData"
                      :childrenLength="child.__config__.children?child.__config__.children.length:1"
                      :layout="child.__config__.layout"
                      @update:value="(val) => $emit('update:value', val)"/>
            </view>
      </view>
    </view>
    <!-- 普通表单项 -->
    <view v-else >
      <uni-forms-item
          :label="field.__config__.showLabel ? field.__config__.label : ''"
          :required="field.__config__.required"
          :name="field.__vModel__">
          <view v-if="field.__config__.tag === 'el-rate'">
                <form-rate
                    :field="field"
                    :form-data="formData"
                    @update:value="val => $emit('update:value', val)"/>
          </view>
        <view v-else-if="field.__config__.tag === 'el-upload'">
          <form-upload
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'el-switch'">
          <form-switch
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'el-input'">
          <form-input
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'handSign'">
          <form-hand-sign
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'el-select'">
          <form-data-select
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'el-radio-group'">
          <form-radio-group
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'el-checkbox-group'">
          <form-checkbox-group
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'handMaterial'">
          <form-material-select
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'hrpMats'">
          <form-hrp-material-select
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'reactiveSelect'">
          <form-reactive-select
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
        <view v-else-if="field.__config__.tag === 'handPrice'">
          <form-hand-price
              :field="field"
              :form-data="formData"
              @update:value="val => $emit('update:value', val)"/>
        </view>
      </uni-forms-item>
    </view>


  </view>
</template>

<script>
import FormInput from '@/components/parser-nuoyi-form/child-components/FormInput.vue'
import FormRate from '@/components/parser-nuoyi-form/child-components/FormRate.vue'
import FormUpload from '@/components/parser-nuoyi-form/child-components/FormUpload.vue'
import FormSwitch from '@/components/parser-nuoyi-form/child-components/FormSwitch.vue'
import FormHandSign from '@/components/parser-nuoyi-form/child-components/FormHandSign.vue'
import FormDataSelect from '@/components/parser-nuoyi-form/child-components/FormDataSelect.vue'
import FormRadioGroup from '@/components/parser-nuoyi-form/child-components/FormRadioGroup.vue'
import FormMaterialSelect from '@/components/parser-nuoyi-form/child-components/FormMaterialSelect.vue'
import FormCheckboxGroup from '@/components/parser-nuoyi-form/child-components/FormCheckboxGroup.vue'
import FormHandPrice from '@/components/parser-nuoyi-form/child-components/FormHandPrice.vue'
import FormHrpMaterialSelect from '@/components/parser-nuoyi-form/child-components/FormHrpMaterialSelect/FormHrpMaterialSelect.vue'
import FormReactiveSelect from '@/components/parser-nuoyi-form/child-components/FormReactiveSelect.vue'
import FormItemRender from '@/components/parser-nuoyi-form/FormItemRender.vue'


export default {
  name: 'FormItemRender',

  components: {
    FormInput,
    FormRate,
    FormUpload,
    FormSwitch,
    FormHandSign,
    FormDataSelect,
    FormRadioGroup,
    FormMaterialSelect,
    FormCheckboxGroup,
    FormHandPrice,
    FormHrpMaterialSelect,
    FormReactiveSelect,
    FormItemRender
  },

  props: {
      field: {
        type: Object,
        required: true
      },
      formData: {
        type: Object,
        required: true
      },
      layout:{
        // col  row
        type:String,
        default:'colFormItem'
      },
      childrenLength:{
        // 每行有多少个元素
        type:Number,
        default:1
      }
  },
  watch: {
    field: {
      immediate: true, // 立即执行
      handler(newVal, oldVal) {
        //console.log('formItemRender--field--handler',newVal, oldVal)
      },
      deep: true
    },
    formData: {
      immediate: true, // 立即执行
      handler(newVal, oldVal) {
        // console.log('formItemRender--formData--handler',newVal, oldVal)
      },
      deep: true
    }
  },
  computed: {
  },
  methods: {
  }
}
</script>

<style lang="scss">
.form-item-render {
  width: 100%;
  .form-row {
    width: 100%;

    &.flex-row {
      .row-content {
        display: flex;
        flex-wrap: wrap;
      }

      .flex-item {
        flex: 0 0 50%; /* 每个组件占50%宽度 */
        padding: 0 10rpx;

        &.second {
          /* 第二个组件的样式 */
          padding-left: 0; /* 去掉左边的间距 */
        }
      }
    }
  }
}
.col__item{
  flex: 0 0 100%;
}
.row__item{
  flex: 0 1 auto;
}
.uni-forms {
  background-color: #fff;
  //padding: 0 30rpx;
  display: flex;
  flex-wrap: wrap;
  // margin: 10rpx 0;
}

.uni-forms-item {
  display: flex;
  /* 允许内部内容以 flex 排列 */
  //flex-direction: column; /* 您可能希望内部元素纵向排列 */
  padding: 0 0 10rpx 0;
  margin: 0 0 30rpx 0;
}

.radio-group-item-vertical-direction {
  display: flex;
  justify-content: left;
  align-items: center;
}

.mPlaceholder {
  color: rgb(192, 196, 204);
}

.buttons {
  // display: flex;
  // padding: 20rpx 0 0 20rpx;

  .button {
    background-color: #027eff;
    color: #fff;
    // margin-right: 20rpx;
  }
}

.horizontal-space-around {
  //display: flex;
  //justify-content: space-around;
}

.horizontal-center {
  display: flex;
  justify-content: center;
}

.vertical-top {
  display: flex;
  align-items: flex-start;
}

//.small-star .uni-rate__icon {
//	transform: scale(0.8);
//	/* 缩小星星大小为原来的80% */
//}

.upload-container {
  display: flex;
  align-items: center;
  /* 垂直居中对齐 */
}

.upload-box {
  width: 135px;
  /* 根据需要调整宽度 */
  height: 150px;
  /* 根据需要调整高度 */
  border: 1px dashed #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #999;
  font-size: 24px;
  /* 调整字体大小 */
}

.upload-box text {
  font-size: 45px;
  /* 调整"+"符号的大小 */
  line-height: 150px;
  /* 使"+"符号垂直居中 */
  color: #8c939d;
  /* 可选：调整颜色 */
}

.uploaded-image {
  width: 135px;
  /* 设置与上传框相同的宽度 */
  height: 150px;
  /* 设置固定高度以便于预览 */
  margin-left: 10px;
  /* 设定图片与上传框之间的间距 */
}

.unit {
  display: inline-block;
  margin-left: 10px;
  /* 给单位添加一些间距 */
  color: #999;
  /* 可选：改变单位文字颜色 */
}

.signature-canvas {
  width: 50%;
  /* 80% 宽度，适应父容器 */
  height: 50px;
  /* 设置高度 */
  border: 1px solid #ccc;
  background-color: #f5f5f5;
  margin: 10px 0;
}

.popup-content {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
</style>