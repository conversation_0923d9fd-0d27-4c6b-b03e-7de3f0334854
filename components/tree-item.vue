<template>
	<view>
		<view style="display: flex; line-height: 30px;">
			<view v-if="data.children && data.children.length" @click.stop="toggleNode(data)">
				<uni-icons v-if="data.isOpen" type="bottom" size="16" color="#2A5EFF"></uni-icons>
				<uni-icons v-else type="forward" size="16"></uni-icons>
			</view>
			<view
				style="padding-left: 10px;"
				:class="{ 'node-disabled': onlyLeafSelectable && !isLeafNode, 'node-selectable': !onlyLeafSelectable || isLeafNode }"
				@click.stop="selectNode(data)">
				{{ data.label }}
			</view>
		</view>
		<view v-if="data.children && data.children.length && data.isOpen" style="margin-left: 16px;">
			<tree-item
				v-for="(child, index) in data.children"
				:key="child.id"
				:node="child"
				:only-leaf-selectable="onlyLeafSelectable"
				@clickNode="clickNode">
			</tree-item>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			node: {
				type: Object,
				required: true
			},
			// 是否只能选择叶子节点（最低层节点）
			onlyLeafSelectable: {
				type: Boolean,
				default: false
			}
		},
		name: "TreeItem",
		data() {
			return {
				data: null
			};
		},
		computed: {
			// 判断是否为叶子节点（没有子节点或子节点为空）
			isLeafNode() {
				return !this.data.children || this.data.children.length === 0;
			}
		},
		methods: {
			// 处理折叠/展开
			toggleNode(item) {
				if (item.children && item.children.length) {
					item.isOpen = !item.isOpen;
				}
			},
			// 处理选择节点
			selectNode(item) {
				// 如果启用了只能选择叶子节点模式，且当前节点不是叶子节点，则不触发选择事件
				if (this.onlyLeafSelectable && !this.isLeafNode) {
					return;
				}
				this.$emit('clickNode', item);
			},
			// 向上传递子节点的选择
			clickNode(item) {
				this.$emit('clickNode', item);
			}
		},
		created() {
			this.data = this.node;
		}
	}
</script>

<style>
	/* 节点样式 */
	.node-selectable {
		cursor: pointer;
		color: #303133;
	}

	.node-selectable:hover {
		color: #2A5EFF;
	}

	.node-disabled {
		cursor: not-allowed;
		color: #C0C4CC;
	}

	.node-disabled:hover {
		color: #C0C4CC;
	}
</style>