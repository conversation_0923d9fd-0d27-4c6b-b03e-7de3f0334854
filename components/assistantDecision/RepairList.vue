<template>
  <view class="second-layer">
    <view class="card">
      <view class="card-header">
        <text class="title">报修对应的维修记录</text>
      </view>

      <view class="card-content">
        <!-- 搜索区域 -->
        <!--        <view class="search-box">-->
        <!--          <view class="search-item">-->
        <!--            <text class="label">报修标题</text>-->
        <!--            <input -->
        <!--              type="text"-->
        <!--              v-model="queryParams.recordTitle"-->
        <!--              placeholder="请输入报修标题"-->
        <!--              class="input"-->
        <!--            />-->
        <!--          </view>-->
        <!--          -->
        <!--          <view class="search-item">-->
        <!--            <text class="label">设备名称</text>-->
        <!--            <input -->
        <!--              type="text"-->
        <!--              v-model="queryParams.deviceName"-->
        <!--              placeholder="请输入设备名称"-->
        <!--              class="input"-->
        <!--            />-->
        <!--          </view>-->
        <!--          -->
        <!--          <view class="search-item">-->
        <!--            <text class="label">使用科室</text>-->
        <!--            <input -->
        <!--              type="text"-->
        <!--              v-model="queryParams.deviceUseDeptName"-->
        <!--              placeholder="请输入使用科室"-->
        <!--              class="input"-->
        <!--            />-->
        <!--          </view>-->
        <!--          -->
        <!--          <view class="search-buttons">-->
        <!--            <button @tap="handleQuery">搜索</button>-->
        <!--            <button @tap="resetQuery">重置</button>-->
        <!--          </view>-->
        <!--        </view>-->

        <!-- 列表区域 -->
        <view class="list-box">
          <scroll-view
              scroll-y
              class="plan-list"
              @scrolltolower="loadMore"
          >
            <view v-if="!repairRecordList || repairRecordList.length === 0" class="empty-state">
              <uni-icons type="info" size="64" color="#909399"></uni-icons>
              <text class="empty-text">暂无数据</text>
            </view>
            <view v-else class="plan-cards">
              <view
                  v-for="item in repairRecordList"
                  :key="item.recordId"
                  class="plan-card"
              >
                <view class="plan-title">{{item.recordTitle}}</view>
                <view class="info-row">
                  <view class="info-item">
                    <text class="info-label">报修编号</text>
                    <text class="info-value">{{item.recordId}}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">报修时间</text>
                    <text class="info-value">{{item.recordTime}}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">设备名称</text>
                    <text class="info-value">{{item.deviceName}}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">使用科室</text>
                    <text class="info-value">{{item.deviceUseDeptName}}</text>
                  </view>
                </view>
                <view class="card-footer">
                  <text class="detail-link" @tap="handleDetail(item)">详情</text>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getSameBatchRepairList } from '@/api/portal/statistics'
import { listRepairRecord } from '@/api/repair'

export default {
  name: 'SecondLayer',
  props: {
    batchId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      repairRecordList: []
    }
  },
  computed: {
    // 使用计算属性构造请求参数
    queryParams() {
      return {
        pageNum: 1,
        pageSize: 10,
        assistRepairBatchinfoId: this.batchId
      }
    },
    params(){
      return {
        pageNum: 1660,
        pageSize: 10,
      }
    }
  },
  watch: {
    batchId: {
      handler(newVal) {
        if (newVal) {
          this.getRepairList()
          // this.getAllRepairList()
        }
      },
      immediate: true
    }
  },
  methods: {
    getRepairList() {
      getSameBatchRepairList(this.queryParams).then(res => {
        if (res.code === 200) {
          console.log("RepairList", res)
          this.repairRecordList = res.rows
          console.log(res.rows)
          console.log(this.repairRecordList)
        } else {
          uni.showToast({
            title: res.msg || '获取数据失败',
            icon: 'none'
          })
        }
      })
    },
    // getAllRepairList() {
    //   listRepairRecord(this.params).then(res => {
    //     if (res.code === 200) {
    //       console.log("AllRepairList", res)
    //       this.repairRecordList = res.rows
    //       console.log(res.rows)
    //       console.log(this.repairRecordList)
    //     } else {
    //       uni.showToast({
    //         title: res.msg || '获取数据失败',
    //         icon: 'none'
    //       })
    //     }
    //   })
    // },
    handleQuery() {
      // 实现查询逻辑
    },
    resetQuery() {
      // 实现重置逻辑
    },
    loadMore() {
      // 实现加载更多逻辑
    },
    handleDetail(item) {
      console.log("item",item)
      if (item.flowTaskDto && item.flowTaskDto.procInsId != null) {
        console.log("item1",item)
        var row = item.flowTaskDto
        console.log("row",row)
        let params = {
          procInsId: row.procInsId,
          executionId: row.executionId,
          deployId: row.deployId,
          taskId: row.taskId,
          taskName: row.taskName,
          startUser: row.startUserName + "-" + row.startDeptName,
        }
        console.log("bbb",params);
        uni.navigateTo({
          url: `/pages/myTasks/taskWebView/myProcess?url=${JSON.stringify(params)}`
        })
      } else {
        uni.showToast({
          title: '该记录没有报修流程',
          icon: 'none',
          timeout: 1000
        })
      }
    }
  }
}
</script>

<style lang="scss">
.second-layer {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;

  .card {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .card-header {
      padding: 20rpx;
      border-bottom: 1rpx solid #eee;

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .card-content {
      padding: 20rpx;

      .search-box {
        margin-bottom: 30rpx;

        .search-item {
          margin-bottom: 20rpx;

          .label {
            display: block;
            font-size: 28rpx;
            margin-bottom: 10rpx;
          }

          .input {
            width: 100%;
            height: 70rpx;
            border: 1rpx solid #ddd;
            border-radius: 4rpx;
            padding: 0 20rpx;
            font-size: 28rpx;
          }
        }
      }

      .search-buttons {
        display: flex;
        justify-content: center;
        gap: 20rpx;
        margin-top: 20rpx;

        button {
          min-width: 160rpx;
          font-size: 28rpx;
        }
      }

      .list-box {
        padding: 20rpx;
      }

      .plan-list {
        height: 800rpx;
      }

      .plan-cards {
        display: flex;
        flex-direction: column;
        gap: 24rpx;
      }

      .plan-card {
        background: #ffffff;
        border-radius: 12rpx;
        padding: 24rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
        border: 1rpx solid #ebeef5;
      }

      .plan-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #303133;
        margin-bottom: 20rpx;
        padding-bottom: 16rpx;
        border-bottom: 1rpx solid #ebeef5;
      }

      .info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
      }

      .info-item {
        flex: 1;
        min-width: 200rpx;
        max-width: calc(33.33% - 14rpx);
        display: flex;
        align-items: center;
      }

      .info-label {
        font-size: 26rpx;
        color: #909399;
        min-width: 120rpx;
        margin-right: 12rpx;
      }

      .info-value {
        font-size: 26rpx;
        color: #606266;
        flex: 1;
      }

      .card-footer {
        margin-top: 16rpx;
        padding-top: 16rpx;
        border-top: 1rpx solid #ebeef5;
        text-align: right;

        .detail-link {
          color: #409eff;
          font-size: 26rpx;
        }
      }
    }
  }
}

@media screen and (max-width: 768rpx) {
  .info-item {
    max-width: calc(50% - 10rpx);
  }
}

@media screen and (max-width: 480rpx) {
  .info-item {
    max-width: 100%;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 800rpx;
  
  .empty-text {
    margin-top: 20rpx;
    font-size: 32rpx;
    color: #909399;
  }
}
</style>