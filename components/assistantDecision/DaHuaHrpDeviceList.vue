<template>
  <view class="first-layer">
    <!-- 大华设备管理卡片 -->
    <view class="card" v-if="showType === 'dahua'">
      <view class="card-header">
        <text class="card-title">报修对应的大华设备管理</text>
      </view>
      <view class="card-content">
        <view class="content-column">
          <!-- 第一层：部门树 -->
          <view class="dept-section">
            <view class="dept-title">大华设备管理</view>
            <!-- 部门选择器 -->
            <view class="dept-selector">
              <view class="tree-select" @tap="toggleDeptSelect">
                <text class="selected-text">{{ selectedDeptName || '请选择部门' }}</text>
                <uni-icons type="bottom" size="16" :color="showDeptSelect ? '#2A5EFF' : '#666'"></uni-icons>
              </view>

              <!-- 下拉树状选择框 -->
              <view class="tree-dropdown" v-if="showDeptSelect">
                <uni-easyinput
                    v-model="deptName"
                    placeholder="请输入部门名称"
                    :clearable="true"
                    style="margin-bottom: 10rpx;"
                />
                <scroll-view
                    scroll-y
                    class="tree-scroll"
                >
                  <tree-item
                      v-for="dept in deptTreeList"
                      :key="dept.id"
                      :node="dept"
                      @clickNode="onDeptSelect"
                  ></tree-item>
                </scroll-view>
              </view>
            </view>

            <!-- 第二层：设备信息 -->
            <view class="device-section">
              <uni-segmented-control
                  :current="activeName"
                  :values="['设备', '通道']"
                  @clickItem="handleTabChange"
                  style-type="text"
                  :activeColor="'#2979ff'"
              />
              <view class="search-area">
                <view class="checkbox-wrapper">
                  <checkbox :checked="showChildNodeData" @change="handleCheckboxChange">
                    显示子节点记录
                  </checkbox>
                </view>
                <uni-search-bar
                    :placeholder="activeName === 0 ? '设备名称/设备编号/IP' : '通道名称/通道编号/设备名称'"
                    @confirm="handleSearch"
                    v-model="searchKeyword"
                />
              </view>
              <uni-table :loading="loading">
                <!-- 表格内容根据 activeName 动态显示 -->
              </uni-table>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- HRP资产管理卡片 -->
    <view class="card" v-if="showType === 'hrp'">
      <view class="card-header">
        <text class="card-title">报修对应的资产信息</text>
      </view>
      <view class="card-content">
        <!-- 简单版展示 -->
        <template v-if="contentType === 0">
          <scroll-view scroll-y class="content-scroll" @scrolltolower="loadMore">
            <view class="content-area">
              <view v-if="!hrpList || hrpList.length === 0" class="empty-state">
                <uni-icons type="info" size="64" color="#909399"></uni-icons>
                <text class="empty-text">暂无数据</text>
              </view>
              <view v-else v-for="(item, index) in hrpList" :key="index" class="content-card">
                <view class="card-title">{{item.cardName}}</view>
                <view class="card-info">
                  <view class="info-row">
                    <view class="info-item">
                      <text class="info-label">一级分类</text>
                      <text class="info-value">{{item.class1Name || '暂无'}}</text>
                    </view>
                    <view class="info-item">
                      <text class="info-label">二级分类</text>
                      <text class="info-value">{{item.class2Name || '暂无'}}</text>
                    </view>
                    <view class="info-item">
                      <text class="info-label">三级分类</text>
                      <text class="info-value">{{item.class3Name || '暂无'}}</text>
                    </view>
                    <view class="info-item">
                      <text class="info-label">到期日期</text>
                      <text class="info-value">{{item.expireDate || '暂无'}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </template>

        <!-- 完整版展示 -->
        <template v-else>
          <view class="hrp-detail-full">
            <scroll-view scroll-y class="hrp-detail-scroll" @scrolltolower="loadMore">
              <view v-if="hrpList.length === 0" class="hrp-detail-empty-state">
                <uni-icons type="info" size="64" color="#909399"></uni-icons>
                <text class="hrp-detail-empty-text">暂无数据</text>
              </view>
              <view v-else class="hrp-detail-container" v-for="(item, index) in hrpList" :key="index">
                <!-- 基础信息卡片 -->
                <view class="hrp-detail-card">
                  <view class="hrp-detail-card-header">
                    <uni-icons type="info" size="16"></uni-icons>
                    <text>基本信息</text>
                  </view>
                  <view class="hrp-detail-card-content">
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">资产编号：</text>
                      <text class="hrp-detail-value">{{ item.cardId || '-' }}</text>
                    </view>
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">资产名称：</text>
                      <text class="hrp-detail-value">{{ item.cardName || '-' }}</text>
                    </view>
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">型号规格：</text>
                      <text class="hrp-detail-value">{{ item.cardSpec || '-' }}</text>
                    </view>
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">科室名称：</text>
                      <text class="hrp-detail-value">{{ item.deptName || '-' }}</text>
                    </view>
                  </view>
                </view>

                <!-- 分类信息卡片 -->
                <view class="hrp-detail-card">
                  <view class="hrp-detail-card-header">
                    <uni-icons type="list" size="16"></uni-icons>
                    <text>分类信息</text>
                  </view>
                  <view class="hrp-detail-card-content">
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">一级分类：</text>
                      <text class="hrp-detail-value">{{ item.class1Name || '-' }}</text>
                    </view>
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">二级分类：</text>
                      <text class="hrp-detail-value">{{ item.class2Name || '-' }}</text>
                    </view>
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">三级分类：</text>
                      <text class="hrp-detail-value">{{ item.class3Name || '-' }}</text>
                    </view>
                  </view>
                </view>

                <!-- 时间信息卡片 -->
                <view class="hrp-detail-card">
                  <view class="hrp-detail-card-header">
                    <uni-icons type="calendar" size="16"></uni-icons>
                    <text>时间信息</text>
                  </view>
                  <view class="hrp-detail-card-content">
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">使用日期：</text>
                      <text class="hrp-detail-value">{{ item.useDate || '-' }}</text>
                    </view>
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">到期日期：</text>
                      <text class="hrp-detail-value">{{ item.expireDate || '-' }}</text>
                    </view>
                  </view>
                </view>

                <!-- 其他信息卡片 -->
                <view class="hrp-detail-card">
                  <view class="hrp-detail-card-header">
                    <uni-icons type="more-filled" size="16"></uni-icons>
                    <text>其他信息</text>
                  </view>
                  <view class="hrp-detail-card-content">
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">存放地点：</text>
                      <text class="hrp-detail-value">{{ item.storagePlace || '-' }}</text>
                    </view>
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">设备状态：</text>
                      <text class="hrp-detail-value">{{ item.deviceRealStatus || '-' }}</text>
                    </view>
                    <view class="hrp-detail-item">
                      <text class="hrp-detail-label">备注：</text>
                      <text class="hrp-detail-value">{{ item.remark || '-' }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
import TreeItem from '@/components/tree-item.vue'
import { getSameBatchHrpList } from '@/api/portal/statistics'

export default {
  name: 'FirstLayer',
  components: {
    TreeItem
  },
  props: {
    showType: {
      type: String,
      default: 'dahua'
    },
    batchId: {
      type: Number,
      default: 0
    },
    contentType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      deptName: '',
      showDeptSelect: false,
      selectedDeptName: '',
      deptTreeList: [],
      activeName: 0,
      showChildNodeData: false,
      class1List: [],
      searchKeyword: '',
      hrpList: [],
    }
  },
  computed: {
    queryParams() {
      return {
        pageNum: 1,
        pageSize: 2,
        assistRepairBatchinfoId: this.batchId
      }
    }
  },
  watch: {
    batchId: {
      handler(newVal) {
        if (newVal) {
          this.initData()
        }
      },
      immediate: true
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      if (this.showType === 'dahua') {
        // this.getHrpList()
      } else if (this.showType === 'hrp') {
        // this.getClass1List()
        this.getHrpList()
      }
    },
    getHrpList() {
      getSameBatchHrpList(this.queryParams).then(res => {
        if (res.code === 200) {
          if (this.contentType === 1) {
            // // 获取当前时间
            // const currentDate = new Date().getTime()
            //
            // // 筛选过期设备
            // this.hrpList = res.rows.filter(item => {
            //   if (item.expireDate) {
            //     // 将过期时间转换为时间戳进行比较
            //     const expireDate = new Date(item.expireDate).getTime()
            //     return expireDate < currentDate
            //   }
            //   return false // 如果没有过期时间，则不显示
            // })
            //
            // // 按过期时间排序（可选）
            // this.hrpList.sort((a, b) => {
            //   const dateA = new Date(a.expireDate).getTime()
            //   const dateB = new Date(b.expireDate).getTime()
            //   return dateA - dateB // 从早到晚排序
            // })
            //
            // console.log('过期设备列表:', this.hrpList)
            this.hrpList = res.rows
          } else {
            // contentType = 0 时显示所有设备
            this.hrpList = res.rows
          }
        } else {
          uni.showToast({
            title: res.msg || '获取数据失败',
            icon: 'none'
          })
        }
      })
    },
    handleTabChange(e) {
      this.activeName = e.currentIndex
      this.searchKeyword = ''
      if (this.activeName === 0) {
        this.getDeviceList()
      } else {
        this.getChannelList()
      }
    },
    handleCheckboxChange(e) {
      this.showChildNodeData = e.detail.value
      this.getList()
    },
    handleSearch() {
      if (this.activeName === 0) {
        this.getDeviceList()
      } else {
        this.getChannelList()
      }
    },
    toggleDeptSelect() {
      this.showDeptSelect = !this.showDeptSelect
    },
    onDeptSelect(dept) {
      console.log('Selected dept:', dept)
      if (!dept.children || dept.children.length === 0) {
        this.selectedDeptName = dept.label
        this.showDeptSelect = false
        this.handleNodeClick(dept)
      }
    },
    loadMore() {
      // 实现加载更多逻辑
    }
  }
}
</script>

<style lang="scss">
.first-layer {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  height: 70vh;

  .card {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    height: 100%;

    .card-header {
      padding: 20rpx;
      border-bottom: 1rpx solid #eee;

      .card-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .card-content {
      padding: 20rpx;
      height: calc(100% - 100rpx);

      .content-column {
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        .dept-section {
          position: relative;
          background-color: #fff;
          border-radius: 12rpx;
          padding: 20rpx;

          .dept-title {
            font-size: 32rpx;
            font-weight: bold;
            padding: 20rpx 0;
            text-align: center;
            border-bottom: 1rpx solid #eee;
            margin-bottom: 20rpx;
          }

          .dept-selector {
            position: relative;
            width: 100%;
            z-index: 999;

            .tree-select {
              width: 100%;
              height: 70rpx;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 0 20rpx;
              background: #fff;
              border: 1rpx solid #ddd;
              border-radius: 4rpx;
            }

            .selected-text {
              font-size: 28rpx;
              color: #333;
            }

            .tree-dropdown {
              position: absolute;
              top: 100%;
              left: 0;
              width: 100%;
              background: #fff;
              border: 1rpx solid #ddd;
              border-radius: 4rpx;
              box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
              padding: 20rpx;

              .tree-scroll {
                max-height: 400rpx;
              }
            }
          }
        }

        .device-section {
          background-color: #fff;
          border-radius: 12rpx;
          padding: 20rpx;

          .search-area {
            margin: 20rpx 0;

            .checkbox-wrapper {
              margin-bottom: 20rpx;
            }
          }
        }
      }

      .content-scroll {
        height: 100%;
      }

      .device-info-container {
        padding: 20rpx;
        background-color: #f5f5f5;
        height: 100%;
      }
    }
  }
}

.content-area {
  padding: 20rpx;
}

.content-card {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #ebeef5;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #ebeef5;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.info-item {
  flex: 1;
  min-width: 200rpx;
  max-width: calc(33.33% - 14rpx);
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 26rpx;
  color: #909399;
  min-width: 120rpx;
  margin-right: 12rpx;
}

.info-value {
  font-size: 26rpx;
  color: #606266;
  flex: 1;
}

.content-scroll {
  height: 800rpx;
}

@media screen and (max-width: 768rpx) {
  .info-item {
    max-width: calc(50% - 10rpx);
  }
}

@media screen and (max-width: 480rpx) {
  .info-item {
    max-width: 100%;
  }
}

.device-info-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    padding: 20rpx 0;
    color: #333;
  }

  .info-card {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #eee;
      margin-bottom: 20rpx;

      text {
        font-size: 32rpx;
        font-weight: bold;
        margin-left: 10rpx;
        color: #333;
      }
    }

    .card-content {
      .info-item {
        display: flex;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        .info-label {
          width: 200rpx;
          color: #666;
          font-size: 28rpx;
        }

        .info-value {
          flex: 1;
          color: #333;
          font-size: 28rpx;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 100rpx 0;
  
  .empty-text {
    margin-top: 20rpx;
    font-size: 32rpx;
    color: #909399;
  }
}

.hrp-detail-full {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f5f5;
  z-index: 999;
  padding: 20rpx;
}

.hrp-detail-scroll {
  height: 100vh;
}

.hrp-detail-container {
  padding: 20rpx;
  background-color: #f5f5f5;
}

.hrp-detail-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .hrp-detail-card-header {
    display: flex;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #eee;
    margin-bottom: 20rpx;

    text {
      font-size: 32rpx;
      font-weight: bold;
      margin-left: 10rpx;
      color: #333;
    }
  }

  .hrp-detail-card-content {
    .hrp-detail-item {
      display: flex;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      .hrp-detail-label {
        width: 200rpx;
        color: #666;
        font-size: 28rpx;
      }

      .hrp-detail-value {
        flex: 1;
        color: #333;
        font-size: 28rpx;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        word-break: break-all;
        white-space: pre-wrap;
        line-height: 1.5;
      }
    }
  }
}

.hrp-detail-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 100rpx 0;
  
  .hrp-detail-empty-text {
    margin-top: 20rpx;
    font-size: 32rpx;
    color: #909399;
  }
}
</style>