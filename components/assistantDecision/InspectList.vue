<template>
  <view class="third-layer">
    <!-- 简单版展示 -->
    <template v-if="contentType === 0">
      <view class="card">
        <view class="card-header">
          <text class="title">报修对应的巡检计划</text>
        </view>
        <view class="card-content">
          <scroll-view
              scroll-y
              class="plan-list"
              @scrolltolower="loadMore"
          >
            <view
                v-if="inspectUserList && inspectUserList.length > 0"
                class="plan-cards"
            >
              <view
                  v-for="item in inspectUserList"
                  :key="item.inspectPlanId"
                  class="plan-card"
              >
                <view class="plan-title">{{item.inspectPlanName}}</view>
                <view class="plan-info">
                  <text class="label">巡检ID：</text>
                  <text class="value">{{item.inspectPlanId}}</text>
                </view>
                <view class="plan-info">
                  <text class="label">巡检路线：</text>
                  <text class="value">{{item.inspectRouteName}}</text>
                </view>
                <view class="plan-info">
                  <text class="label">巡检类型：</text>
                  <text class="value">{{item.inspectTypeName}}</text>
                </view>
                <view class="plan-info">
                  <text class="label">开始时间：</text>
                  <text class="value">{{item.inspectStartTime}}</text>
                </view>
                <view class="plan-info">
                  <text class="label">结束时间：</text>
                  <text class="value">{{item.inspectEndTime}}</text>
                </view>
                <view class="card-footer">
                  <text class="detail-link" @tap="handleDetail(item)">详情</text>
                </view>
              </view>
            </view>
            <view v-else class="empty-state">
              <uni-icons type="info" size="64" color="#909399"></uni-icons>
              <text class="empty-text">暂无数据</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </template>

    <!-- 完整版展示 -->
    <template v-else>
      <scroll-view
          scroll-y
          class="plan-list-full"
          @scrolltolower="loadMore"
      >
        <view
            v-if="inspectUserList && inspectUserList.length > 0"
            class="plan-cards-full"
        >
          <view
              v-for="item in inspectUserList"
              :key="item.inspectPlanId"
              class="plan-card-full"
          >
            <view class="plan-title">{{item.inspectPlanName}}</view>
            <view class="plan-info">
              <text class="label">巡检ID：</text>
              <text class="value">{{item.inspectPlanId}}</text>
            </view>
            <view class="plan-info">
              <text class="label">巡检路线：</text>
              <text class="value">{{item.inspectRouteName}}</text>
            </view>
            <view class="plan-info">
              <text class="label">巡检类型：</text>
              <text class="value">{{item.inspectTypeName}}</text>
            </view>
            <view class="plan-info">
              <text class="label">开始时间：</text>
              <text class="value">{{item.inspectStartTime}}</text>
            </view>
            <view class="plan-info">
              <text class="label">结束时间：</text>
              <text class="value">{{item.inspectEndTime}}</text>
            </view>
            <view class="card-footer">
              <text class="detail-link" @tap="handleDetail(item)">详情</text>
            </view>
          </view>
        </view>
        <view v-else class="empty-box">
          <text class="empty-text">暂无数据</text>
        </view>
      </scroll-view>
    </template>
  </view>
</template>

<script>
import { getSameBatchPlanList } from '@/api/portal/statistics'

export default {
  name: 'ThirdLayer',
  props: {
    batchId: {
      type: Number,
      default: 0
    },
    contentType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      inspectUserList: []
    }
  },
  computed: {
    // 使用计算属性构造请求参数
    queryParams() {
      return {
        pageNum: 1,
        pageSize: 10,
        assistRepairBatchinfoId: this.batchId
      }
    }
  },
  watch: {
    batchId: {
      handler(newVal) {
        if (newVal) {
          this.getPlanList()
        }
      },
      immediate: true
    }
  },
  methods: {
    getPlanList() {
      getSameBatchPlanList(this.queryParams).then(res => {
        if (res.code === 200) {
          console.log("PlanList", res)
          this.inspectUserList = res.rows
        }
      })
    },
    handleQuery() {
      // 实现查询逻辑
    },
    resetQuery() {
      // 实现重置逻辑
    },
    loadMore() {
      // 实现加载更多逻辑
    },
    handleDetail(item) {
      console.log("item",item)

      if (item.status == 0){
        const objStr = {
          ...item,
          statusId: 0  // 添加固定的 statusId 值
        }
        this.getInspectLocation(objStr)
      } else {
        const objStr = item;
        this.getInspectLocation(objStr)
      }
    },
    getInspectLocation(inspectInfo) {
      uni.navigateTo({
        url: '/pages/inspect/inspectPlace?inspectInfo='+
            encodeURIComponent(JSON.stringify(inspectInfo))
      })
    },
  }
}
</script>

<style lang="scss">
.third-layer {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  height: 100vh;

  .card {
    background: #fff;
    border-radius: 8rpx;

    .card-header {
      padding: 20rpx 30rpx;
      border-bottom: 1rpx solid #eee;

      .title {
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .card-content {
      padding: 20rpx;

      .search-box {
        margin-bottom: 30rpx;

        .search-item {
          margin-bottom: 20rpx;

          .label {
            display: block;
            font-size: 28rpx;
            margin-bottom: 10rpx;
            color: #666;
          }

          .input {
            width: 100%;
            height: 70rpx;
            border: 1rpx solid #ddd;
            border-radius: 4rpx;
            padding: 0 20rpx;
            font-size: 28rpx;
          }
        }

        .search-buttons {
          display: flex;
          justify-content: center;
          gap: 20rpx;
          margin-top: 20rpx;

          button {
            min-width: 160rpx;
            font-size: 28rpx;
          }
        }
      }

      .plan-list {
        height: 800rpx;
      }

      .plan-cards {
        padding: 10rpx;
      }

      .plan-card {
        background: #f8f8f8;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
      }

      .plan-title {
        font-size: 30rpx;
        font-weight: bold;
        margin-bottom: 16rpx;
        color: #333;
      }

      .plan-info {
        display: flex;
        margin-bottom: 10rpx;
        font-size: 26rpx;
        line-height: 1.5;

        .label {
          color: #666;
          margin-right: 10rpx;
          margin-bottom: 0;
        }

        .value {
          color: #333;
          flex: 1;
        }
      }
      .card-footer {
        margin-top: 16rpx;
        padding-top: 16rpx;
        border-top: 1rpx solid #ebeef5;
        text-align: right;

        .detail-link {
          color: #409eff;
          font-size: 26rpx;
        }
      }
    }
  }

  .empty-box {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300rpx;

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .plan-list-full {
    height: 100%;
    padding: 20rpx;
    background-color: #f5f5f5;
  }

  .plan-cards-full {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  .plan-card-full {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    width: 100%;
    box-sizing: border-box;

    .plan-title {
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
      color: #333;
    }

    .plan-info {
      display: flex;
      margin-bottom: 10rpx;
      font-size: 26rpx;
      line-height: 1.5;

      .label {
        color: #666;
        margin-right: 10rpx;
        margin-bottom: 0;
      }

      .value {
        color: #333;
        flex: 1;
      }
    }

    .card-footer {
      margin-top: 16rpx;
      padding-top: 16rpx;
      border-top: 1rpx solid #ebeef5;
      text-align: right;

      .detail-link {
        color: #409eff;
        font-size: 26rpx;
      }
    }
  }
}

/* 适配小屏幕 */
@media screen and (max-width: 768rpx) {
  .search-box {
    padding: 10rpx;
  }

  .plan-card {
    padding: 15rpx;
  }

  .plan-title {
    font-size: 28rpx;
  }

  .plan-info {
    font-size: 24rpx;
  }
}
</style>