<template>
  <view class="energy-view">
    <view class="card" style="margin: 20rpx 0 0 0">
      <uni-segmented-control :current="activeTabIndex" :values="tabItems" @clickItem="onClickItem" style-type="button" />
      <view class="content">
        <view v-if="activeTabIndex === 0">
          <energy-statistics :energy-type="energyType" :dept-id="currentDeptId" />
        </view>
        <view v-else-if="activeTabIndex === 1">
          <energy-rank :energy-type="energyType" :dept-id="currentDeptId" />
        </view>
        <view v-else-if="activeTabIndex === 2 && deptId === -1">
          <hosp-energy-rank :energy-type="energyType" />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import EnergyStatistics from './viewEnergyData/energyStatistics.vue'
import EnergyRank from './viewEnergyData/energyRank.vue'
import HospEnergyRank from './viewEnergyData/hospEnergyRank.vue'
import { getUserProfile } from '@/api/system/user'

export default {
  name: 'viewEnergy',
  components: {
    EnergyStatistics,
    EnergyRank,
    HospEnergyRank
  },
  props: {
    // 能耗类型  1: '用电' 2: '用水' 3: '用气'
    energyType: {
      type: Number,
      required: false,
      default: 1
    },
    // 部门ID
    deptId: {
      type: Number,
      required: false,
      default: undefined
    }
  },
  data() {
    return {
      activeTabIndex: 0,
      tabItems: [],
      internalDeptId: -1,  // 内部维护的部门ID，默认为-1表示全院
      energyTypeMap: {
        1: '用电',
        2: '用水',
        3: '用气'
      }
    }
  },
  computed: {
    // 获取当前使用的部门ID
    currentDeptId() {
      // 如果外部传入了deptId（不为undefined），则使用外部的，否则使用内部的
      return this.deptId !== undefined ? this.deptId : this.internalDeptId
    },
    // 获取能耗类型文本
    energyTypeText() {
      return this.energyTypeMap[this.energyType] || '用电'
    },
    // 获取部门类型文本
    deptTypeText() {
      return this.currentDeptId === -1 ? '全院' : '科室'
    }
  },
  watch: {
    // 监听外部传入的deptId变化
    deptId: {
      immediate: true,
      handler(newVal) {
        this.updateTabItems()
      }
    }
  },
  created() {
    console.log('viewEnergy created:', {
      deptId: this.deptId,
      energyType: this.energyType
    })
    // 初始化标签页
    this.updateTabItems()
    // 只有在没有外部传入deptId时才获取用户部门信息
    if (this.deptId === undefined) {
      this.getUserDeptInfo()
    }
  },
  methods: {
    onClickItem(e) {
      console.log('tab clicked:', e.currentIndex)
      this.activeTabIndex = e.currentIndex
    },
    // 更新tabItems
    updateTabItems() {
      // 根据当前部门ID来决定显示全院还是科室
      const isHospital = this.currentDeptId === -1
      this.tabItems = [
        `${isHospital ? '全院' : '科室'}${this.energyTypeText}统计`,
        `${isHospital ? '全院' : '科室'}${this.energyTypeText}排名`
      ]
      
      // 只有在全院视图时才显示第三个标签
      if (isHospital) {
        this.tabItems.push(`全院科室${this.energyTypeText}排名`)
      }
    },
    // 获取用户部门信息
    async getUserDeptInfo() {
      try {
        const res = await getUserProfile()
        if (res.code === 200 && res.data) {
          console.log('获取用户部门信息:', res.data)
          // 如果外部没有传入deptId，则使用获取到的部门ID
          if (this.deptId === undefined) {
            this.internalDeptId = res.data.deptId || -1
            this.updateTabItems()
          }
        }
      } catch (error) {
        console.error('获取用户部门信息失败:', error)
      }
    }
  }
}
</script>

<style lang="scss">
.energy-view {
  .card {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 20rpx;
  }

  .content {
    margin-top: 20rpx;
  }
}
</style>
