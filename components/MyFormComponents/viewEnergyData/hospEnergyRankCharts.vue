<template>
  <view class="energy-rank-charts-container">
    <!-- 用电排名图表 -->
    <hosp-energy-rank-chart
        v-if="energyType === 1"
        :energy-type="1"
        :type-id="typeId"
        text-label="用电"
        :start="start"
        :typeTime="typeTime"
        :disableTimeSelection="disableTimeSelection"
        :canvasId="canvasIdPrefix + '_electric'"
    />

    <!-- 用水排名图表 -->
    <hosp-energy-rank-chart
        v-if="energyType === 2"
        :energy-type="2"
        :type-id="typeId"
        text-label="用水"
        :start="start"
        :typeTime="typeTime"
        :disableTimeSelection="disableTimeSelection"
        :canvasId="canvasIdPrefix + '_water'"
    />

    <!-- 用气排名图表 -->
    <hosp-energy-rank-chart
        v-if="energyType === 3"
        :energy-type="3"
        :type-id="typeId"
        text-label="用气"
        :start="start"
        :typeTime="typeTime"
        :disableTimeSelection="disableTimeSelection"
        :canvasId="canvasIdPrefix + '_gas'"
    />
  </view>
</template>

<script>
import hospEnergyRankChart from "./hospEnergyRankChart.vue";

export default {
  name: 'EnergyRankCharts',
  components: {
    hospEnergyRankChart
  },
  props: {
    typeId: {
      type: Number,
      required: false
    },
    energyType: {
      type: Number,
      required: true
    },
    start: {
      type: String,
      default: ''
    },
    typeTime: {
      type: Number,
      default: null
    },
    disableTimeSelection: {
      type: Boolean,
      default: false
    },
    canvasIdPrefix: {
      type: String,
      required: true
    }
  }
}
</script>

<style lang="scss">
.energy-rank-charts-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
</style> 