<template>
  <view class="energy-charts-container">
    <!-- 用电图表 -->
    <energy-statistics-chart
      :energy-type="1"
      :dept-id="deptId"
      :type-id="typeId"
      :time-type="timeType"
      text-label="用电"
    />
    
    <!-- 用水图表 -->
    <energy-statistics-chart
      :energy-type="2"
      :dept-id="deptId"
      :type-id="typeId"
      :time-type="timeType"
      text-label="用水"
    />
    
    <!-- 用气图表 -->
    <energy-statistics-chart
      :energy-type="3"
      :dept-id="deptId"
      :type-id="typeId"
      :time-type="timeType"
      text-label="用气"
    />
  </view>
</template>

<script>
import EnergyStatisticsChart from './energyStatisticsChart.vue'

export default {
  name: 'EnergyStatisticsCharts',
  components: {
    EnergyStatisticsChart
  },
  props: {
    deptId: {
      type: Number,
      required: true
    },
    typeId: {
      type: Number,
      required: false
    },
    timeType: {
      type: Number,
      required: false,
      default: null
    }
  }
}
</script>

<style lang="scss">
.energy-charts-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
</style> 