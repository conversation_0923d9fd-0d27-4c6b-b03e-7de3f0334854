<template>
  <view class="energy-rank-chart">
    <!-- 标签栏 -->
    <view class="card">
      <!-- 标签切换 -->
      <view class="tab-container">
        <view
          v-for="(tab, index) in tabItems"
          :key="index"
          :class="['tab', { 
            active: timeType === index + 2,
            disabled: disableTimeSelection
          }]"
          @tap="!disableTimeSelection && changeTab(index + 2)"
        >{{ tab }}</view>
      </view>

      <!-- 日期选择器 -->
      <view class="date-picker-row" v-if="!disableTimeSelection">
        <view class="date-picker-container">
          <!-- 按日：使用自定义日期选择器 -->
          <template v-if="timeType === 2">
            <day-picker
                :value="dayPickerValue"
                :disableFutureDates="true"
                @change="handleCustomDateChange"
                style="width: 100%"
            />
          </template>

          <!-- 按月：使用自定义月份选择器 -->
          <template v-else-if="timeType === 3">
            <month-picker
                :value="monthPickerValue"
                :disableFutureDates="true"
                @change="handleCustomDateChange"
                style="width: 100%"
            />
          </template>

          <!-- 按年：使用自定义年份选择器 -->
          <template v-else-if="timeType === 4">
            <year-picker
                :value="yearPickerValue"
                :disableFutureDates="true"
                @change="handleCustomDateChange"
                style="width: 100%"
            />
          </template>
        </view>
      </view>

      <!-- 快捷查询按钮 -->
      <view class="quick-query-row" v-if="!disableTimeSelection">
        <view class="quick-query-container" v-if="timeType === 2">
          <button type="primary" size="mini" @tap="setLastDay">前一天</button>
          <button type="primary" size="mini" @tap="setSevenDaysAgo">前七天</button>
          <button type="primary" size="mini" @tap="handleExport">导出</button>
        </view>
        <view class="quick-query-container" v-if="timeType === 3">
          <button type="primary" size="mini" @tap="setOneMonthAgo">前一月</button>
          <button type="primary" size="mini" @tap="setThreeMonthsAgo">前三月</button>
          <button type="primary" size="mini" @tap="handleExport">导出</button>
        </view>
        <view class="quick-query-container" v-if="timeType === 4">
          <button type="primary" size="mini" @tap="setOneYearAgo">前一年</button>
          <button type="primary" size="mini" @tap="setThreeYearsAgo">前三年</button>
          <button type="primary" size="mini" @tap="handleExport">导出</button>
        </view>
      </view>
    </view>

    <!-- 图表块 -->
    <view class="card">
      <!-- 图示容器 -->
      <view class="chart-container" v-if="showChart">
        <view v-if="!chartData || chartData.series.length === 0" class="no-data">
          <text class="iconfont icon-warning"></text>
          <text>暂无数据</text>
        </view>
        <qiun-data-charts
          v-else
          type="bar"
          :opts="chartOpts"
          :chartData="chartData"
          :canvasId="canvasId"
        />
      </view>

      <!-- 表格部分 -->
      <view class="table-container" v-else>
        <view class="table-title">{{ titleMessage.dataListLabel }}</view>
        <view v-if="!tableListData || tableListData.length === 0" class="no-data">
          <text class="iconfont icon-warning"></text>
          <text>暂无数据</text>
        </view>
        <template v-else>
          <view class="table">
            <view class="table-header">
              <view class="table-cell" style="width: 80px; text-align: center;">排名</view>
              <view class="table-cell" v-for="(value, key) in tableListOptions" :key="key" v-if="key !== 'sortOrderNum'">
                {{ value.label }}
              </view>
            </view>
            <view class="table-body">
              <view class="table-row" v-for="(item, index) in paginatedTableData" :key="index">
                <view class="table-cell" style="width: 80px; text-align: center;">{{ item.sortOrderNum }}</view>
                <view class="table-cell" v-for="(value, key) in tableListOptions" :key="key" v-if="key !== 'sortOrderNum'">
                  {{ item[key] }}
                </view>
              </view>
            </view>
          </view>

          <!-- 分页 -->
          <view class="pagination">
            <view class="pagination-item" @tap="handlePrevPage" :class="{ disabled: currentPage === 1 }">上一页</view>
            <view class="pagination-item">{{ currentPage }}</view>
            <view class="pagination-item" @tap="handleNextPage" :class="{ disabled: currentPage >= totalPages }">下一页</view>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
import { qryRankList } from '@/api/HRP/weg';
import QiunDataCharts from "../../../uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue";
import { exportExcel } from '@/utils/exportUtil';
import DayPicker from '@/components/MyFormComponents/time-picker/DayPicker.vue';
import MonthPicker from '@/components/MyFormComponents/time-picker/MonthPicker.vue';
import YearPicker from '@/components/MyFormComponents/time-picker/YearPicker.vue';

export default {
  name: "energyRankChart",
  components: {
    QiunDataCharts,
    DayPicker,
    MonthPicker,
    YearPicker
  },
  props: {
    // 能耗类型  1: '用电' 2: '用水' 3: '用气'
    energyType: {
      type: Number,
      required: false,
    },
    // 用途id
    typeId: {
      type: Number,
      required: false,
    },
    // 科室id
    deptId: {
      type: Number,
      required: true
    },
    textLabel:{
      type: String,
      required: false
    },
    // 开始时间
    start: {
      type: String,
      default: ''
    },
    // 结束时间
    end: {
      type: String,
      default: ''
    },
    // 时间类型
    typeTime: {
      type: Number,
      default: null
    },
    // 是否禁用时间选择
    disableTimeSelection: {
      type: Boolean,
      default: false
    },
    // 图表唯一标识
    canvasId: {
      type: String,
      required: true
    }
  },

  data() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const defaultDate = `${year}-${month}-${day}`;

    return {
      tabItems: ['按日', '按月', '按年'],
      timeType: 2, // 2: 日, 3: 月, 4: 年
      startTime: defaultDate, // 开始日期，默认为当天
      endTime: defaultDate, // 结束日期，默认为当天
      // 查询数据存储
      chartParams: {
        timeType: 2, // 2: 按日，3: 按月，4: 按年
        startTime: defaultDate,
        endTime: defaultDate,
        typeId: this.typeId,
        orgId: this.deptId,
        qryType: this.energyType
      },

      chartData: {
        categories: [],
        series: []
      },
      
      // 图表配置
      chartOpts: {
        color: ['#70BCA4'],
        padding: [15, 15, 0, 15],
        legend: {
          show: true
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          column: {
            width: 30,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08
          }
        }
      },

      // 表格展示选项
      tableListOptions: {
        sortOrderNum: { label: "排名" },
        statisticsTime: { label: "统计时间" },
        usage: { label: "使用量" },
        onYear: { label: "同比增长(%)" },
        chainRate: { label: "环比增长(%)" },
        area: { label: "面积" },
        shareUsage: { label: "均摊量" },
        unitAmt: { label: "单价(元)" },
        allAmt: { label: "总价(元)" }
      },
      // 表格数据
      tableListData: [],

      // 图示与表格展示状态
      showChart: true,

      // 表格分页数据
      currentPage: 1, // 当前页
      pageSize: 10, // 每页显示条数
      total: 0, // 总条数

      // 标题信息
      titleMessage: {
        // 图示标题
        chartLabel: this.textLabel + '排名图表',
        // 列表标题
        dataListLabel: this.textLabel + '排名列表',
        // 导出文件标题
        pgLabel: this.textLabel + '排名',
        // 切换选项标题
        tabDayLabel: '按日',
        tabMonthLabel: '按月',
        tabYearLabel: '按年',
      },

      // 防抖定时器
      debounceTimer: null,
      // 初始化标记
      isInitialized: false
    };
  },
  computed: {
    paginatedTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableListData.slice(start, end);
    },
    totalPages() {
      return Math.ceil(this.total / this.pageSize);
    },
    // 日期选择器的值 - 转换为数组格式（范围选择）
    dayPickerValue() {
      if (!this.startTime || !this.endTime) return [];
      return [this.startTime, this.endTime];
    },
    // 月份选择器的值 - 转换为数组格式
    monthPickerValue() {
      if (!this.startTime || !this.endTime) return [];
      // 提取年月部分
      const startMonth = this.startTime.substring(0, 7); // yyyy-MM
      const endMonth = this.endTime.substring(0, 7);
      return [startMonth, endMonth];
    },
    // 年份选择器的值 - 转换为数组格式
    yearPickerValue() {
      if (!this.startTime || !this.endTime) return [];
      // 提取年份部分
      const startYear = this.startTime.substring(0, 4); // yyyy
      const endYear = this.endTime.substring(0, 4);
      return [startYear, endYear];
    }
  },
  watch: {
    deptId(newVal) {
      this.chartParams.orgId = newVal;
      if (this.isInitialized) {
        this.getChartDataDebounced();
      }
    },
    typeId() {
      this.chartParams.typeId = this.typeId;
      if (this.isInitialized) {
        this.getChartDataDebounced();
      }
    },
    energyType() {
      this.chartParams.qryType = this.energyType;
      if (this.isInitialized) {
        this.getChartDataDebounced();
      }
    },
    // 监听外部传入的时间
    start: {
      handler(newVal) {
        if (newVal) {
          this.startTime = newVal;
          this.chartParams.startTime = newVal;
          if (this.isInitialized) {
            this.getChartDataDebounced();
          }
        }
      },
      immediate: true
    },
    end: {
      handler(newVal) {
        if (newVal) {
          this.endTime = newVal;
          this.chartParams.endTime = newVal;
          if (this.isInitialized) {
            this.getChartDataDebounced();
          }
        }
      },
      immediate: true
    },
    typeTime: {
      handler(newVal) {
        if (newVal) {
          this.timeType = newVal;
          this.chartParams.timeType = newVal;
          if (this.isInitialized) {
            this.getChartDataDebounced();
          }
        }
      },
      immediate: true
    }
  },
  created() {
    if ((this.start && (this.typeTime === null || this.typeTime === undefined || !this.end)) ||
        (this.end && (this.typeTime === null || this.typeTime === undefined || !this.start))) {
      throw new Error('energyRankChart 组件：如果传入了 start 或 end，则必须同时传入 end、start 和 typeTime');
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 如果有传入typeTime、start和end，优先用props
      if (this.typeTime && this.start && this.end) {
        this.timeType = this.typeTime;
        this.chartParams.timeType = this.typeTime;
        this.startTime = this.start;
        this.endTime = this.end;
        this.chartParams.startTime = this.start;
        this.chartParams.endTime = this.end;
      } else {
        // 默认使用当天日期
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        this.startTime = `${year}-${month}-${day}`;
        this.endTime = `${year}-${month}-${day}`;
        this.chartParams.startTime = this.startTime;
        this.chartParams.endTime = this.endTime;
      }

      // 标记组件已初始化，然后调用一次API
      this.isInitialized = true;
      this.getChartData();
    });
  },
  beforeDestroy() {
    // 清除防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
  },
  methods: {
    // 防抖版本的获取图表数据
    getChartDataDebounced() {
      // 清除之前的定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      // 设置新的定时器
      this.debounceTimer = setTimeout(() => {
        this.getChartData();
      }, 300); // 300ms防抖延迟
    },

    // 获取图表数据
    getChartData() {
      // 使用 props 传入的 deptId
      this.chartParams.orgId = this.deptId;
      qryRankList(this.chartParams).then(res => {
        if (res.code === 200) {
          // 数据清空
          this.chartData = {
            categories: [],
            series: []
          };
          this.tableListData = [];

          if (res.data && res.data.length > 0) {
            // 计算排名
            const sortedData = [...res.data].sort((a, b) => {
              // 首先按usage降序排序
              if (b.usage !== a.usage) {
                return b.usage - a.usage;
              }
              // 如果usage相同，添加一个随机因素来打破平局
              return Math.random() - 0.5;
            });

            // 添加排名
            const rankedData = sortedData.map((item, index) => ({
              ...item,
              sortOrderNum: index + 1
            }));

            // 表格数据处理
            this.tableListData = rankedData.map(item => {
              let statisticsTime;
              if (this.timeType === 2) { // 按日
                const calcTime = String(item.calcTime);
                const year = calcTime.substring(0, 4);
                const month = calcTime.substring(4, 6);
                const day = calcTime.substring(6, 8);
                statisticsTime = `${year}-${month}-${day}`;
              } else if (this.timeType === 3) { // 按月
                const calcTime = String(item.calcTime);
                const year = calcTime.substring(0, 4);
                const month = calcTime.substring(4, 6);
                statisticsTime = `${year}-${month}`;
              } else if (this.timeType === 4) { // 按年
                statisticsTime = `${item.calcTime}`; // calcTime 直接就是年份数字
              }

              return {
                sortOrderNum: item.sortOrderNum,
                statisticsTime,
                usage: parseFloat(item.usage),
                onYear: item.onYear !== null ? parseFloat((item.onYear * 100).toFixed(2)) : '-',
                chainRate: item.chainRate !== null ? parseFloat((item.chainRate * 100).toFixed(2)) : '-',
                area: parseFloat(item.area),
                shareUsage: parseFloat(item.shareUsage),
                unitAmt: item.unitAmt !== null ? parseFloat(item.unitAmt) : '-',
                allAmt: item.allAmt !== null ? parseFloat(item.allAmt) : '-'
              };
            });

            this.total = this.tableListData.length;
            this.currentPage = 1;

            // 图表数据处理
            this.chartData = {
              categories: this.tableListData.map(item => item.sortOrderNum.toString()),
              series: [{
                name: '使用量',
                data: this.tableListData.map(item => item.usage)
              }]
            };
          }
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          });
        }
      });
    },

    // 切换标签
    changeTab(tabIndex) {
      this.timeType = tabIndex;
      this.startTime = "";
      this.endTime = "";

      this.$nextTick(() => {
        const now = new Date();
        let year = now.getFullYear();
        let month = String(now.getMonth() + 1).padStart(2, '0');
        let day = String(now.getDate()).padStart(2, '0');

        if (tabIndex === 2) { // 按日
          // 设置为今天
          this.startTime = `${year}-${month}-${day}`;
          this.endTime = `${year}-${month}-${day}`;
        } else if (tabIndex === 3) { // 按月
          // 设置为本月第一天
          this.startTime = `${year}-${month}-01`;
          this.endTime = `${year}-${month}-01`;
        } else if (tabIndex === 4) { // 按年
          // 设置为本年第一天
          this.startTime = `${year}-01-01`;
          this.endTime = `${year}-01-01`;
        }

        this.chartParams.startTime = this.startTime;
        this.chartParams.endTime = this.endTime;
        this.chartParams.timeType = tabIndex;

        this.getChartDataDebounced();
      });
    },

    // 获取日期选择器类型
    getDatePickerType() {
      switch(this.timeType) {
        case 2: return "date";      // 按日
        case 3: return "month";     // 按月
        case 4: return "year";      // 按年
        default: return "date";
      }
    },

    // 获取日期格式
    getDateFormat() {
      switch(this.timeType) {
        case 2: return "yyyy-MM-dd";      // 按日
        case 3: return "yyyy-MM";         // 按月
        case 4: return "yyyy";            // 按年
        default: return "yyyy-MM-dd";
      }
    },

    // 获取值格式
    getValueFormat() {
      switch(this.timeType) {
        case 2: return "yyyy-MM-dd";      // 按日
        case 3: return "yyyy-MM";         // 按月
        case 4: return "yyyy";            // 按年
        default: return "yyyy-MM-dd";
      }
    },

    // 获取日期选择器配置
    getPickerOptions() {
      return (date) => {
        const today = new Date();
        return date > today;
      }
    },

    // 处理日期变化（原有的uni-datetime-picker，现在不再使用）
    handleDateChange(e, type) {
      if (type === 'start') {
        this.startTime = e;
      } else {
        this.endTime = e;
      }

      if (this.startTime && this.endTime) {
        const start = new Date(this.startTime);
        const end = new Date(this.endTime);

        if (start > end) {
          uni.showToast({
            title: "开始时间不能大于结束时间！",
            icon: 'none'
          });
          this.startTime = "";
          this.endTime = "";
          return;
        }

        this.chartParams.startTime = this.startTime;
        this.chartParams.endTime = this.endTime;
        this.getChartDataDebounced();
      }
    },

    // 处理自定义选择器的变化
    handleCustomDateChange(dateRange) {
      if (!dateRange || dateRange.length !== 2) return;

      const [startDate, endDate] = dateRange;
      if (!startDate || !endDate) return;

      switch(this.timeType) {
        case 2: // 按日
          this.startTime = startDate; // 格式: "2024-01-01"
          this.endTime = endDate;
          break;
        case 3: // 按月
          // 月份格式转换为完整日期
          this.startTime = `${startDate}-01`; // "2024-01" -> "2024-01-01"
          // 获取月份的最后一天
          const endYear = parseInt(endDate.split('-')[0]);
          const endMonth = parseInt(endDate.split('-')[1]);
          const lastDay = new Date(endYear, endMonth, 0).getDate();
          this.endTime = `${endDate}-${String(lastDay).padStart(2, '0')}`;
          break;
        case 4: // 按年
          // 年份格式转换为完整日期
          this.startTime = `${startDate}-01-01`; // "2024" -> "2024-01-01"
          this.endTime = `${endDate}-12-31`; // "2024" -> "2024-12-31"
          break;
      }

      // 验证时间范围
      if (this.startTime && this.endTime) {
        const start = new Date(this.startTime);
        const end = new Date(this.endTime);

        if (start > end) {
          uni.showToast({
            title: "开始时间不能大于结束时间！",
            icon: 'none'
          });
          return;
        }

        // 更新查询参数并获取数据
        this.chartParams.startTime = this.startTime;
        this.chartParams.endTime = this.endTime;
        this.getChartDataDebounced();
      }
    },

    // 获取日期选择器宽度
    getDatePickerWidth() {
      switch(this.timeType) {
        case 2: return '160px';  // 按日
        case 3: return '140px';  // 按月
        case 4: return '120px';  // 按年
        default: return '160px';
      }
    },

    // 快捷选项部分

    // 设置前一天的时间范围
    setLastDay() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 1); // 前一天

      this.startTime = startDate.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
      this.endTime = endDate.toISOString().split('T')[0];
      this.updateChartParams();
    },

    // 设置前七天的时间范围
    setSevenDaysAgo() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 7); // 七天前

      this.startTime = startDate.toISOString().split('T')[0];
      this.endTime = endDate.toISOString().split('T')[0];
      this.updateChartParams();
    },

    // 设置前一月的时间范围
    setOneMonthAgo() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(endDate.getMonth() - 1); // 一个月前

      const year = startDate.getFullYear();
      const month = String(startDate.getMonth() + 1).padStart(2, '0');
      const endYear = endDate.getFullYear();
      const endMonth = String(endDate.getMonth() + 1).padStart(2, '0');

      this.startTime = `${year}-${month}-01`;
      this.endTime = `${endYear}-${endMonth}-01`;
      this.updateChartParams();
    },

    // 设置前三月的时间范围
    setThreeMonthsAgo() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(endDate.getMonth() - 3); // 三个月前

      const year = startDate.getFullYear();
      const month = String(startDate.getMonth() + 1).padStart(2, '0');
      const endYear = endDate.getFullYear();
      const endMonth = String(endDate.getMonth() + 1).padStart(2, '0');

      this.startTime = `${year}-${month}-01`;
      this.endTime = `${endYear}-${endMonth}-01`;
      this.updateChartParams();
    },

    // 设置前一年的时间范围
    setOneYearAgo() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setFullYear(endDate.getFullYear() - 1); // 一年前

      const year = startDate.getFullYear();
      const endYear = endDate.getFullYear();

      this.startTime = `${year}-01-01`;
      this.endTime = `${endYear}-01-01`;
      this.updateChartParams();
    },

    // 设置前三年的时间范围
    setThreeYearsAgo() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setFullYear(endDate.getFullYear() - 3); // 三年前

      const year = startDate.getFullYear();
      const endYear = endDate.getFullYear();

      this.startTime = `${year}-01-01`;
      this.endTime = `${endYear}-01-01`;
      this.updateChartParams();
    },

    // 快捷设置更新请求参数并获取数据
    updateChartParams() {
      this.chartParams.startTime = this.startTime;
      this.chartParams.endTime = this.endTime;
      this.getChartDataDebounced();
    },

    // 导出
    async handleExport() {
      try {
        const params = {
          qryType: this.energyType,
          typeId: this.typeId,
          orgId: this.deptId,
          startTime: this.startTime,
          endTime: this.endTime,
          timeType: this.timeType
        };
        
        // 显示加载提示
        uni.showLoading({
          title: '正在导出...',
          mask: true
        });
        
        await exportExcel({
          url: '/ds/weg/appExportRankList',
          params: params
        });

        uni.showToast({
          title: '导出成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('导出失败:', error);
        uni.showToast({
          title: error.message || '导出失败，请重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 分页方法
    handlePrevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },
    handleNextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    }
  },
};
</script>

<style lang="scss">
.energy-rank-chart {
  width: 100%;
  height: auto;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  padding-top: 20rpx;
  padding-bottom: 0;
}

.tab-container {
  display: flex;
  margin-bottom: 10px;
  padding-bottom: 5px;
  justify-content: flex-start;
}

.tab {
  padding: 20rpx 40rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  flex-shrink: 0;

  &.active {
    background-color: #007AFF;
    color: #fff;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.date-picker-row {
  margin-bottom: 10px;
  padding-bottom: 5px;
}

.date-picker-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;

  :deep(.uni-datetime-picker) {
    .uni-datetime-picker-input {
      padding: 8px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      color: #606266;
      text-align: center;
      background-color: #fff;
    }
  }
}

.separator {
  margin: 0 5px;
  white-space: nowrap;
  color: #606266;
}

.quick-query-row {
  margin-bottom: 10px;
  padding-bottom: 5px;
}

.quick-query-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  padding-left: 0;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.table-container {
  width: 100%;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin: 15px 0;
  padding: 8px 0;
  border-bottom: 2px solid #eee;
}

.table {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  
  &:last-child {
    border-bottom: none;
  }
}

.table-cell {
  flex: 1;
  padding: 12px;
  font-size: 14px;
  color: #606266;
  text-align: left;
  word-break: break-all;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 10px;
}

.pagination-item {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  
  &.disabled {
    color: #c0c4cc;
    cursor: not-allowed;
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
  font-size: 14px;
  
  .iconfont {
    font-size: 48px;
    margin-bottom: 10px;
  }
}
</style>
