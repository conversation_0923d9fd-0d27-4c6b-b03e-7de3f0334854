<template>
  <view class="energy-rank">
    <view class="select-container">
      <uni-data-select
          v-model="activeTabId"
          :localdata="selectOptions"
          :clear="false"
          :disabled="disableTypeSelection"
          @change="handleSelectChange"
      />
    </view>

    <view class="content-container">
      <!-- 总统计内容 -->
      <view v-if="activeTabId === 'total'" class="tab-content">
        <hosp-energy-rank-charts
            :type-id="null"
            :energy-type="energyType"
            :start="startTime"
            :typeTime="timeType"
            :disableTimeSelection="disableTypeSelection"
            canvas-id-prefix="total"
        />
      </view>
      <!-- 设备用途内容 -->
      <view v-for="item in deviceUseList"
            :key="item.id"
            v-if="activeTabId === item.id.toString()"
            class="tab-content">
        <hosp-energy-rank-charts
            :type-id="item.id"
            :energy-type="energyType"
            :start="startTime"
            :typeTime="timeType"
            :disableTimeSelection="disableTypeSelection"
            :canvas-id-prefix="'type_' + item.id"
        />
      </view>
    </view>
  </view>
</template>

<script>
import { qryDeviceUse } from "@/api/HRP/weg";
import hospEnergyRankCharts from "./hospEnergyRankCharts.vue";

export default {
  name: 'hospEnergyRank',
  components: {
    hospEnergyRankCharts
  },
  props: {
    // 能耗类型
    energyType: {
      type: Number,
      required: false,
    },
    // 开始时间
    startTime: {
      type: String,
      default: ''
    },
    // 时间类型
    timeType: {
      type: Number,
      default: null
    },
    // 类型ID
    typeId: {
      type: Number,
      default: null
    },
    // 是否禁用用途选择
    disableTypeSelection: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      deviceUseList: [], // 设备用途列表
      activeTabId: 'total', // 当前激活的tab id
      selectOptions: [
        { value: 'total', text: '总排名' }
      ]
    }
  },
  mounted() {
    this.getDeviceUseList()
  },
  methods: {
    // 获取设备用途列表并渲染在左边tab中
    getDeviceUseList() {
      // 获取设备用途列表
      qryDeviceUse({ type: this.energyType }).then(res => {
        console.log('接口返回数据:', res);
        if (res.code === 200) {
          this.deviceUseList = res.data;
          // 更新下拉框选项
          this.updateSelectOptions();
          if (this.deviceUseList.length > 0) {
            this.activeTabId = 'total';
          }
        }
      })
    },
    // 更新下拉框选项
    updateSelectOptions() {
      const options = [
        { value: 'total', text: this.totalTabLabel }
      ];
      // 添加设备用途选项
      this.deviceUseList.forEach(item => {
        options.push({
          value: item.id.toString(),
          text: item.typeName
        });
      });
      this.selectOptions = options;
    },
    // 下拉框选择事件
    handleSelectChange(value) {
      this.activeTabId = value;
    }
  },
  computed: {
    totalTabLabel() {
      switch (this.energyType) {
        case 1: return '用电总排名';
        case 2: return '用水总排名';
        case 3: return '用气总排名';
        default: return '总排名';
      }
    }
  },
  watch: {
    // 监听能耗类型变化,更新总排名标签
    energyType() {
      this.updateSelectOptions();
    }
  }
}
</script>

<style lang="scss">
.energy-rank {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.select-container {
  //padding: 20rpx;
  //margin-top: 0;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #e4e7ed;
}

.content-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  //box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  border-radius: 4px;
  margin: 0 5px;
}

/* 自定义下拉框样式 */
:deep(.uni-data-select) {
  width: 200px;

  .uni-select {
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    &:hover {
      border-color: #c0c4cc;
    }

    &.is-disabled {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}
</style>
