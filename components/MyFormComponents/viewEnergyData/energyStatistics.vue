<template>
  <view class="energy-statistics">
    <uni-data-select
      v-model="activeTabIndex"
      :localdata="selectOptions"
      @change="handleSelectChange"
    />
    <view class="content">
      <view v-if="activeTabIndex === 0">
        <energy-statistics-chart
          :dept-id="deptId"
          :energy-type="energyType"
          :textLabel="totalTabLabel"
          :start="start"
          :end="end"
          :timeType="timeType"
          :typeId="typeId"
          :disableSelection="hasExternalParams"
        />
      </view>
      <view v-else v-for="item in deviceUseList" :key="item.id">
        <energy-statistics-chart
          v-if="activeTabIndex === deviceUseList.indexOf(item) + 1"
          :typeId="item.id"
          :dept-id="deptId"
          :energy-type="energyType"
          :textLabel="item.typeName"
          :start="start"
          :end="end"
          :timeType="timeType"
          :disableSelection="hasExternalParams"
        />
      </view>
    </view>
  </view>
</template>

<script>
import {qryDeviceUse} from "@/api/HRP/weg";
import EnergyStatisticsChart from './energyStatisticsChart.vue';

export default {
  name: 'energyStatistics',
  components: {
    EnergyStatisticsChart
  },
  props: {
    // 能耗类型
    energyType: {
      type: Number,
      required: false,
      default: 1
    },
    // 科室id
    deptId: {
      type: Number,
      required: false,
      default: -1
    },
    // 外部传入的时间范围
    start: {
      type: String,
      required: false,
      default: ''
    },
    end: {
      type: String,
      required: false,
      default: ''
    },
    // 外部传入的时间类型
    timeType: {
      type: Number,
      required: false,
      default: 1
    },
    // 外部传入的用途ID
    typeId: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      deviceUseList: [], // 设备用途列表
      activeTabIndex: 0, // 当前选中的选项索引
      selectOptions: [] // 下拉框选项数组
    }
  },
  beforeCreate() {
    console.log('energyStatistics beforeCreate');
  },
  created() {
    console.log('energyStatistics created:', {
      deptId: this.deptId,
      energyType: this.energyType,
      hasExternalParams: this.hasExternalParams
    });
    
    // 获取设备用途列表
    this.getDeviceUseList();
  },
  beforeMount() {
    console.log('energyStatistics beforeMount');
  },
  mounted() {
    console.log('energyStatistics mounted');
  },
  methods: {
    // 下拉框选择事件
    handleSelectChange(e) {
      console.log('select changed:', e);
      this.activeTabIndex = e;
    },
    // 获取设备用途列表并渲染在下拉框中
    async getDeviceUseList() {
      console.log('getDeviceUseList called with type:', this.energyType);
      try {
        // 获取设备用途列表
        const res = await qryDeviceUse({type: this.energyType});
        console.log('设备用途列表接口返回数据:', res);
        if (res.code === 200) {
          this.deviceUseList = res.data;
          // 更新selectOptions
          this.selectOptions = [
            { value: 0, text: this.totalTabLabel },
            ...this.deviceUseList.map((item, index) => ({
              value: index + 1,
              text: item.typeName
            }))
          ];
          console.log('更新后的selectOptions:', this.selectOptions);
        }
      } catch (error) {
        console.error('获取设备用途列表失败:', error);
      }
    }
  },
  computed: {
    // 判断是否有外部传入的参数
    hasExternalParams() {
      return (
        this.timeType !== null || // 默认值是null
        this.start !== '' || // 默认值是''
        this.end !== '' // 默认值是''
      );
    },
    totalTabLabel() {
      switch (this.energyType) {
        case 1: return '用电总统计';
        case 2: return '用水总统计';
        case 3: return '用气总统计';
        default: return '总统计';
      }
    }
  }
}
</script>

<style lang="scss">
.energy-statistics {
  .content {
    margin-top: 20rpx;
  }
}
</style>
