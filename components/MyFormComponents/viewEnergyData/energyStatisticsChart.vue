<template>
  <view class="chart-container">
    <!-- 标签栏 -->
    <view class="card">
      <view class="tabs">
        <!-- 标签切换 -->
        <view class="tab-container">
          <view
              v-for="(tab, index) in tabItems"
              :key="index"
              :class="['tab', { 
                active: selectedTab === index + 1,
                disabled: isTabDisabled && selectedTab !== index + 1
              }]"
              @click="changeTab(index + 1)"
          >{{ tab }}</view>
        </view>

        <view class="date-picker-wrapper">
          <view class="left-section">
            <view class="date-picker-container">
              <!-- 按小时：保留原有的datetime选择器 -->
              <template v-if="selectedTab === 1">
                <uni-datetime-picker
                    v-model="startTime"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :disabledDate="getPickerOptions()"
                    placeholder="开始时间"
                    @change="handleDateChange"
                    style="width: 400rpx"
                />
                <text class="separator">至</text>
                <uni-datetime-picker
                    v-model="endTime"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :disabledDate="getPickerOptions()"
                    placeholder="结束时间"
                    @change="handleDateChange"
                    style="width: 400rpx"
                />
              </template>

              <!-- 按日：使用自定义日期选择器 -->
              <template v-else-if="selectedTab === 2">
                <day-picker
                    :value="dayPickerValue"
                    :disableFutureDates="true"
                    @change="handleCustomDateChange"
                    style="width: 100%"
                />
              </template>

              <!-- 按月：使用自定义月份选择器 -->
              <template v-else-if="selectedTab === 3">
                <month-picker
                    :value="monthPickerValue"
                    :disableFutureDates="true"
                    @change="handleCustomDateChange"
                    style="width: 100%"
                />
              </template>

              <!-- 按年：使用自定义年份选择器 -->
              <template v-else-if="selectedTab === 4">
                <year-picker
                    :value="yearPickerValue"
                    :disableFutureDates="true"
                    @change="handleCustomDateChange"
                    style="width: 100%"
                />
              </template>
            </view>

            <!-- 快捷查询按钮 -->
            <view class="quick-query-container" v-if="selectedTab === 1">
              <button type="primary" size="mini" @click="setLastTwelveHours">最近12小时</button>
              <button type="primary" size="mini" @click="setTodayHours">今日小时</button>
              <button type="primary" size="mini" @click="handleExport">导出</button>
            </view>
            <view class="quick-query-container" v-if="selectedTab === 2">
              <button type="primary" size="mini" @click="setLastSevenDays">最近7天</button>
              <button type="primary" size="mini" @click="setCurrentMonthRange">本月</button>
              <button type="primary" size="mini" @click="handleExport">导出</button>
            </view>
            <view class="quick-query-container" v-if="selectedTab === 3">
              <button type="primary" size="mini" @click="setLastThreeMonths">前三月</button>
              <button type="primary" size="mini" @click="setLastSixMonths">前六月</button>
              <button type="primary" size="mini" @click="handleExport">导出</button>
            </view>
            <view class="quick-query-container" v-if="selectedTab === 4">
              <button type="primary" size="mini" @click="setLastYear">前一年</button>
              <button type="primary" size="mini" @click="setLastThreeYears">前三年</button>
              <button type="primary" size="mini" @click="handleExport">导出</button>
            </view>
          </view>

          <!-- 切换图表和表格显示 -->
          <!--          <view class="right-section">-->
          <!--            <button -->
          <!--              type="default" -->
          <!--              size="mini" -->
          <!--              @click="handleExport"-->
          <!--              v-if="!showChart"-->
          <!--            >导出</button>-->
          <!--            <switch -->
          <!--              :checked="showChart" -->
          <!--              @change="showChart = $event.detail.value"-->
          <!--              active-text="图示"-->
          <!--              inactive-text="表格"-->
          <!--            />-->
          <!--          </view>-->
        </view>
      </view>
    </view>

    <!-- 图表块 -->
    <view class="card">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <uni-load-more status="loading" />
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-state">
        <text>{{ error }}</text>
        <button type="primary" size="mini" @click="getChartData">重试</button>
      </view>

      <!-- 暂无数据状态 -->
      <view v-else-if="!tableListData.length" class="empty-state">
        <text>暂无数据</text>
      </view>

      <!-- 图示容器 -->
      <view v-else-if="showChart" class="chart-wrapper">
        <qiun-data-charts
          type="column"
          :opts="chartOpts"
          :chartData="chartData"
          :ontouch="true"
          :ontap="true"
          :tooltipShow="true"
          @getIndex="getIndex"
          @touchStart="touchStart"
          @touchMove="touchMove"
          @touchEnd="touchEnd"
        />
      </view>

      <!-- 表格部分 -->
      <view v-else class="table-container">
        <view class="table-title">{{ this.titleMessage.dataListLabel }}</view>

        <scroll-view scroll-y class="table-scroll">
          <view class="table">
            <view class="table-header">
              <view
                  v-for="(value, key) in tableListOptions"
                  :key="key"
                  class="table-cell"
              >{{ value.label }}</view>
            </view>
            <view
                v-for="(item, index) in paginatedTableData"
                :key="index"
                class="table-row"
            >
              <view
                  v-for="(value, key) in tableListOptions"
                  :key="key"
                  class="table-cell"
              >{{ item[key] }}</view>
            </view>
          </view>
        </scroll-view>

        <!-- 分页 -->
        <view class="pagination">
          <button
              type="default"
              size="mini"
              :disabled="currentPage === 1"
              @click="currentPage--"
          >上一页</button>
          <text class="page-info">{{ currentPage }} / {{ Math.ceil(total / pageSize) }}</text>
          <button
              type="default"
              size="mini"
              :disabled="currentPage === Math.ceil(total / pageSize)"
              @click="currentPage++"
          >下一页</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { qryStatisticsByType } from '@/api/HRP/weg';
import QiunDataCharts from "../../../uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue";
import { exportExcel } from '@/utils/exportUtil';
import DayPicker from '@/components/MyFormComponents/time-picker/DayPicker.vue';
import MonthPicker from '@/components/MyFormComponents/time-picker/MonthPicker.vue';
import YearPicker from '@/components/MyFormComponents/time-picker/YearPicker.vue';

export default {
  name: 'energyStatisticsChart',
  components: {
    QiunDataCharts,
    DayPicker,
    MonthPicker,
    YearPicker
  },
  props: {
    // 能耗类型  1: '用电' 2: '用水' 3: '用气'
    energyType: {
      type: Number,
      required: true,
    },
    // 用途id
    typeId: {
      type: Number,
      required: false,
    },
    // 科室id
    deptId: {
      type: Number,
      required: true
    },
    textLabel: {
      type: String,
      required: true
    },
    // 时间类型
    timeType: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      selectedTab: this.timeType || 1, // 1:按小时，2:按日，3:按月，4:按年
      startTime: '', // 开始日期
      endTime: '', // 结束日期
      tabItems: ['按小时', '按日', '按月', '按年'],
      loading: false,
      error: null,
      cWidth: 0,
      cHeight: 0,
      pixelRatio: 1,
      isInitialized: false, // 标记组件是否已初始化
      debounceTimer: null, // 防抖定时器
      // 存储图表数据
      chartData: {
        categories: [],
        series: [
          {
            name: `${this.textLabel}使用量`,
            type: "column",
            data: []
          },
          {
            name: "同比",
            type: "line",
            data: []
          },
          {
            name: "环比",
            type: "line",
            data: []
          }
        ]
      },
      // 表格数据
      tableListData: [],
      showChart: true,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 表格展示选项
      tableListOptions: {
        statisticsTime: { label: "统计时间" },
        usage: { label: "使用量" },
        onYear: { label: "同比增长(%)" },
        chainRate: { label: "环比增长(%)" },
        area: { label: "面积" },
        shareUsage: { label: "均摊量" },
        unitAmt: { label: "单价(元)" },
        allAmt: { label: "总价(元)" }
      },
      // 标题信息
      titleMessage: {
        // 图示标题
        chartLabel: '',
        // 列表标题
        dataListLabel: '',
        // 导出文件标题
        pgLabel: '',
        // 切换选项标题
        tabTimeLabel: '按小时',
        tabDayLabel: '按日',
        tabMonthLabel: '按月',
        tabYearLabel: '按年',
      },
      // 添加图表配置
      chartOpts: {
        color: ['#1890FF', '#91CB74', '#FAC858'],
        padding: [15, 15, 0, 15],
        xAxis: {
          disableGrid: true,
          itemCount: 5,
          scrollShow: true,
          scrollAlign: 'left',
          scrollBackgroundColor: '#F5F5F5',
          scrollColor: '#1890FF',
          boundaryGap: false
        },
        yAxis: [{
          title: '使用量',
          titleFontSize: 12,
          titleFontColor: '#666666',
          min: 0,
          format: '0',
          position: 'left'
        }, {
          title: '增长率(%)',
          titleFontSize: 12,
          titleFontColor: '#666666',
          min: 0,
          format: '0%',
          position: 'right'
        }],
        extra: {
          column: {
            width: 30,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08
          },
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow'
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: true,
            borderWidth: 0,
            borderRadius: 4,
            borderColor: '#000000',
            borderOpacity: 0.7,
            bgColor: '#000000',
            bgOpacity: 0.7,
            gridType: 'solid',
            dashLength: 4,
            gridColor: '#CCCCCC',
            fontColor: '#FFFFFF',
            splitLine: true,
            horizentalLine: true,
            xAxisLabel: true,
            yAxisLabel: true,
            labelBgColor: '#FFFFFF',
            labelBgOpacity: 0.7,
            labelFontColor: '#666666'
          }
        },
        dataLabel: false,
        dataPointShape: true,
        enableScroll: true,
        touchMoveLimit: 60,
        enableMarkLine: true,
        markLine: {
          type: 'dash',
          dashLength: 5,
          data: [{
            value: 0,
            lineColor: '#a8a8a8'
          }]
        }
      }
    };
  },
  computed: {
    // 是否禁用tab切换
    isTabDisabled() {
      return this.timeType !== null && this.timeType !== 1;
    },
    // 图表参数计算属性
    chartParams() {
      return {
        timeType: this.selectedTab,
        startTime: this.startTime,
        endTime: this.endTime,
        typeId: this.typeId,
        orgId: this.deptId,
        qryType: this.energyType
      }
    },
    // 分页数据
    paginatedTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableListData.slice(start, end);
    },
    // 日期选择器的值 - 转换为数组格式
    dayPickerValue() {
      if (!this.startTime || !this.endTime) return [];
      // 提取日期部分（去掉时间）
      const startDate = this.startTime.split(' ')[0];
      const endDate = this.endTime.split(' ')[0];
      return [startDate, endDate];
    },
    // 月份选择器的值 - 转换为数组格式
    monthPickerValue() {
      if (!this.startTime || !this.endTime) return [];
      // 提取年月部分
      const startMonth = this.startTime.substring(0, 7); // yyyy-MM
      const endMonth = this.endTime.substring(0, 7);
      return [startMonth, endMonth];
    },
    // 年份选择器的值 - 转换为数组格式
    yearPickerValue() {
      if (!this.startTime || !this.endTime) return [];
      // 提取年份部分
      const startYear = this.startTime.substring(0, 4); // yyyy
      const endYear = this.endTime.substring(0, 4);
      return [startYear, endYear];
    }
  },
  watch: {
    // 监听外部timeType变化
    timeType: {
      handler(newVal) {
        if (newVal !== null) {
          this.selectedTab = newVal;
          this.setDefaultTimeRange();
          // 只有在组件初始化完成后才调用API
          if (this.isInitialized) {
            this.getChartDataDebounced();
          }
        }
      },
      immediate: true
    },
    // 监听能耗类型变化
    energyType: {
      handler(newVal) {
        console.log('energyType changed in chart:', newVal);
        // 只有在组件初始化完成后才调用API
        if (this.isInitialized) {
          this.getChartDataDebounced();
        }
      },
      immediate: true
    },
    // 监听科室ID变化
    deptId: {
      handler(newVal) {
        console.log('deptId changed:', newVal);
        // 只有在组件初始化完成后才调用API
        if (this.isInitialized) {
          this.getChartDataDebounced();
        }
      },
      immediate: true
    },
    // 监听时间变化
    startTime: {
      handler(newVal) {
        if (!newVal) {
          this.endTime = '';
        }
      }
    },
    endTime: {
      handler(newVal) {
        if (!newVal) {
          this.startTime = '';
        }
      }
    }
  },
  beforeCreate() {
    console.log('energyStatisticsChart beforeCreate');
  },
  created() {
    console.log('energyStatisticsChart created:', {
      deptId: this.deptId,
      energyType: this.energyType,
      typeId: this.typeId
    });
  },
  beforeMount() {
    console.log('energyStatisticsChart beforeMount');
  },
  onReady() {
    console.log('energyStatisticsChart onReady');
    this.cWidth = uni.upx2px(750);
    this.cHeight = uni.upx2px(500);
    // 设置默认时间范围
    this.setDefaultTimeRange();
    // 标记组件已初始化，然后调用一次API
    this.isInitialized = true;
    this.getChartData();
  },
  mounted() {
    console.log('energyStatisticsChart mounted:', {
      deptId: this.deptId,
      energyType: this.energyType,
      typeId: this.typeId,
      chartParams: this.chartParams
    });
  },
  beforeDestroy() {
    // 清除定时器
    if (this.resizeTimer) {
      clearInterval(this.resizeTimer);
    }
    // 清除防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
  },
  methods: {
    // 验证时间范围
    validateTimeRange(start, end) {
      if (!start || !end) return false;
      const startDate = new Date(start.replace(' ', 'T'));
      const endDate = new Date(end.replace(' ', 'T'));
      if (startDate > endDate) {
        uni.showToast({
          title: "开始时间不能大于结束时间！",
          icon: 'none'
        });
        return false;
      }
      return true;
    },

    // 防抖版本的获取图表数据
    getChartDataDebounced() {
      // 清除之前的定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      // 设置新的定时器
      this.debounceTimer = setTimeout(() => {
        this.getChartData();
      }, 300); // 300ms防抖延迟
    },

    // 获取图表数据
    async getChartData() {
      if (!this.validateTimeRange(this.startTime, this.endTime)) {
        return;
      }

      // 防止重复请求
      if (this.loading) {
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        // 使用 props 传入的 deptId
        this.chartParams.orgId = this.deptId;
        const res = await qryStatisticsByType(this.chartParams);
        console.log('API response:', res);
        
        if (res.code === 200) {
          await this.processChartData(res.data);
        } else {
          this.handleError(res.msg || '获取数据失败');
        }
      } catch (error) {
        this.handleError(error);
      } finally {
        this.loading = false;
      }
    },

    // 处理图表数据
    async processChartData(data) {
      const { categories, usageData, onYearData, chainRateData } = this.formatChartData(data);
      await this.updateChart(categories, usageData, onYearData, chainRateData);
      this.updateTableData(data);
    },

    // 格式化图表数据
    formatChartData(data) {
      const categories = [];
      const usageData = [];
      const onYearData = [];
      const chainRateData = [];

      data.forEach(item => {
        const timeKey = this.formatTimeKey(item.calcTime);
        categories.push(timeKey);
        usageData.push(item.usage);
        onYearData.push(item.onYear !== null ? item.onYear * 100 : 0);
        chainRateData.push(item.chainRate !== null ? item.chainRate * 100 : 0);
      });

      return { categories, usageData, onYearData, chainRateData };
    },

    // 格式化时间键
    formatTimeKey(calcTime) {
      switch(this.selectedTab) {
        case 1: // 按小时
          return this.formatHourTime(calcTime);
        case 2: // 按日
          return this.formatDayTime(calcTime);
        case 3: // 按月
          return this.formatMonthTime(calcTime);
        case 4: // 按年
          return calcTime;
        default:
          return calcTime;
      }
    },

    // 格式化小时时间
    formatHourTime(calcTime) {
      const date = new Date(calcTime);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00:00`;
    },

    // 格式化日期时间
    formatDayTime(calcTime) {
      const calcTimeStr = String(calcTime);
      const year = calcTimeStr.substring(0, 4);
      const month = calcTimeStr.substring(4, 6);
      const day = calcTimeStr.substring(6, 8);
      return `${year}-${month}-${day}`;
    },

    // 格式化月份时间
    formatMonthTime(calcTime) {
      const calcTimeStr = String(calcTime);
      const year = calcTimeStr.substring(0, 4);
      const month = calcTimeStr.substring(4, 6);
      return `${year}-${month}`;
    },

    // 更新表格数据
    updateTableData(data) {
      this.tableListData = data.map(item => {
        let statisticsTime = this.formatTimeKey(item.calcTime);
        return {
          statisticsTime,
          usage: item.usage.toFixed(4),
          onYear: item.onYear !== null ? (item.onYear * 100).toFixed(2) : '-',
          chainRate: item.chainRate !== null ? (item.chainRate * 100).toFixed(2) : '-',
          area: item.area.toFixed(4),
          shareUsage: item.shareUsage.toFixed(4),
          unitAmt: item.unitAmt !== null ? item.unitAmt.toFixed(2) : '-',
          allAmt: item.allAmt !== null ? item.allAmt.toFixed(2) : '-'
        };
      }).sort((a, b) => {
        return new Date(a.statisticsTime) - new Date(b.statisticsTime);
      });

      this.total = this.tableListData.length;
      this.currentPage = 1;
    },

    // 错误处理
    handleError(error) {
      this.error = typeof error === 'string' ? error : '获取数据出错';
      console.error('获取图表数据失败:', error);
      uni.showToast({
        title: this.error,
        icon: 'none',
        duration: 2000
      });
    },

    // 切换标签
    changeTab(tabIndex) {
      if (this.isTabDisabled) {
        return;
      }
      this.selectedTab = tabIndex;
      this.setDefaultTimeRange();
      this.getChartDataDebounced();
    },

    // 设置默认时间范围
    setDefaultTimeRange() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');

      switch(this.selectedTab) {
        case 1: // 按小时
          this.startTime = `${year}-${month}-${day} 00:00:00`;
          this.endTime = `${year}-${month}-${day} ${hour}:00:00`;
          break;
        case 2: // 按日
          this.startTime = `${year}-${month}-${day}`;
          this.endTime = `${year}-${month}-${day}`;
          break;
        case 3: // 按月
          this.startTime = `${year}-${month}-01`;
          this.endTime = `${year}-${month}-${day}`;
          break;
        case 4: // 按年
          this.startTime = `${year}-01-01`;
          this.endTime = `${year}-12-31`;
          break;
      }
    },

    // 处理日期变化
    handleDateChange() {
      if (this.startTime && this.endTime) {
        if (this.validateTimeRange(this.startTime, this.endTime)) {
          this.formatDates();
          this.getChartDataDebounced();
        }
      }
    },

    // 处理自定义选择器的变化
    handleCustomDateChange(dateRange) {
      if (!dateRange || dateRange.length !== 2) return;

      const [startDate, endDate] = dateRange;
      if (!startDate || !endDate) return;

      switch(this.selectedTab) {
        case 2: // 按日
          this.startTime = startDate; // 格式: "2024-01-01"
          this.endTime = endDate;
          break;
        case 3: // 按月
          // 月份格式转换为完整日期
          this.startTime = `${startDate}-01`; // "2024-01" -> "2024-01-01"
          // 获取月份的最后一天
          const endYear = parseInt(endDate.split('-')[0]);
          const endMonth = parseInt(endDate.split('-')[1]);
          const lastDay = new Date(endYear, endMonth, 0).getDate();
          this.endTime = `${endDate}-${String(lastDay).padStart(2, '0')}`;
          break;
        case 4: // 按年
          // 年份格式转换为完整日期
          this.startTime = `${startDate}-01-01`; // "2024" -> "2024-01-01"
          this.endTime = `${endDate}-12-31`; // "2024" -> "2024-12-31"
          break;
      }

      // 验证并获取数据
      if (this.validateTimeRange(this.startTime, this.endTime)) {
        this.getChartDataDebounced();
      }
    },

    // 格式化日期
    formatDates() {
      if (!this.startTime || !this.endTime) return;

      switch(this.selectedTab) {
        case 1: // 按小时
          if (!this.startTime.includes(':')) {
            this.startTime = `${this.startTime} 00:00:00`;
          }
          if (!this.endTime.includes(':')) {
            this.endTime = `${this.endTime} 23:59:59`;
          }
          break;
        case 2: // 按日
          this.startTime = this.startTime.split(' ')[0];
          this.endTime = this.endTime.split(' ')[0];
          break;
        case 3: // 按月
          this.startTime = `${this.startTime.substring(0, 7)}-01`;
          this.endTime = `${this.endTime.substring(0, 7)}-01`;
          break;
        case 4: // 按年
          this.startTime = `${this.startTime.substring(0, 4)}-01-01`;
          this.endTime = `${this.endTime.substring(0, 4)}-01-01`;
          break;
      }
    },

    // 获取日期选择器配置
    getPickerOptions() {
      return (date) => {
        return date.getTime() > Date.now();
      };
    },

    // 获取日期选择器类型
    getDatePickerType() {
      switch(this.selectedTab) {
        case 1: return "datetime";
        case 2: return "date";
        case 3: return "month";
        case 4: return "year";
      }
    },

    // 获取显示的日期格式
    getDateFormat() {
      switch(this.selectedTab) {
        case 1: return "yyyy-MM-dd HH:mm:ss";
        case 2: return "yyyy-MM-dd";
        case 3: return "yyyy-MM";
        case 4: return "yyyy";
      }
    },

    // 获取实际存储的日期格式
    getValueFormat() {
      switch(this.selectedTab) {
        case 1: return "yyyy-MM-dd HH:mm:ss";
        case 2: return "yyyy-MM-dd";
        case 3: return "yyyy-MM-01";
        case 4: return "yyyy-01-01";
      }
    },

    // 获取日期选择器宽度
    getDatePickerWidth() {
      switch(this.selectedTab) {
        case 1: return '400rpx';
        case 2: return '320rpx';
        case 3: return '280rpx';
        case 4: return '240rpx';
      }
    },

    // 快捷选项方法
    setLastTwelveHours() {
      const now = new Date();
      const twelveHoursAgo = new Date(now);
      twelveHoursAgo.setHours(now.getHours() - 12);

      this.startTime = this.formatDateTime(twelveHoursAgo);
      this.endTime = this.formatDateTime(now);
      this.getChartDataDebounced();
    },

    setTodayHours() {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      this.startTime = this.formatDateTime(today);
      this.endTime = this.formatDateTime(now);
      this.getChartDataDebounced();
    },

    setLastSevenDays() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 6);

      this.startTime = this.formatDate(startDate);
      this.endTime = this.formatDate(endDate);
      this.getChartDataDebounced();
    },

    setCurrentMonthRange() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');

      this.startTime = `${year}-${month}-01`;
      this.endTime = `${year}-${month}-${day}`;
      this.getChartDataDebounced();
    },

    setLastThreeMonths() {
      const today = new Date();
      today.setMonth(today.getMonth() - 3);
      const startYear = today.getFullYear();
      const startMonth = String(today.getMonth() + 1).padStart(2, '0');

      this.startTime = `${startYear}-${startMonth}-01`;

      const currentYear = new Date().getFullYear();
      const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
      this.endTime = `${currentYear}-${currentMonth}-01`;
      this.getChartDataDebounced();
    },

    setLastSixMonths() {
      const today = new Date();
      today.setMonth(today.getMonth() - 6);
      const startYear = today.getFullYear();
      const startMonth = String(today.getMonth() + 1).padStart(2, '0');

      this.startTime = `${startYear}-${startMonth}-01`;

      const currentYear = new Date().getFullYear();
      const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
      this.endTime = `${currentYear}-${currentMonth}-01`;
      this.getChartDataDebounced();
    },

    setLastYear() {
      const currentYear = new Date().getFullYear();
      const lastYear = currentYear - 1;

      this.startTime = `${lastYear}-01-01`;
      this.endTime = `${currentYear}-01-01`;
      this.getChartDataDebounced();
    },

    setLastThreeYears() {
      const currentYear = new Date().getFullYear();
      const lastThreeYears = currentYear - 3;

      this.startTime = `${lastThreeYears}-01-01`;
      this.endTime = `${currentYear}-01-01`;
      this.getChartDataDebounced();
    },

    // 格式化日期时间
    formatDateTime(date) {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00:00`;
    },

    // 格式化日期
    formatDate(date) {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 修改更新图表方法
    updateChart(categories, usageData, onYearData, chainRateData) {
      this.chartData = {
        categories: categories,
        series: [{
          name: `${this.textLabel}使用量`,
          data: usageData,
          type: 'column',
          color: '#1890FF',
          yAxisIndex: 0
        }, {
          name: '同比',
          data: onYearData,
          type: 'line',
          color: '#91CB74',
          yAxisIndex: 1
        }, {
          name: '环比',
          data: chainRateData,
          type: 'line',
          color: '#FAC858',
          yAxisIndex: 1
        }]
      };
    },

    // 修改触摸事件处理方法
    touchStart(e) {
      // 事件由组件内部处理
    },

    touchMove(e) {
      // 事件由组件内部处理
    },

    touchEnd(e) {
      // 事件由组件内部处理
    },

    // 添加获取索引方法
    getIndex(e) {
      // 处理点击事件
      console.log('图表点击事件:', e);
    },

    // 导出
    async handleExport() {
      if (!this.validateTimeRange(this.startTime, this.endTime)) {
        return;
      }
      
      try {
        const params = {
          qryType: this.energyType,
          typeId: this.typeId,
          orgId: this.deptId,
          startTime: this.startTime,
          endTime: this.endTime,
          timeType: this.selectedTab
        };
        
        // 显示加载提示
        uni.showLoading({
          title: '正在导出...',
          mask: true
        });
        
        await exportExcel({
          url: '/ds/weg/appExportStatisticByType',
          params: params
        });

        uni.showToast({
          title: '导出成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('导出失败:', error);
        uni.showToast({
          title: error.message || '导出失败，请重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        // 隐藏加载提示
        uni.hideLoading();
      }
    }
  }
};
</script>

<style lang="scss">
.chart-container {
  .card {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
  }

  .tabs {
    .tab-container {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 20rpx;
      margin-bottom: 20rpx;
      flex-wrap: nowrap;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      padding: 10rpx 0;
    }

    .tab {
      padding: 20rpx 40rpx;
      background-color: #f5f5f5;
      border-radius: 10rpx;
      font-size: 28rpx;
      color: #333;
      white-space: nowrap;
      flex-shrink: 0;

      &.active {
        background-color: #007AFF;
        color: #fff;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }

  .date-picker-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .left-section {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
    }

    .date-picker-container {
      display: flex;
      align-items: center;
      gap: 20rpx;
    }

    .separator {
      color: #666;
    }

    .quick-query-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10rpx;
    }

    .right-section {
      display: flex;
      align-items: center;
      gap: 20rpx;
      margin-top: 20rpx;
    }
  }

  .chart-wrapper {
    width: 100%;
    height: 600rpx;
    display: flex;
    justify-content: center;
  }

  .charts {
    width: 100%;
    height: 100%;
  }

  .table-container {
    .table-title {
      font-size: 32rpx;
      font-weight: bold;
      text-align: center;
      margin: 20rpx 0;
      padding: 20rpx 0;
      border-bottom: 2rpx solid #eee;
    }

    .table-scroll {
      height: 600rpx;
    }

    .table {
      width: 100%;
      border-collapse: collapse;

      .table-header {
        background-color: #f5f5f5;
        font-weight: bold;
      }

      .table-row {
        display: flex;
        border-bottom: 1rpx solid #eee;

        &:nth-child(even) {
          background-color: #f9f9f9;
        }
      }

      .table-cell {
        flex: 1;
        padding: 20rpx;
        text-align: center;
        font-size: 28rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20rpx;
      gap: 20rpx;

      .page-info {
        color: #666;
        font-size: 28rpx;
      }
    }
  }

  .loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .error-state {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 400px;

    text {
      margin-bottom: 20rpx;
    }

    button {
      margin-top: 20rpx;
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;

    text {
      color: #999;
      font-size: 28rpx;
    }
  }
}
</style>
