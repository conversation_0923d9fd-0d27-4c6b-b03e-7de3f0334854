<template>
  <view class="energy-rank-charts-container">
    <!-- 用电排名图表 -->
    <energy-rank-chart
      v-if="energyType === 1"
      :energy-type="1"
      :dept-id="deptId"
      :type-id="typeId"
      text-label="用电"
      :start="start"
      :end="end"
      :typeTime="typeTime"
      :disableTimeSelection="disableTimeSelection"
      canvasId="energyRankChart1"
    />
    
    <!-- 用水排名图表 -->
    <energy-rank-chart
      v-if="energyType === 2"
      :energy-type="2"
      :dept-id="deptId"
      :type-id="typeId"
      text-label="用水"
      :start="start"
      :end="end"
      :typeTime="typeTime"
      :disableTimeSelection="disableTimeSelection"
      canvasId="energyRankChart2"
    />
    
    <!-- 用气排名图表 -->
    <energy-rank-chart
      v-if="energyType === 3"
      :energy-type="3"
      :dept-id="deptId"
      :type-id="typeId"
      text-label="用气"
      :start="start"
      :end="end"
      :typeTime="typeTime"
      :disableTimeSelection="disableTimeSelection"
      canvasId="energyRankChart3"
    />
  </view>
</template>

<script>
import EnergyRankChart from './energyRankChart.vue'

export default {
  name: 'EnergyRankCharts',
  components: {
    EnergyRankChart
  },
  props: {
    deptId: {
      type: Number,
      required: true
    },
    typeId: {
      type: Number,
      required: false
    },
    energyType: {
      type: Number,
      required: true
    },
    start: {
      type: String,
      default: ''
    },
    end: {
      type: String,
      default: ''
    },
    typeTime: {
      type: Number,
      default: null
    },
    disableTimeSelection: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss">
.energy-rank-charts-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
</style> 