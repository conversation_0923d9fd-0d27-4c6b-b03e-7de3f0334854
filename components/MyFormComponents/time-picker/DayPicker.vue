<template>
  <view class="day-picker-wrapper">
    <!-- 显示选择的日期范围的输入框 -->
    <view class="date-input" @click="showPopup">
      <view class="date-display">
        <text v-if="!singleMode && dateRange.length === 2 && dateRange[0] && dateRange[1]">
          {{ formatDisplayDate(dateRange[0]) }} 至 {{ formatDisplayDate(dateRange[1]) }}
        </text>
        <text v-else-if="singleMode && dateRange[0]">
          {{ formatDisplayDate(dateRange[0]) }}
        </text>
        <text v-else class="placeholder">{{ singleMode ? '请选择日期' : '请选择日期范围' }}</text>
      </view>
      <uni-icons v-if="clearIcon && ((singleMode && dateRange[0]) || (!singleMode && dateRange.length === 2 && dateRange[0] && dateRange[1]))" 
                type="clear" 
                size="16" 
                color="#999" 
                @click.stop="clearDate">
      </uni-icons>
      <uni-icons type="calendar" size="16" color="#666"></uni-icons>
    </view>
    
    <!-- 弹出层 -->
    <uni-popup ref="popup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <view class="left-btns">
            <text class="cancel-btn" @click="hidePopup">取消</text>
            <text class="clear-btn" @click="clearInPopup">清空</text>
          </view>
          <text class="title">{{ singleMode ? '选择日期' : '选择日期范围' }}</text>
          <text class="confirm-btn" @click="confirmSelection">确定</text>
        </view>
        
        <!-- 日期选择器内容 -->
        <view class="day-picker-container">
          <view class="header">
            <text class="arrow" @click="prevMonth">«</text>
            <text class="month-title">{{ currentYear }}年{{ currentMonth }}月</text>
            <text class="arrow" @click="nextMonth">»</text>
          </view>
          <view class="weekdays">
            <text v-for="(day, index) in weekDays" :key="index" class="weekday">{{ day }}</text>
          </view>
          <view class="days-grid">
            <view 
              v-for="(day, index) in days" 
              :key="index"
              :class="['day-item', { 
                'empty': !day,
                'start-day': isStartDay(day),
                'end-day': isEndDay(day),
                'in-range': isInRange(day),
                'disabled': isDayDisabled(day),
                'today': isToday(day)
              }]"
              @click="day && !isDayDisabled(day) && selectDay(day)"
            >
              {{ day || '' }}
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'DayPicker',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    minDate: {
      type: String,
      default: ''
    },
    maxDate: {
      type: String,
      default: ''
    },
    clearIcon: {
      type: Boolean,
      default: true
    },
    returnType: {
      type: String,
      default: 'string'
    },
    disableFutureDates: {
      type: Boolean,
      default: false
    },
    singleMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const today = new Date();
    return {
      currentYear: today.getFullYear(),
      currentMonth: today.getMonth() + 1,
      weekDays: ['日', '一', '二', '三', '四', '五', '六'],
      startDate: null,
      endDate: null,
      today: {
        year: today.getFullYear(),
        month: today.getMonth() + 1,
        day: today.getDate()
      },
      dateRange: this.value || [],
      tempDateRange: [] // 用于存储临时选择的日期范围
    }
  },
  computed: {
    days() {
      const days = [];
      const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1);
      const lastDay = new Date(this.currentYear, this.currentMonth, 0);
      const daysInMonth = lastDay.getDate();
      
      // 填充月初空白天数
      const firstDayOfWeek = firstDay.getDay();
      for (let i = 0; i < firstDayOfWeek; i++) {
        days.push(null);
      }
      
      // 添加当月所有天数
      for (let i = 1; i <= daysInMonth; i++) {
        days.push(i);
      }
      
      // 填充月末空白天数，使总数为42（6行7列）
      const remainingDays = 42 - days.length;
      for (let i = 0; i < remainingDays; i++) {
        days.push(null);
      }
      
      return days;
    }
  },
  watch: {
    value(newVal) {
      this.dateRange = newVal || [];
      this.initDateFromRange();
    }
  },
  created() {
    this.initDateFromRange();
  },
  methods: {
    initDateFromRange() {
      // 初始化startDate和endDate
      if (this.dateRange && this.dateRange.length >= 1) {
        const startDate = this.dateRange[0];
        if (startDate) {
          const [startYear, startMonth, startDay] = startDate.split('-').map(Number);
          this.currentYear = startYear;
          this.currentMonth = startMonth;
          this.startDate = {
            year: startYear,
            month: startMonth,
            day: startDay
          };
        }
        
        if (!this.singleMode && this.dateRange.length === 2) {
          const endDate = this.dateRange[1];
          if (endDate) {
            const [endYear, endMonth, endDay] = endDate.split('-').map(Number);
            this.endDate = {
              year: endYear,
              month: endMonth,
              day: endDay
            };
          }
        }
      }
    },
    formatDisplayDate(dateStr) {
      if (!dateStr) return '';
      const [year, month, day] = dateStr.split('-');
      return `${year}/${month}/${day}`;
    },
    showPopup() {
      this.tempDateRange = [...this.dateRange]; // 备份当前选择的日期范围
      this.$refs.popup.open();
    },
    hidePopup() {
      // 取消选择，恢复原来的值
      this.dateRange = [...this.tempDateRange];
      this.initDateFromRange();
      this.$refs.popup.close();
    },
    confirmSelection() {
      this.emitValue();
      this.$refs.popup.close();
    },
    clearDate(e) {
      e && e.stopPropagation();
      this.startDate = null;
      this.endDate = null;
      this.dateRange = [];
      this.emitValue();
    },
    clearInPopup() {
      // 在弹窗内清空选择
      this.startDate = null;
      this.endDate = null;
    },
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentYear--;
        this.currentMonth = 12;
      } else {
        this.currentMonth--;
      }
    },
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear++;
        this.currentMonth = 1;
      } else {
        this.currentMonth++;
      }
    },
    selectDay(day) {
      if (this.singleMode) {
        // 单选模式：只设置起始日期
        this.startDate = {
          year: this.currentYear,
          month: this.currentMonth,
          day: day
        };
        this.endDate = null;
        this.emitValue();
        return;
      }
      
      // 范围选择模式
      if (!this.startDate || (this.startDate && this.endDate)) {
        // 如果没有起始日期，或者已经选择了起始和结束日期，则重新开始选择
        this.startDate = {
          year: this.currentYear,
          month: this.currentMonth,
          day: day
        };
        this.endDate = null;
      } else {
        // 如果已经有起始日期，则设置结束日期
        const selectedDate = new Date(this.currentYear, this.currentMonth - 1, day);
        const startDateTime = new Date(this.startDate.year, this.startDate.month - 1, this.startDate.day);
        
        if (selectedDate < startDateTime) {
          // 如果选择的日期小于起始日期，则交换
          this.endDate = { ...this.startDate };
          this.startDate = {
            year: this.currentYear,
            month: this.currentMonth,
            day: day
          };
        } else {
          this.endDate = {
            year: this.currentYear,
            month: this.currentMonth,
            day: day
          };
        }
      }
    },
    isStartDay(day) {
      if (!this.startDate || !day) return false;
      return this.startDate.year === this.currentYear && 
             this.startDate.month === this.currentMonth && 
             this.startDate.day === day;
    },
    isEndDay(day) {
      if (!this.endDate || !day) return false;
      return this.endDate.year === this.currentYear && 
             this.endDate.month === this.currentMonth && 
             this.endDate.day === day;
    },
    isInRange(day) {
      if (!this.startDate || !this.endDate || !day || this.singleMode) return false;
      
      const currentDate = new Date(this.currentYear, this.currentMonth - 1, day);
      const startDate = new Date(this.startDate.year, this.startDate.month - 1, this.startDate.day);
      const endDate = new Date(this.endDate.year, this.endDate.month - 1, this.endDate.day);
      
      return currentDate > startDate && currentDate < endDate;
    },
    isToday(day) {
      if (!day) return false;
      return this.today.year === this.currentYear && 
             this.today.month === this.currentMonth && 
             this.today.day === day;
    },
    isDayDisabled(day) {
      if (!day) return false;
      
      const currentDate = new Date(this.currentYear, this.currentMonth - 1, day);
      
      // 如果启用了禁用未来日期的功能，检查是否超过今天
      if (this.disableFutureDates) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (currentDate > today) return true;
      }
      
      if (this.minDate) {
        const [minYear, minMonth, minDay] = this.minDate.split('-').map(Number);
        const minDate = new Date(minYear, minMonth - 1, minDay || 1);
        if (currentDate < minDate) return true;
      }
      
      if (this.maxDate) {
        const [maxYear, maxMonth, maxDay] = this.maxDate.split('-').map(Number);
        const maxDate = new Date(maxYear, maxMonth - 1, maxDay || 31);
        if (currentDate > maxDate) return true;
      }
      
      return false;
    },
    emitValue() {
      let startValue = '';
      
      if (this.startDate) {
        startValue = `${this.startDate.year}-${String(this.startDate.month).padStart(2, '0')}-${String(this.startDate.day).padStart(2, '0')}`;
      }
      
      if (this.singleMode) {
        // 单选模式：只返回一个日期
        this.dateRange = [startValue];
        this.$emit('input', [startValue]);
        this.$emit('change', [startValue]);
      } else {
        // 范围选择模式：返回起始和结束日期
        let endValue = '';
        if (this.endDate) {
          endValue = `${this.endDate.year}-${String(this.endDate.month).padStart(2, '0')}-${String(this.endDate.day).padStart(2, '0')}`;
        }
        
        this.dateRange = [startValue, endValue];
        this.$emit('input', [startValue, endValue]);
        this.$emit('change', [startValue, endValue]);
      }
    }
  }
}
</script>

<style lang="scss">
.day-picker-wrapper {
  position: relative;
  width: 100%;
  
  .date-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70rpx;
    padding: 0 20rpx;
    border: 1px solid #ddd;
    border-radius: 8rpx;
    background-color: #fff;
    
    .date-display {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      
      .placeholder {
        color: #999;
      }
    }
    
    uni-icons {
      margin-left: 10rpx;
    }
  }
}

.popup-content {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25rpx 30rpx;
    border-bottom: 1px solid #eee;
    
    .left-btns {
      display: flex;
      gap: 20rpx;
    }
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .cancel-btn {
      font-size: 28rpx;
      color: #999;
    }
    
    .clear-btn {
      font-size: 28rpx;
      color: #999;
    }
    
    .confirm-btn {
      font-size: 28rpx;
      color: #07C160;
      font-weight: bold;
    }
  }
}

.day-picker-container {
  background: #fff;
  padding: 20rpx;
  width: 100%;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #eee;
    
    .month-title {
      font-size: 32rpx;
      font-weight: bold;
    }
    
    .arrow {
      font-size: 36rpx;
      padding: 0 20rpx;
      color: #666;
      cursor: pointer;
    }
  }
  
  .weekdays {
    display: flex;
    padding: 20rpx 0;
    border-bottom: 1px solid #eee;
    
    .weekday {
      flex: 1;
      text-align: center;
      font-size: 28rpx;
      color: #666;
    }
  }
  
  .days-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 0;
    
    .day-item {
      width: calc(100% / 7);
      height: 70rpx;
      text-align: center;
      line-height: 70rpx;
      font-size: 28rpx;
      border-radius: 50rpx;
      margin: 6rpx 0;
      position: relative;
      
      &.empty {
        background: transparent;
      }
      
      &.start-day, &.end-day {
        color: #fff;
        background-color: #07C160;
        font-weight: bold;
      }
      
      &.in-range {
        background-color: rgba(7, 193, 96, 0.1);
        color: #07C160;
      }
      
      &.disabled {
        color: #ccc;
        background-color: #f8f8f8;
        cursor: not-allowed;
      }
      
      &.today {
        border: 1px solid #07C160;
      }
    }
  }
}
</style>