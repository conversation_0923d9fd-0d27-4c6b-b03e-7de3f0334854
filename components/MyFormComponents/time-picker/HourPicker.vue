<template>
  <view class="hour-picker-wrapper">
    <!-- 显示选择的时间范围的输入框 -->
    <view class="time-input" @click="showPopup">
      <view class="time-display">
        <text v-if="!singleMode && hourRange.length === 2 && hourRange[0] && hourRange[1]">
          {{ formatDisplayHour(hourRange[0]) }} 至 {{ formatDisplayHour(hourRange[1]) }}
        </text>
        <text v-else-if="singleMode && hourRange[0]">
          {{ formatDisplayHour(hourRange[0]) }}
        </text>
        <text v-else class="placeholder">{{ singleMode ? '请选择时间' : '请选择时间范围' }}</text>
      </view>
      <uni-icons v-if="clearIcon && ((singleMode && hourRange[0]) || (!singleMode && hourRange.length === 2 && hourRange[0] && hourRange[1]))" 
                type="clear" 
                size="16" 
                color="#999" 
                @click.stop="clearTime">
      </uni-icons>
      <uni-icons type="clock" size="16" color="#666"></uni-icons>
    </view>
    
    <!-- 弹出层 -->
    <uni-popup ref="popup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <view class="left-btns">
            <text class="cancel-btn" @click="hidePopup">取消</text>
            <text class="clear-btn" @click="clearInPopup">清空</text>
          </view>
          <text class="title">{{ singleMode ? '选择时间' : '选择时间范围' }}</text>
          <text class="confirm-btn" @click="confirmSelection">确定</text>
        </view>
        
        <!-- 时间选择器内容 -->
        <view class="hour-picker-container">
          <view class="header">
            <text class="arrow" @click="prevDay">«</text>
            <text class="date-title">{{ formatCurrentDate() }}</text>
            <text class="arrow" @click="nextDay">»</text>
          </view>
          <view class="hours-grid">
            <view 
              v-for="hour in hours" 
              :key="hour"
              :class="['hour-item', { 
                'start-hour': isStartHour(hour),
                'end-hour': isEndHour(hour),
                'in-range': isInRange(hour),
                'disabled': isHourDisabled(hour),
                'current-hour': isCurrentHour(hour)
              }]"
              @click="!isHourDisabled(hour) && selectHour(hour)"
            >
              {{ formatHour(hour) }}
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'HourPicker',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    currentDate: {
      type: String,
      default: () => {
        const today = new Date();
        return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      }
    },
    minHour: {
      type: String,
      default: ''
    },
    maxHour: {
      type: String,
      default: ''
    },
    clearIcon: {
      type: Boolean,
      default: true
    },
    disableFutureHours: {
      type: Boolean,
      default: false
    },
    singleMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedDate: this.currentDate,
      startHour: null,
      endHour: null,
      currentHour: new Date().getHours(),
      hourRange: this.value || [],
      tempHourRange: [] // 用于存储临时选择的时间范围
    }
  },
  computed: {
    hours() {
      // 生成0-23的小时数组
      const hours = [];
      for (let i = 0; i < 24; i++) {
        hours.push(i);
      }
      return hours;
    }
  },
  watch: {
    value(newVal) {
      this.hourRange = newVal || [];
      this.initHourFromRange();
    },
    currentDate(newVal) {
      this.selectedDate = newVal;
    }
  },
  created() {
    this.initHourFromRange();
  },
  methods: {
    initHourFromRange() {
      // 初始化startHour和endHour
      if (this.hourRange && this.hourRange.length >= 1) {
        const startTime = this.hourRange[0];
        if (startTime) {
          const hour = parseInt(startTime.split(':')[0]);
          this.startHour = hour;
        }
        
        if (!this.singleMode && this.hourRange.length === 2) {
          const endTime = this.hourRange[1];
          if (endTime) {
            const hour = parseInt(endTime.split(':')[0]);
            this.endHour = hour;
          }
        }
      }
    },
    formatDisplayHour(timeStr) {
      if (!timeStr) return '';
      return timeStr;
    },
    formatHour(hour) {
      return `${String(hour).padStart(2, '0')}:00`;
    },
    formatCurrentDate() {
      const [year, month, day] = this.selectedDate.split('-');
      return `${year}年${month}月${day}日`;
    },
    showPopup() {
      this.tempHourRange = [...this.hourRange]; // 备份当前选择的时间范围
      this.$refs.popup.open();
    },
    hidePopup() {
      // 取消选择，恢复原来的值
      this.hourRange = [...this.tempHourRange];
      this.initHourFromRange();
      this.$refs.popup.close();
    },
    confirmSelection() {
      this.emitValue();
      this.$refs.popup.close();
    },
    clearTime(e) {
      e && e.stopPropagation();
      this.startHour = null;
      this.endHour = null;
      this.hourRange = [];
      this.emitValue();
    },
    clearInPopup() {
      // 在弹窗内清空选择
      this.startHour = null;
      this.endHour = null;
    },
    prevDay() {
      const currentDate = new Date(this.selectedDate);
      currentDate.setDate(currentDate.getDate() - 1);
      this.selectedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;
    },
    nextDay() {
      const currentDate = new Date(this.selectedDate);
      currentDate.setDate(currentDate.getDate() + 1);
      this.selectedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;
    },
    selectHour(hour) {
      if (this.singleMode) {
        // 单选模式：只设置起始时间
        this.startHour = hour;
        this.endHour = null;
        this.emitValue();
        return;
      }
      
      // 范围选择模式
      if (this.startHour === null || (this.startHour !== null && this.endHour !== null)) {
        // 如果没有起始时间，或者已经选择了起始和结束时间，则重新开始选择
        this.startHour = hour;
        this.endHour = null;
      } else {
        // 如果已经有起始时间，则设置结束时间
        if (hour < this.startHour) {
          // 如果选择的时间小于起始时间，则交换
          this.endHour = this.startHour;
          this.startHour = hour;
        } else {
          this.endHour = hour;
        }
      }
    },
    isStartHour(hour) {
      return this.startHour === hour;
    },
    isEndHour(hour) {
      return this.endHour === hour;
    },
    isInRange(hour) {
      if (this.startHour === null || this.endHour === null || this.singleMode) return false;
      return hour > this.startHour && hour < this.endHour;
    },
    isCurrentHour(hour) {
      // 检查是否为当前小时（仅当选择的日期是今天时）
      const today = new Date();
      const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      return this.selectedDate === todayStr && hour === this.currentHour;
    },
    isHourDisabled(hour) {
      // 如果启用了禁用未来时间的功能
      if (this.disableFutureHours) {
        const now = new Date();
        const todayStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
        
        if (this.selectedDate === todayStr && hour > now.getHours()) {
          return true;
        }
        
        if (this.selectedDate > todayStr) {
          return true;
        }
      }
      
      if (this.minHour) {
        const minHourNum = parseInt(this.minHour.split(':')[0]);
        if (hour < minHourNum) return true;
      }
      
      if (this.maxHour) {
        const maxHourNum = parseInt(this.maxHour.split(':')[0]);
        if (hour > maxHourNum) return true;
      }
      
      return false;
    },
    emitValue() {
      if (this.singleMode) {
        // 单选模式：只返回一个时间
        const timeValue = this.startHour !== null ? this.formatHour(this.startHour) : '';
        this.hourRange = [timeValue];
        this.$emit('input', [timeValue]);
        this.$emit('change', [timeValue]);
      } else {
        // 范围选择模式：返回起始和结束时间
        const startValue = this.startHour !== null ? this.formatHour(this.startHour) : '';
        const endValue = this.endHour !== null ? this.formatHour(this.endHour) : '';
        
        this.hourRange = [startValue, endValue];
        this.$emit('input', [startValue, endValue]);
        this.$emit('change', [startValue, endValue]);
      }
    }
  }
}
</script>

<style lang="scss">
.hour-picker-wrapper {
  position: relative;
  width: 100%;

  .time-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70rpx;
    padding: 0 20rpx;
    border: 1px solid #ddd;
    border-radius: 8rpx;
    background-color: #fff;

    .time-display {
      flex: 1;
      font-size: 28rpx;
      color: #333;

      .placeholder {
        color: #999;
      }
    }

    uni-icons {
      margin-left: 10rpx;
    }
  }
}

.popup-content {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  max-height: 80vh;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 35rpx 40rpx;
    border-bottom: 1px solid #eee;
    background-color: #fafafa;

    .left-btns {
      display: flex;
      gap: 30rpx;
    }

    .title {
      font-size: 34rpx;
      font-weight: bold;
      color: #333;
    }

    .cancel-btn {
      font-size: 30rpx;
      color: #999;
      padding: 10rpx 15rpx;
      border-radius: 6rpx;

      &:hover {
        background-color: #f0f0f0;
      }
    }

    .clear-btn {
      font-size: 30rpx;
      color: #999;
      padding: 10rpx 15rpx;
      border-radius: 6rpx;

      &:hover {
        background-color: #f0f0f0;
      }
    }

    .confirm-btn {
      font-size: 30rpx;
      color: #07C160;
      font-weight: bold;
      padding: 10rpx 20rpx;
      border-radius: 6rpx;

      &:hover {
        background-color: rgba(7, 193, 96, 0.1);
      }
    }
  }
}

.hour-picker-container {
  background: #fff;
  padding: 30rpx;
  width: 100%;
  min-height: 600rpx;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 30rpx;

    .date-title {
      font-size: 34rpx;
      font-weight: bold;
    }

    .arrow {
      font-size: 40rpx;
      padding: 15rpx 25rpx;
      color: #666;
      cursor: pointer;
      border-radius: 8rpx;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }

  .hours-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(6, 1fr);
    gap: 20rpx;
    padding: 30rpx 0;

    .hour-item {
      height: 90rpx;
      text-align: center;
      line-height: 90rpx;
      font-size: 30rpx;
      border-radius: 16rpx;
      border: 1px solid #eee;
      background-color: #fff;
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #07C160;
        transform: translateY(-2rpx);
        box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
      }

      &.start-hour, &.end-hour {
        color: #fff;
        background-color: #07C160;
        border-color: #07C160;
        font-weight: bold;
        transform: translateY(-2rpx);
        box-shadow: 0 6rpx 16rpx rgba(7, 193, 96, 0.3);
      }

      &.in-range {
        background-color: rgba(7, 193, 96, 0.15);
        color: #07C160;
        border-color: #07C160;
        font-weight: 500;
      }

      &.disabled {
        color: #ccc;
        background-color: #f8f8f8;
        border-color: #f0f0f0;
        cursor: not-allowed;
        box-shadow: none;

        &:hover {
          transform: none;
          border-color: #f0f0f0;
          box-shadow: none;
        }
      }

      &.current-hour {
        border: 2px solid #07C160;
        font-weight: bold;
        box-shadow: 0 0 0 4rpx rgba(7, 193, 96, 0.2);

        &:not(.disabled):not(.start-hour):not(.end-hour) {
          background-color: rgba(7, 193, 96, 0.05);
        }
      }
    }
  }
}
</style>
