<template>
  <view class="year-picker">
    <view class="year-header">
      <text @click="prevDecade" class="arrow">&lt;&lt;</text>
      <text class="decade-title">{{ decadeRange }}</text>
      <text @click="nextDecade" class="arrow">&gt;&gt;</text>
    </view>
    <view class="years-grid">
      <view
        v-for="year in years"
        :key="year"
        :class="{ 'current-year': year === currentYear }"
        @click="selectYear(year)"
        class="year-cell"
      >
        {{ year }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CurrentYearPicker',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择年份'
    }
  },
  data() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const startYear = Math.floor(currentYear / 10) * 10;
    
    return {
      startYear: startYear,
      currentYear: currentYear
    };
  },
  computed: {
    decadeRange() {
      return `${this.startYear}年 - ${this.startYear + 9}年`;
    },
    years() {
      const years = [];
      for (let i = 0; i < 10; i++) {
        years.push(this.startYear + i);
      }
      return years;
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          const year = parseInt(newVal);
          if (!isNaN(year)) {
            this.currentYear = year;
            this.startYear = Math.floor(year / 10) * 10;
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    prevDecade() {
      this.startYear -= 10;
    },
    nextDecade() {
      this.startYear += 10;
    },
    selectYear(year) {
      this.currentYear = year;
      const selectedYear = String(year);
      this.$emit('input', selectedYear);
      this.$emit('change', selectedYear);
    }
  }
};
</script>

<style scoped lang="scss">
.year-picker {
  background-color: #fff;
  border-radius: 8px;
  padding: 20rpx;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 700rpx;
  text-align: center;
  color: #333;

  .year-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;

    .decade-title {
      font-size: 36rpx;
      font-weight: bold;
      flex: 1;
    }

    .arrow {
      font-size: 40rpx;
      padding: 0 20rpx;
      color: #666;
      cursor: pointer;
      
      &:hover {
        color: #2A5EFF;
      }
    }
  }

  .current-year {
    background-color: #2A5EFF;
    color: #fff;
    border-radius: 8rpx;
  }

  .years-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    
    .year-cell {
      width: 150rpx;
      height: 70rpx;
      line-height: 70rpx;
      font-size: 32rpx;
      margin: 8rpx;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 8rpx;
      
      &:hover {
        background-color: #f0f0f0;
      }
    }
  }
}
</style>