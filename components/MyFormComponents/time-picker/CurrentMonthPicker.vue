<template>
  <view class="calendar">
    <!-- 头部：显示年 -->
    <view class="calendar-header">
      <text class="arrow" @click="prevYear">&lt;&lt;</text>
      <text class="month-title">{{ currentYear }}年</text>
      <text class="arrow" @click="nextYear">&gt;&gt;</text>
    </view>
    <view class="calendar-grid">
      <view v-for="(row, rowIndex) in monthGrid" :key="rowIndex" class="month-row">
        <view
            v-for="(cell, cellIndex) in row"
            :key="cellIndex"
            :class="{ 'is-current': cell.isCurrent }"
            @click="selectMonth(cell)"
            class="month-cell"
        >
          {{ cell.name }}
        </view>
      </view>
    </view>
  </view>
</template>


<script>
export default {
  name: 'CurrentMonthPicker',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择月份'
    }
  },
  data() {
    const now = new Date();
    return {
      currentYear: now.getFullYear(),
      currentMonth: now.getMonth() + 1,
      monthGrid: []
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          const parts = newVal.split('-');
          if (parts.length >= 2) {
            this.currentYear = parseInt(parts[0]);
            this.currentMonth = parseInt(parts[1]);
            this.initMonthGrid();
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.initMonthGrid();
  },
  methods: {
    initMonthGrid() {
      this.monthGrid = [];
      const months = [
        '一月', '二月', '三月',
        '四月', '五月', '六月',
        '七月', '八月', '九月',
        '十月', '十一月', '十二月'
      ];

      for (let i = 0; i < 12; i += 4) {
        const row = [];
        for (let j = 0; j < 4; j++) {
          const monthNum = i + j + 1;
          row.push({
            name: months[i + j],
            value: monthNum,
            isCurrent: monthNum === this.currentMonth
          });
        }
        this.monthGrid.push(row);
      }
    },
    prevYear() {
      this.currentYear--;
      this.initMonthGrid();
    },
    nextYear() {
      this.currentYear++;
      this.initMonthGrid();
    },
    selectMonth(cell) {
      this.currentMonth = cell.value;
      this.initMonthGrid();
      
      // 格式化为 yyyy-MM
      const month = cell.value.toString().padStart(2, '0');
      const selectedDate = `${this.currentYear}-${month}`;
      
      this.$emit('input', selectedDate);
      this.$emit('change', selectedDate);
    }
  }
}
</script>

<style scoped lang="scss">
.calendar {
  background-color: #fff;
  border-radius: 8px;
  padding: 20rpx;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 700rpx;
  text-align: center;
  color: #333;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;

    .month-title {
      font-size: 36rpx;
      font-weight: bold;
      flex: 1;
    }

    .arrow {
      font-size: 40rpx;
      padding: 0 20rpx;
      color: #666;
      cursor: pointer;
      
      &:hover {
        color: #2A5EFF;
      }
    }
  }

  &-grid {
    display: flex;
    flex-direction: column;
    align-items: center;

    .month-row {
      display: flex;
      justify-content: space-around;
      width: 100%;
      margin-bottom: 20rpx;
    }

    .month-cell {
      width: 160rpx;
      height: 90rpx;
      line-height: 70rpx;
      font-size: 32rpx;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 8rpx;

      &:hover {
        background-color: #f0f0f0;
      }

      &.is-current {
        background-color: #2A5EFF;
        color: white;
      }
    }
  }
}
</style>
