<template>
  <view class="year-picker-wrapper">
    <!-- 显示选择的年份的输入框 -->
    <view class="date-input" @click="showPopup">
      <view class="date-display">
        <text v-if="!singleMode && yearRange.length === 2 && yearRange[0] && yearRange[1]">
          {{ yearRange[0] }}年 至 {{ yearRange[1] }}年
        </text>
        <text v-else-if="singleMode && yearRange[0]">
          {{ yearRange[0] }}年
        </text>
        <text v-else class="placeholder">{{ singleMode ? '请选择年份' : '请选择年份范围' }}</text>
      </view>
      <uni-icons v-if="clearIcon && ((singleMode && yearRange[0]) || (!singleMode && yearRange.length === 2 && yearRange[0] && yearRange[1]))" 
                type="clear" 
                size="16" 
                color="#999" 
                @click.stop="clearYear">
      </uni-icons>
      <uni-icons type="calendar" size="16" color="#666"></uni-icons>
    </view>
    
    <!-- 弹出层 -->
    <uni-popup ref="popup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <view class="left-btns">
            <text class="cancel-btn" @click="hidePopup">取消</text>
            <text class="clear-btn" @click="clearInPopup">清空</text>
          </view>
          <text class="title">{{ singleMode ? '选择年份' : '选择年份范围' }}</text>
          <text class="confirm-btn" @click="confirmSelection">确定</text>
        </view>
        
        <!-- 年份选择器内容 -->
        <view class="year-picker-container">
          <view class="header">
            <text class="arrow" @click="prevDecade">«</text>
            <text class="decade-title">{{ startYear }}年 - {{ endYear }}年</text>
            <text class="arrow" @click="nextDecade">»</text>
          </view>
          <view class="years-grid">
            <view 
              v-for="year in years" 
              :key="year"
              :class="['year-item', { 
                'start-year': isStartYear(year),
                'end-year': isEndYear(year),
                'in-range': isInRange(year),
                disabled: isYearDisabled(year) 
              }]"
              @click="selectYear(year)"
            >
              {{ year }}
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'YearPicker',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    minYear: {
      type: Number,
      default: 2000
    },
    maxYear: {
      type: Number,
      default: 2050
    },
    clearIcon: {
      type: Boolean,
      default: true
    },
    disableFutureDates: {
      type: Boolean,
      default: false
    },
    singleMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const currentYear = new Date().getFullYear();
    const startYear = Math.floor(currentYear / 10) * 10;
    return {
      startYear: startYear,
      endYear: startYear + 9,
      selectedStartYear: null,
      selectedEndYear: null,
      years: [],
      yearRange: this.value || [],
      tempYearRange: [] // 用于存储临时选择的年份范围
    }
  },
  watch: {
    value(newVal) {
      this.yearRange = newVal || [];
      this.initYearFromRange();
    }
  },
  created() {
    this.generateYears();
    this.initYearFromRange();
  },
  methods: {
    initYearFromRange() {
      // 初始化selectedStartYear和selectedEndYear
      if (this.yearRange && this.yearRange.length >= 1) {
        const startYear = this.yearRange[0];
        if (startYear) {
          this.selectedStartYear = parseInt(startYear);
          this.updateDecade(this.selectedStartYear);
        }
        
        if (!this.singleMode && this.yearRange.length === 2) {
          const endYear = this.yearRange[1];
          if (endYear) {
            this.selectedEndYear = parseInt(endYear);
          }
        }
      }
    },
    showPopup() {
      this.tempYearRange = [...this.yearRange]; // 备份当前选择的年份范围
      this.$refs.popup.open();
    },
    hidePopup() {
      // 取消选择，恢复原来的值
      this.yearRange = [...this.tempYearRange];
      this.initYearFromRange();
      this.$refs.popup.close();
    },
    confirmSelection() {
      this.emitValue();
      this.$refs.popup.close();
    },
    clearYear(e) {
      e && e.stopPropagation();
      this.selectedStartYear = null;
      this.selectedEndYear = null;
      this.yearRange = [];
      this.emitValue();
    },
    clearInPopup() {
      // 在弹窗内清空选择
      this.selectedStartYear = null;
      this.selectedEndYear = null;
    },
    generateYears() {
      this.years = [];
      for (let i = this.startYear; i <= this.endYear; i++) {
        this.years.push(i);
      }
    },
    updateDecade(year) {
      this.startYear = Math.floor(year / 10) * 10;
      this.endYear = this.startYear + 9;
      this.generateYears();
    },
    prevDecade() {
      this.startYear -= 10;
      this.endYear -= 10;
      this.generateYears();
    },
    nextDecade() {
      this.startYear += 10;
      this.endYear += 10;
      this.generateYears();
    },
    selectYear(year) {
      if (this.isYearDisabled(year)) return;
      
      if (this.singleMode) {
        // 单选模式：只设置起始年份
        this.selectedStartYear = year;
        this.selectedEndYear = null;
        this.emitValue();
        return;
      }
      
      // 范围选择模式
      if (!this.selectedStartYear || (this.selectedStartYear && this.selectedEndYear)) {
        // 如果没有起始年份，或者已经选择了起始和结束年份，则重新开始选择
        this.selectedStartYear = year;
        this.selectedEndYear = null;
      } else {
        // 如果已经有起始年份，则设置结束年份
        if (year < this.selectedStartYear) {
          // 如果选择的年份小于起始年份，则交换
          this.selectedEndYear = this.selectedStartYear;
          this.selectedStartYear = year;
        } else {
          this.selectedEndYear = year;
        }
      }
      
      this.emitValue();
    },
    isStartYear(year) {
      return year === this.selectedStartYear;
    },
    isEndYear(year) {
      return year === this.selectedEndYear;
    },
    isInRange(year) {
      if (!this.selectedStartYear || !this.selectedEndYear || this.singleMode) return false;
      return year > this.selectedStartYear && year < this.selectedEndYear;
    },
    emitValue() {
      const startValue = this.selectedStartYear ? String(this.selectedStartYear) : '';
      
      if (this.singleMode) {
        // 单选模式：只返回一个年份
        this.yearRange = [startValue];
        this.$emit('input', [startValue]);
        this.$emit('change', [startValue]);
      } else {
        // 范围选择模式：返回起始和结束年份
        const endValue = this.selectedEndYear ? String(this.selectedEndYear) : '';
        this.yearRange = [startValue, endValue];
        this.$emit('input', [startValue, endValue]);
        this.$emit('change', [startValue, endValue]);
      }
    },
    isYearDisabled(year) {
      // 如果启用了禁用未来年份的功能，检查是否超过当前年份
      if (this.disableFutureDates) {
        const currentYear = new Date().getFullYear();
        if (year > currentYear) return true;
      }

      return year < this.minYear || year > this.maxYear;
    }
  }
}
</script>

<style lang="scss">
.year-picker-wrapper {
  position: relative;
  width: 100%;
  
  .date-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70rpx;
    padding: 0 20rpx;
    border: 1px solid #ddd;
    border-radius: 8rpx;
    background-color: #fff;
    
    .date-display {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      
      .placeholder {
        color: #999;
      }
    }
    
    uni-icons {
      margin-left: 10rpx;
    }
  }
}

.popup-content {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25rpx 30rpx;
    border-bottom: 1px solid #eee;
    
    .left-btns {
      display: flex;
      gap: 20rpx;
    }
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .cancel-btn {
      font-size: 28rpx;
      color: #999;
    }
    
    .clear-btn {
      font-size: 28rpx;
      color: #999;
    }
    
    .confirm-btn {
      font-size: 28rpx;
      color: #07C160;
      font-weight: bold;
    }
  }
}

.year-picker-container {
  background: #fff;
  padding: 20rpx;
  width: 100%;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #eee;
    
    .decade-title {
      font-size: 32rpx;
      font-weight: bold;
    }
    
    .arrow {
      font-size: 36rpx;
      padding: 0 20rpx;
      color: #666;
      cursor: pointer;
    }
  }
  
  .years-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 0;
    
    .year-item {
      width: 25%;
      text-align: center;
      padding: 30rpx 0;
      font-size: 28rpx;
      border-radius: 10rpx;
      margin: 10rpx 0;
      position: relative;
      
      &.start-year {
        color: #fff;
        background-color: #07C160;
        font-weight: bold;
      }
      
      &.end-year {
        color: #fff;
        background-color: #07C160;
        font-weight: bold;
      }
      
      &.in-range {
        background-color: rgba(7, 193, 96, 0.1);
        color: #07C160;
      }
      
      &.disabled {
        color: #ccc;
        background-color: #f8f8f8;
      }
    }
  }
}
</style> 