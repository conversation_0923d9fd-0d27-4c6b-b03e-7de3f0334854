<template>
  <view class="calendar">
    <!-- 头部：显示年月 -->
    <view class="calendar-header">
      <text class="arrow" @click="prevYear">&lt;&lt;</text>
      <text class="arrow" @click="prevMonth">&lt;</text>
      <text class="day-title">{{ displayYear }} 年 {{ displayMonth }} 月</text>
      <text class="arrow" @click="nextMonth">&gt;</text>
      <text class="arrow" @click="nextYear">&gt;&gt;</text>
    </view>
    <!-- 日期网格 -->
    <view class="calendar-grid">
      <view class="week-row">
        <view v-for="day in weekDays" :key="day" class="week-day">{{ day }}</view>
      </view>
      <view class="dates-container">
        <view v-for="(row, rowIndex) in monthGrid" :key="rowIndex" class="date-row">
          <view
              v-for="(cell, cellIndex) in row"
              :key="cellIndex"
              :class="{ 'is-current': cell.isCurrent, 'empty-cell': !cell.day }"
              @click="selectDate(cell)"
              class="date-cell"
          >
            {{ cell.day || '' }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CurrentDayPicker',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择日期'
    }
  },
  data() {
    const now = new Date();
    return {
      displayYear: now.getFullYear(),
      displayMonth: now.getMonth() + 1,
      currentDay: null,
      weekDays: ['日', '一', '二', '三', '四', '五', '六'],
      monthGrid: []
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          const date = new Date(newVal);
          this.displayYear = date.getFullYear();
          this.displayMonth = date.getMonth() + 1;
          this.currentDay = date.getDate();
        }
      },
      immediate: true
    }
  },
  created() {
    this.initMonthGrid();
  },
  methods: {
    initMonthGrid() {
      const firstDay = new Date(this.displayYear, this.displayMonth - 1, 1);
      const startDayOfWeek = firstDay.getDay(); // 0 是周日，1 是周一
      const daysInMonth = new Date(this.displayYear, this.displayMonth, 0).getDate();
      
      this.monthGrid = [];
      let row = [];
      
      // 添加上月剩余天数的空单元格
      for (let i = 0; i < startDayOfWeek; i++) {
        row.push({});
      }
      
      // 添加当月的天数
      for (let i = 1; i <= daysInMonth; i++) {
        row.push({
          day: i,
          isCurrent: i === this.currentDay
        });
        
        if (row.length === 7) {
          this.monthGrid.push(row);
          row = [];
        }
      }
      
      // 如果最后一行不满7天，添加下月天数的空单元格
      if (row.length > 0) {
        while (row.length < 7) {
          row.push({});
        }
        this.monthGrid.push(row);
      }
    },
    prevYear() {
      this.displayYear--;
      this.initMonthGrid();
    },
    nextYear() {
      this.displayYear++;
      this.initMonthGrid();
    },
    prevMonth() {
      if (this.displayMonth === 1) {
        this.displayMonth = 12;
        this.displayYear--;
      } else {
        this.displayMonth--;
      }
      this.initMonthGrid();
    },
    nextMonth() {
      if (this.displayMonth === 12) {
        this.displayMonth = 1;
        this.displayYear++;
      } else {
        this.displayMonth++;
      }
      this.initMonthGrid();
    },
    selectDate(cell) {
      if (!cell.day) return;
      
      this.currentDay = cell.day;
      this.initMonthGrid();
      
      // 格式化为 yyyy-MM-dd
      const month = this.displayMonth.toString().padStart(2, '0');
      const day = cell.day.toString().padStart(2, '0');
      const selectedDate = `${this.displayYear}-${month}-${day}`;
      
      this.$emit('input', selectedDate);
      this.$emit('change', selectedDate);
    },
  }
};
</script>

<style scoped lang="scss">
.calendar {
  background-color: #fff;
  border-radius: 8px;
  padding: 20rpx;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 700rpx;
  text-align: center;
  color: #333;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;

    .day-title {
      font-size: 36rpx;
      font-weight: bold;
      flex: 1;
    }

    .arrow {
      font-size: 40rpx;
      padding: 0 20rpx;
      color: #666;
      cursor: pointer;
      
      &:hover {
        color: #2A5EFF;
      }
    }
  }

  .week-row {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10rpx;
    border-bottom: 1px solid #eee;
    padding-bottom: 10rpx;
    
    .week-day {
      width: 90rpx;
      height: 60rpx;
      line-height: 60rpx;
      font-size: 32rpx;
      color: #999;
    }
  }
  
  .dates-container {
    .date-row {
      display: flex;
      justify-content: space-around;
      margin-bottom: 5rpx;
    }
    
    .date-cell {
      width: 90rpx;
      height: 60rpx;
      line-height: 60rpx;
      font-size: 32rpx;
      margin: 3rpx 0;
      border-radius: 8rpx;
      cursor: pointer;
      
      &:hover {
        background-color: #f0f0f0;
      }
      
      &.is-current {
        background-color: #2A5EFF;
        color: white;
      }
      
      &.empty-cell {
        visibility: hidden;
      }
    }
  }
}
</style>