<template>
  <view class="month-picker-wrapper">
    <!-- 显示选择的月份的输入框 -->
    <view class="date-input" @click="showPopup">
      <view class="date-display">
        <text v-if="!singleMode && monthRange.length === 2 && monthRange[0] && monthRange[1]">
          {{ formatDisplayMonth(monthRange[0]) }} 至 {{ formatDisplayMonth(monthRange[1]) }}
        </text>
        <text v-else-if="singleMode && monthRange[0]">
          {{ formatDisplayMonth(monthRange[0]) }}
        </text>
        <text v-else class="placeholder">{{ singleMode ? '请选择月份' : '请选择月份范围' }}</text>
      </view>
      <uni-icons v-if="clearIcon && ((singleMode && monthRange[0]) || (!singleMode && monthRange.length === 2 && monthRange[0] && monthRange[1]))" 
                type="clear" 
                size="16" 
                color="#999" 
                @click.stop="clearMonth">
      </uni-icons>
      <uni-icons type="calendar" size="16" color="#666"></uni-icons>
    </view>
    
    <!-- 弹出层 -->
    <uni-popup ref="popup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <view class="left-btns">
            <text class="cancel-btn" @click="hidePopup">取消</text>
            <text class="clear-btn" @click="clearInPopup">清空</text>
          </view>
          <text class="title">{{ singleMode ? '选择月份' : '选择月份范围' }}</text>
          <text class="confirm-btn" @click="confirmSelection">确定</text>
        </view>
        
        <!-- 月份选择器内容 -->
        <view class="month-picker-container">
          <view class="header">
            <text class="arrow" @click="prevYear">«</text>
            <text class="year-title">{{ currentYear }}年</text>
            <text class="arrow" @click="nextYear">»</text>
          </view>
          <view class="months-grid">
            <view 
              v-for="(month, index) in monthNames" 
              :key="index"
              :class="['month-item', { 
                'start-month': isStartMonth(index + 1),
                'end-month': isEndMonth(index + 1),
                'in-range': isInRange(index + 1),
                disabled: isMonthDisabled(index + 1)
              }]"
              @click="selectMonth(index + 1)"
            >
              {{ month }}
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'MonthPicker',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    minDate: {
      type: String,
      default: ''
    },
    maxDate: {
      type: String,
      default: ''
    },
    clearIcon: {
      type: Boolean,
      default: true
    },
    disableFutureDates: {
      type: Boolean,
      default: false
    },
    singleMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentYear: new Date().getFullYear(),
      startMonth: null,
      endMonth: null,
      monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
      monthRange: this.value || [],
      tempMonthRange: [] // 用于存储临时选择的月份范围
    }
  },
  watch: {
    value(newVal) {
      this.monthRange = newVal || [];
      this.initMonthFromRange();
    }
  },
  created() {
    this.initMonthFromRange();
  },
  methods: {
    initMonthFromRange() {
      // 初始化startMonth和endMonth
      if (this.monthRange && this.monthRange.length >= 1) {
        const startDate = this.monthRange[0];
        if (startDate) {
          const [startYear, startMonth] = startDate.split('-');
          this.currentYear = parseInt(startYear);
          this.startMonth = parseInt(startMonth);
        }
        
        if (!this.singleMode && this.monthRange.length === 2) {
          const endDate = this.monthRange[1];
          if (endDate) {
            const [endYear, endMonth] = endDate.split('-');
            this.endMonth = parseInt(endMonth);
          }
        }
      }
    },
    formatDisplayMonth(monthStr) {
      if (!monthStr) return '';
      const [year, month] = monthStr.split('-');
      return `${year}年${month}月`;
    },
    showPopup() {
      this.tempMonthRange = [...this.monthRange]; // 备份当前选择的月份范围
      this.$refs.popup.open();
    },
    hidePopup() {
      // 取消选择，恢复原来的值
      this.monthRange = [...this.tempMonthRange];
      this.initMonthFromRange();
      this.$refs.popup.close();
    },
    confirmSelection() {
      this.emitValue();
      this.$refs.popup.close();
    },
    clearMonth(e) {
      e && e.stopPropagation();
      this.startMonth = null;
      this.endMonth = null;
      this.monthRange = [];
      this.emitValue();
    },
    clearInPopup() {
      // 在弹窗内清空选择
      this.startMonth = null;
      this.endMonth = null;
    },
    prevYear() {
      this.currentYear--;
    },
    nextYear() {
      this.currentYear++;
    },
    selectMonth(month) {
      if (this.isMonthDisabled(month)) return;
      
      if (this.singleMode) {
        // 单选模式：只设置起始月份
        this.startMonth = month;
        this.endMonth = null;
        this.emitValue();
        return;
      }
      
      // 范围选择模式
      if (!this.startMonth || (this.startMonth && this.endMonth)) {
        // 如果没有起始月份，或者已经选择了起始和结束月份，则重新开始选择
        this.startMonth = month;
        this.endMonth = null;
      } else {
        // 如果已经有起始月份，则设置结束月份
        if (month < this.startMonth) {
          // 如果选择的月份小于起始月份，则交换
          this.endMonth = this.startMonth;
          this.startMonth = month;
        } else {
          this.endMonth = month;
        }
      }
      
      this.emitValue();
    },
    isStartMonth(month) {
      return month === this.startMonth;
    },
    isEndMonth(month) {
      return month === this.endMonth;
    },
    isInRange(month) {
      if (!this.startMonth || !this.endMonth || this.singleMode) return false;
      return month > this.startMonth && month < this.endMonth;
    },
    emitValue() {
      const startValue = this.startMonth ? `${this.currentYear}-${String(this.startMonth).padStart(2, '0')}` : '';
      
      if (this.singleMode) {
        // 单选模式：只返回一个月份
        this.monthRange = [startValue];
        this.$emit('input', [startValue]);
        this.$emit('change', [startValue]);
      } else {
        // 范围选择模式：返回起始和结束月份
        const endValue = this.endMonth ? `${this.currentYear}-${String(this.endMonth).padStart(2, '0')}` : '';
        this.monthRange = [startValue, endValue];
        this.$emit('input', [startValue, endValue]);
        this.$emit('change', [startValue, endValue]);
      }
    },
    isMonthDisabled(month) {
      const currentDate = new Date(this.currentYear, month - 1);

      // 如果启用了禁用未来月份的功能，检查是否超过当前月份
      if (this.disableFutureDates) {
        const today = new Date();
        const currentMonth = new Date(today.getFullYear(), today.getMonth());
        if (currentDate > currentMonth) return true;
      }

      if (this.minDate) {
        const [minYear, minMonth] = this.minDate.split('-').map(Number);
        const minDate = new Date(minYear, minMonth - 1);
        if (currentDate < minDate) return true;
      }

      if (this.maxDate) {
        const [maxYear, maxMonth] = this.maxDate.split('-').map(Number);
        const maxDate = new Date(maxYear, maxMonth - 1);
        if (currentDate > maxDate) return true;
      }

      return false;
    }
  }
}
</script>

<style lang="scss">
.month-picker-wrapper {
  position: relative;
  width: 100%;
  
  .date-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70rpx;
    padding: 0 20rpx;
    border: 1px solid #ddd;
    border-radius: 8rpx;
    background-color: #fff;
    
    .date-display {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      
      .placeholder {
        color: #999;
      }
    }
    
    uni-icons {
      margin-left: 10rpx;
    }
  }
}

.popup-content {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25rpx 30rpx;
    border-bottom: 1px solid #eee;
    
    .left-btns {
      display: flex;
      gap: 20rpx;
    }
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .cancel-btn {
      font-size: 28rpx;
      color: #999;
    }
    
    .clear-btn {
      font-size: 28rpx;
      color: #999;
    }
    
    .confirm-btn {
      font-size: 28rpx;
      color: #07C160;
      font-weight: bold;
    }
  }
}

.month-picker-container {
  background: #fff;
  padding: 20rpx;
  width: 100%;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #eee;
    
    .year-title {
      font-size: 32rpx;
      font-weight: bold;
    }
    
    .arrow {
      font-size: 36rpx;
      padding: 0 20rpx;
      color: #666;
      cursor: pointer;
    }
  }
  
  .months-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 0;
    
    .month-item {
      width: 25%;
      text-align: center;
      padding: 30rpx 0;
      font-size: 28rpx;
      border-radius: 50rpx;
      margin: 10rpx 0;
      position: relative;
      
      &.start-month {
        color: #fff;
        background-color: #07C160;
        font-weight: bold;
      }
      
      &.end-month {
        color: #fff;
        background-color: #07C160;
        font-weight: bold;
      }
      
      &.in-range {
        background-color: rgba(7, 193, 96, 0.1);
        color: #07C160;
      }
      
      &.disabled {
        color: #ccc;
        background-color: #f8f8f8;
      }
    }
  }
}
</style> 