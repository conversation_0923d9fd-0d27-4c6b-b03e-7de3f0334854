<template>
  <view class="monitoringContainer">
    <view class="content-wrapper">
      <!-- 部门选择区域 -->
      <view class="dept-section">
        <view class="tree-select" @tap="toggleDeptSelect">
          <text class="selected-text">{{ selectedDeptName || '请选择部门' }}</text>
          <uni-icons type="bottom" size="16" :color="showDeptSelect ? '#2A5EFF' : '#666'"></uni-icons>
        </view>

        <!-- 下拉树状选择框 -->
        <view class="tree-dropdown" v-if="showDeptSelect">
          <scroll-view
              scroll-y
              class="tree-scroll"
          >
            <tree-item
                v-for="dept in deptTreeList"
                :key="dept.id"
                :node="dept"
                :only-leaf-selectable="true"
                @clickNode="onDeptSelect"
            />
          </scroll-view>
        </view>
      </view>

      <!-- 数据展示区域 -->
      <view class="data-section">
        <view class="data-display">
          <view v-if="deptId === '-1'" class="empty-content">
            <text>请选择部门查看实时{{ getEnergyTypeName() }}能监控数据</text>
          </view>
          <view v-else-if="allRealTimeData.length === 0 && !loadingRealData" class="empty-content">
            <text>暂无实时数据</text>
          </view>
          <scroll-view v-else scroll-y class="data-scroll">
            <view class="data-grid">
              <view
                v-for="(item, index) in allRealTimeData"
                :key="index"
                class="data-card"
              >
                <view class="card-content">
                  <view class="card-header">
                    <text class="title">{{ getConfIdName(item.confId) }}</text>
                    <text class="area">面积: {{ item.area || '未知' }}</text>
                  </view>
                  <view class="device-info">
                    <text>设备编号: {{ item.deviceNo }}</text>
                  </view>
                  <view class="data-list">
                    <view class="data-item">
                      <text class="label">{{ getEnergyLabel() }}:</text>
                      <text class="value">{{ getEnergyValue(item) }} {{ getEnergyUnit() }}</text>
                    </view>
                    <template v-if="type === 1">
                      <view class="data-item">
                        <text class="label">电压:</text>
                        <text class="value">{{ item.eleVoltageVal || 0 }} V</text>
                      </view>
                      <view class="data-item">
                        <text class="label">电流:</text>
                        <text class="value">{{ item.eleCurrVal || 0 }} A</text>
                      </view>
                    </template>
                    <!-- 水能源暂时只显示使用量，不显示水压和流量 -->
                    <!-- 气能源暂时只显示总用气量，不显示气压和流量 -->
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDeptTree } from "@/api/commservice/comSysDept";
import { qryRealData, qryDeviceUse } from "@/api/HRP/weg";
import TreeItem from '@/components/tree-item.vue'

export default {
  name: 'RealConsumption',

  components: {
    TreeItem
  },

  props: {
    // 能源类型：1-电能，2-水能，3-气能
    type: {
      type: Number,
      default: 1
    }
  },

  data() {
    return {
      // 部门名称
      deptName: '',
      // 部门ID
      deptId: '-1',
      // 部门树数据
      treeData: [],
      // 加载状态
      loading: false,
      // 配置项
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用电类型列表
      deviceUseList: [],
      // 类型名称映射
      typeNameMap: {},
      // 实时数据
      allRealTimeData: [],
      // 是否正在加载实时数据
      loadingRealData: false,
      // 是否正在请求中
      isRequesting: false,
      // 部门树列表
      deptTreeList: [],
      // 是否显示部门选择框
      showDeptSelect: false,
      // 选中的部门名称
      selectedDeptName: ''
    };
  },

  watch: {
    deptName(val) {
      this.filterTree(val);
    }
  },

  created() {
    this.loadDepartmentTree();
    this.loadDeviceUseList();
  },

  methods: {
    // 筛选节点
    filterTree(value) {
      if (!value) return;
      this.treeData = this.treeData.filter(node => {
        return node.label.indexOf(value) !== -1;
      });
    },

    // 节点单击事件
    handleNodeClick(data) {
      if (this.isRequesting) {
        uni.showToast({
          title: '数据正在加载中，请稍候...',
          icon: 'none'
        });
        return;
      }

      this.deptId = String(data.id);
      this.loadAllRealTimeData(this.deptId);
    },

    // 加载部门树
    async loadDepartmentTree() {
      try {
        this.loading = true;
        const res = await getDeptTree();
        if (res.code === 200 && res.data) {
          this.deptTreeList = this.processDeptData(res.data);
          console.log('部门树数据:', this.deptTreeList);
        } else {
          console.error('获取部门树数据失败:', res);
          uni.showToast({
            title: '获取部门树数据失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载部门树失败:', error);
        uni.showToast({
          title: '加载部门树失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 处理部门数据
    processDeptData(depts) {
      if (!depts) return [];

      return depts.map(dept => ({
        id: dept.id,
        label: dept.label,
        children: dept.children ? this.processDeptData(dept.children) : [],
        isOpen: false
      }));
    },

    // 加载设备用途列表
    async loadDeviceUseList() {
      try {
        const params = {
          type: this.type
        };
        const res = await qryDeviceUse(params);
        if (res.code === 200 && res.data) {
          this.deviceUseList = res.data;
          this.deviceUseList.forEach(item => {
            this.typeNameMap[item.id] = item.typeName;
          });
        }
      } catch (error) {
        console.error('加载设备用途列表失败:', error);
        uni.showToast({
          title: '加载设备用途列表失败',
          icon: 'none'
        });
      }
    },

    // 延时函数
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },

    // 加载实时监控数据
    async loadRealTimeData(deptId, confId) {
      const params = {
        orgId: Number(deptId),
        confId: Number(confId),
        type: this.type
      };
      console.log('请求参数:', params);
      
      const maxRetries = 3;
      let retryCount = 0;
      
      while (retryCount < maxRetries) {
        try {
          const res = await qryRealData(params);
          if (res.code === 200 && res.data) {
            console.log('获取实时数据成功:', res.data);
            return res.data;
          } else {
            console.warn('获取实时数据结果为空或未成功:', res);
            return [];
          }
        } catch (err) {
          retryCount++;
          if (retryCount === maxRetries) {
            console.error('获取实时数据失败，已达到最大重试次数:', err);
            throw err;
          }
          console.warn(`获取实时数据失败，正在进行第${retryCount}次重试...`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒后重试
        }
      }
    },

    // 加载所有用电类型的实时监控数据
    async loadAllRealTimeData(orgId) {
      if (orgId === '-1' || this.deviceUseList.length === 0) return;

      if (this.isRequesting) {
        return;
      }

      try {
        this.isRequesting = true;
        this.loadingRealData = true;
        this.allRealTimeData = [];

        // 添加加载提示
        uni.showLoading({
          title: '正在加载数据...',
          mask: true
        });

        for (const deviceUse of this.deviceUseList) {
          try {
            console.log("org",orgId,"dev",deviceUse.id);
            const data = await this.loadRealTimeData(orgId, deviceUse.id);
            if (data && data.length > 0) {
              this.allRealTimeData = [...this.allRealTimeData, ...data];
            }
            // 增加请求间隔，避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 1000));
          } catch (err) {
            console.error(`加载 ${deviceUse.typeName} 数据失败:`, err);
            // 单个设备数据加载失败时继续加载其他设备
            continue;
          }
        }

        if (this.allRealTimeData.length === 0) {
          uni.showToast({
            title: '未查询到实时监控数据',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载实时数据失败:', error);
        uni.showToast({
          title: '加载实时数据失败: ' + (error.message || '未知错误'),
          icon: 'none'
        });
      } finally {
        this.loadingRealData = false;
        this.isRequesting = false;
        uni.hideLoading();
      }
    },

    // 获取配置ID对应的名称
    getConfIdName(confId) {
      if (!confId || !this.deviceUseList.length) {
        return '未知类型';
      }
      const deviceUse = this.deviceUseList.find(item => item.id === confId);
      return deviceUse ? deviceUse.typeName : '未知类型';
    },

    // 获取能源类型名称
    getEnergyTypeName() {
      switch(this.type) {
        case 1:
          return '电';
        case 2:
          return '水';
        case 3:
          return '气';
        default:
          return '能源';
      }
    },

    // 获取能源使用量标签
    getEnergyLabel() {
      switch(this.type) {
        case 1:
          return '电使用量';
        case 2:
          return '累计用水量';
        case 3:
          return '总用氧量';
        default:
          return '能源使用量';
      }
    },

    // 获取能源类型单位
    getEnergyUnit() {
      switch(this.type) {
        case 1:
          return 'kWh';
        case 2:
          return 'm³';
        case 3:
          return 'Nm³';
        default:
          return '未知单位';
      }
    },

    // 获取能源使用量值
    getEnergyValue(item) {
      switch(this.type) {
        case 1:
          return item.elePowerVal || 0;
        case 2:
          return item.waterVal || 0;
        case 3:
          return item.gasVal || item.elePowerVal || 0;
        default:
          return 0;
      }
    },

    // 切换部门选择框的显示状态
    toggleDeptSelect() {
      this.showDeptSelect = !this.showDeptSelect;
    },

    // 处理部门选择
    onDeptSelect(node) {
      this.deptId = String(node.id);
      this.selectedDeptName = node.label;
      this.showDeptSelect = false;
      this.loadAllRealTimeData(this.deptId);
    }
  }
};
</script>

<style lang="scss">
.monitoringContainer {
  height: 100vh;
  background-color: #ffffff;
  padding: 20rpx;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dept-section {
  position: relative;
  width: 100%;
  z-index: 999;

  .tree-select {
    width: 100%;
    height: 70rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    background: #fff;
    border: 1rpx solid #ddd;
    border-radius: 4rpx;
  }

  /* 下拉树状选择框 */
  .tree-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #fff;
    border: 1rpx solid #ddd;
    border-radius: 4rpx;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;

    .tree-scroll {
      max-height: 400rpx;
      padding: 20rpx;
    }
  }
}

/* 添加树节点的样式 */
::v-deep .tree-item {
  .tree-node {
    padding: 10rpx 0;
    display: flex;
    align-items: center;
  }

  .tree-children {
    margin-left: 20rpx;
  }
}

.data-section {
  flex: 1;
  
  .data-display {
    height: 100%;
    
    .data-scroll {
      height: 100%;
    }
    
    .data-grid {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      padding: 20rpx;
    }
    
    .data-card {
      background-color: #ffffff;
      border-radius: 8rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
      
      .card-content {
        padding: 20rpx;
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        
        .title {
          font-size: 28rpx;
          font-weight: bold;
          color: #303133;
        }
        
        .area {
          font-size: 24rpx;
          color: #606266;
        }
      }
      
      .device-info {
        margin-bottom: 20rpx;
        font-size: 24rpx;
        color: #606266;
      }
      
      .data-list {
        border-top: 1px solid #ebeef5;
        padding-top: 20rpx;
      }
      
      .data-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        
        .label {
          color: #606266;
          font-size: 28rpx;
        }
        
        .value {
          font-weight: bold;
          color: #303133;
          font-size: 32rpx;
        }
      }
    }
  }
  
  .empty-content {
    height: 200rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #909399;
  }
}
</style>
