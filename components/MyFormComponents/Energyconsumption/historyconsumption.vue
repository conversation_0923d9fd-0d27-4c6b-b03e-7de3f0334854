<template>
  <view class="monitoringContainer">
    <view class="content-wrapper">
      <!-- 部门选择区域 -->
      <view class="dept-section">
        <view class="tree-select" @tap="toggleDeptSelect">
          <text class="selected-text">{{ selectedDeptName || '请选择部门' }}</text>
          <uni-icons type="bottom" size="16" :color="showDeptSelect ? '#2A5EFF' : '#666'"></uni-icons>
        </view>

        <!-- 下拉树状选择框 -->
        <view class="tree-dropdown" v-if="showDeptSelect">
          <scroll-view
              scroll-y
              class="tree-scroll"
          >
            <tree-item
                v-for="dept in deptTreeList"
                :key="dept.id"
                :node="dept"
                :only-leaf-selectable="true"
                @clickNode="onDeptSelect"
            />
          </scroll-view>
        </view>
      </view>

      <!-- 数据展示区域 -->
      <view class="data-section">
        <!-- 筛选条件 -->
        <view class="filter-section">
          <view class="filter-item">
            <text class="filter-label">设备类型：</text>
            <uni-data-select
                v-model="selectedDeviceTypeId"
                :localdata="deviceTypeOptions"
                placeholder="请选择设备类型"
                :clear="true"
                @change="handleDeviceTypeChange"
            />
          </view>

          <view class="filter-item">
            <text class="filter-label">设备列表：</text>
            <uni-data-select
                v-model="selectedDeviceNo"
                :localdata="deviceOptions"
                placeholder="请选择设备"
                :clear="true"
                @change="handleDeviceChange"
            />
          </view>

          <view class="filter-item">
            <text class="filter-label">选择日期：</text>
            <DayPicker
                :value="selectedDateArray"
                @change="handleDateChange"
                placeholder="请选择日期"
                :singleMode="true"
                :disableFutureDates="true"
            />
          </view>

          <button
              class="query-btn"
              @tap="loadDataWithDate"
              :disabled="!selectedDate || !selectedDeviceTypeId || !selectedDeviceNo"
          >查询</button>
        </view>

        <!-- 数据展示 -->
        <view class="data-display">
          <view v-if="deptId === '-1'" class="empty-content">
            <text>请选择部门查看历史能源数据</text>
          </view>
          <view v-else-if="!hasAnyData && !loadingHistoryData" class="empty-content">
            <text>暂无历史数据</text>
          </view>
          <view v-else class="charts-container">
            <!-- 能源使用量折线图 -->
            <view class="chart-card">
              <view class="chart-header">
                <view class="chart-title">
                  <text>{{ getChartTitle() }} ({{ resourceTitleMap[type].unit }})</text>
                </view>
                <view class="chart-switch">
                  <button
                    class="switch-btn"
                    :class="{ active: powerChartMode === 'original' }"
                    @tap="switchPowerChartMode('original')"
                  >原始</button>
                  <button
                    class="switch-btn"
                    :class="{ active: powerChartMode === 'hourly' }"
                    @tap="switchPowerChartMode('hourly')"
                  >小时</button>
                </view>
              </view>
              <qiun-data-charts
                  type="line"
                  :opts="powerChartOpts"
                  :chartData="powerChartData"
                  :ontouch="true"
                  :ontap="true"
                  :tooltipShow="true"
                  @getIndex="getPowerChartIndex"
              />
            </view>

            <!-- 压力折线图 - 水表和气表时不显示 -->
            <view v-if="type === 1" class="chart-card">
              <view class="chart-header">
                <view class="chart-title">
                  <text>{{ resourceTitleMap[type].vName }} ({{ resourceTitleMap[type].vUnit }})</text>
                </view>
                <view class="chart-switch">
                  <button
                    class="switch-btn"
                    :class="{ active: voltageChartMode === 'original' }"
                    @tap="switchVoltageChartMode('original')"
                  >原始</button>
                  <button
                    class="switch-btn"
                    :class="{ active: voltageChartMode === 'hourly' }"
                    @tap="switchVoltageChartMode('hourly')"
                  >小时</button>
                </view>
              </view>
              <qiun-data-charts
                  type="line"
                  :opts="voltageChartOpts"
                  :chartData="voltageChartData"
                  :ontouch="true"
                  :ontap="true"
                  :tooltipShow="true"
                  @getIndex="getVoltageChartIndex"
              />
            </view>

            <!-- 流量折线图 - 水表和气表时不显示 -->
            <view v-if="type === 1" class="chart-card">
              <view class="chart-header">
                <view class="chart-title">
                  <text>{{ resourceTitleMap[type].cName }} ({{ resourceTitleMap[type].cUnit }})</text>
                </view>
                <view class="chart-switch">
                  <button
                    class="switch-btn"
                    :class="{ active: currentChartMode === 'original' }"
                    @tap="switchCurrentChartMode('original')"
                  >原始</button>
                  <button
                    class="switch-btn"
                    :class="{ active: currentChartMode === 'hourly' }"
                    @tap="switchCurrentChartMode('hourly')"
                  >小时</button>
                </view>
              </view>
              <qiun-data-charts
                  type="line"
                  :opts="currentChartOpts"
                  :chartData="currentChartData"
                  :ontouch="true"
                  :ontap="true"
                  :tooltipShow="true"
                  @getIndex="getCurrentChartIndex"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDeptTree } from "@/api/commservice/comSysDept";
import { wegHisQry, qryDeviceUse, qryWegDevice } from "@/api/HRP/weg";
import TreeItem from '@/components/tree-item.vue'
import QiunDataCharts from "../../../uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue";
import DayPicker from '@/components/MyFormComponents/time-picker/DayPicker.vue';

export default {
  name: 'HistoryConsumption',
  components: {
    QiunDataCharts,
    TreeItem,
    DayPicker
  },
  props: {
    defaultDeptId: {
      type: String,
      default: '-1'
    },
    type: {
      type: Number,
      default: 1 // 默认为1，表示电表；2为水表；3为气表
    }
  },
  data() {
    return {
      // 部门名称
      deptName: '',
      // 部门ID
      deptId: this.defaultDeptId,
      // 部门树数据
      treeData: [],
      // 加载状态
      loading: false,
      // 配置项
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用电类型列表
      deviceUseList: [],
      // 选中的设备类型ID
      selectedDeviceTypeId: '',
      // 设备列表
      deviceList: [],
      // 是否正在加载设备列表
      loadingDeviceList: false,
      // 选中的设备编号
      selectedDeviceNo: '',
      // 类型名称映射
      typeNameMap: {},
      // 选择的日期
      selectedDate: '',
      // DayPicker组件需要的数组格式日期
      selectedDateArray: [],
      // 历史数据
      historyData: {
        powerData: null,
        voltageData: null,
        electricData: null,
        waterData: null,
        gasData: null
      },
      // 是否正在加载历史数据
      loadingHistoryData: false,
      // 是否正在请求中
      isRequesting: false,
      // 资源类型标题映射
      resourceTitleMap: {
        1: { title: '电表', unit: 'kWh', vName: '电压', vUnit: 'V', cName: '电流', cUnit: 'A' },
        2: { title: '累计用水量', unit: 'm³', vName: '水压', vUnit: 'MPa', cName: '流量', cUnit: 'L/s' },
        3: { title: '总用氧量', unit: 'Nm³', vName: '气压', vUnit: 'MPa', cName: '流量', cUnit: 'L/s' }
      },
      // 是否显示部门选择框
      showDeptSelect: false,
      // 选中的部门名称
      selectedDeptName: '',
      // 部门树数据
      deptTreeList: [],
      // 添加图表配置
      powerChartOpts: {
        color: ['#1890FF'],
        padding: [15, 15, 0, 15],
        xAxis: {
          disableGrid: true,
          itemCount: 5,
          scrollShow: true,
          scrollAlign: 'left',
          scrollBackgroundColor: '#F5F5F5',
          scrollColor: '#1890FF',
          boundaryGap: true  // 改为true，确保单条数据点不贴边
        },
        yAxis: {
          title: '使用量',
          titleFontSize: 12,
          titleFontColor: '#666666',
          min: 0,
          position: 'left',
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow'
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: true,
            borderWidth: 0,
            borderRadius: 4,
            borderColor: '#000000',
            borderOpacity: 0.7,
            bgColor: '#000000',
            bgOpacity: 0.7,
            gridType: 'solid',
            dashLength: 4,
            gridColor: '#CCCCCC',
            fontColor: '#FFFFFF',
            splitLine: true,
            horizentalLine: true,
            xAxisLabel: true,
            yAxisLabel: true,
            labelBgColor: '#FFFFFF',
            labelBgOpacity: 0.7,
            labelFontColor: '#666666'
          }
        },
        dataLabel: false,
        dataPointShape: true,
        enableScroll: true,
        touchMoveLimit: 60
      },
      voltageChartOpts: {
        color: ['#91CB74'],
        padding: [15, 15, 0, 15],
        xAxis: {
          disableGrid: true,
          itemCount: 5,
          scrollShow: true,
          scrollAlign: 'left',
          scrollBackgroundColor: '#F5F5F5',
          scrollColor: '#91CB74',
          boundaryGap: true
        },
        yAxis: {
          title: '压力',
          titleFontSize: 12,
          titleFontColor: '#666666',
          min: 0,
          position: 'left'
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow'
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: true,
            borderWidth: 0,
            borderRadius: 4,
            borderColor: '#000000',
            borderOpacity: 0.7,
            bgColor: '#000000',
            bgOpacity: 0.7,
            gridType: 'solid',
            dashLength: 4,
            gridColor: '#CCCCCC',
            fontColor: '#FFFFFF',
            splitLine: true,
            horizentalLine: true,
            xAxisLabel: true,
            yAxisLabel: true,
            labelBgColor: '#FFFFFF',
            labelBgOpacity: 0.7,
            labelFontColor: '#666666'
          }
        },
        dataLabel: false,
        dataPointShape: true,
        enableScroll: true,
        touchMoveLimit: 60
      },
      currentChartOpts: {
        color: ['#FAC858'],
        padding: [15, 15, 0, 15],
        xAxis: {
          disableGrid: true,
          itemCount: 5,
          scrollShow: true,
          scrollAlign: 'left',
          scrollBackgroundColor: '#F5F5F5',
          scrollColor: '#FAC858',
          boundaryGap: true
        },
        yAxis: {
          title: '流量',
          titleFontSize: 12,
          titleFontColor: '#666666',
          min: 0,
          position: 'left'
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow'
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: true,
            borderWidth: 0,
            borderRadius: 4,
            borderColor: '#000000',
            borderOpacity: 0.7,
            bgColor: '#000000',
            bgOpacity: 0.7,
            gridType: 'solid',
            dashLength: 4,
            gridColor: '#CCCCCC',
            fontColor: '#FFFFFF',
            splitLine: true,
            horizentalLine: true,
            xAxisLabel: true,
            yAxisLabel: true,
            labelBgColor: '#FFFFFF',
            labelBgOpacity: 0.7,
            labelFontColor: '#666666'
          }
        },
        dataLabel: false,
        dataPointShape: true,
        enableScroll: true,
        touchMoveLimit: 60
      },
      // 添加图表数据
      powerChartData: {
        categories: [],
        series: [{
          name: '使用量',
          data: []
        }]
      },
      voltageChartData: {
        categories: [],
        series: [{
          name: '压力',
          data: []
        }]
      },
      currentChartData: {
        categories: [],
        series: [{
          name: '流量',
          data: []
        }]
      },
      // 各图表的数据显示模式：'original' 原始数据，'hourly' 按小时聚合
      powerChartMode: 'original',      // 能源使用量图表模式
      voltageChartMode: 'original',    // 电压/压力图表模式
      currentChartMode: 'original'     // 电流/流量图表模式
    };
  },
  computed: {
    // 检查是否有任何历史数据
    hasAnyData() {
      return this.historyData.powerData ||
             this.historyData.voltageData ||
             this.historyData.electricData ||
             this.historyData.waterData ||
             this.historyData.gasData;
    },

    // 设备类型选项 - 转换为uni-data-select需要的格式
    deviceTypeOptions() {
      return this.deviceUseList.map(item => ({
        text: item.typeName,
        value: item.id
      }));
    },

    // 设备选项 - 转换为uni-data-select需要的格式
    deviceOptions() {
      return this.deviceList.map(item => ({
        text: item.dhDeviceNo,
        value: item.dhDeviceNo
      }));
    }
  },
  watch: {
    deptName(val) {
      this.filterTree(val);
    },
    'historyData.powerData': {
      handler() {
        this.$nextTick(() => {
          this.initCharts();
        });
      },
      deep: true
    },

    selectedDate(val) {
      // 同步更新数组格式的日期
      this.selectedDateArray = val ? [val] : [];
    }
  },
  created() {
    this.loadDepartmentTree();
    this.loadDeviceUseList();
    this.selectedDate = this.formatDate(new Date());
    // 初始化数组格式的日期
    this.selectedDateArray = [this.selectedDate];
  },
  mounted() {
    // 延迟初始化图表，确保canvas已经渲染
    setTimeout(() => {
      console.log('开始初始化图表');
      this.initCharts();
    }, 300);
  },
  onShow() {
    // 每次页面显示时重新调整图表大小
    this.$nextTick(() => {
      this.initCharts();
    });
  },
  beforeDestroy() {
    if (this.deviceTypeChangeTimer) {
      clearTimeout(this.deviceTypeChangeTimer);
    }
  },
  methods: {
    // 获取图表标题
    getChartTitle() {
      const title = this.resourceTitleMap[this.type].title;
      // 电表需要添加"使用量"，水表和气表的title已经包含了完整名称
      return this.type === 1 ? `${title}使用量` : title;
    },

    // 格式化日期为yyyy-MM-dd格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 智能数值格式化 - 返回小数位数
    getYAxisTofix(data) {
      if (!data || data.length === 0) return 0;

      const maxValue = Math.max(...data);
      const minValue = Math.min(...data);
      const range = maxValue - minValue;
      const avgValue = data.reduce((sum, val) => sum + val, 0) / data.length;

      // 单条数据的特殊处理
      if (data.length === 1) {
        if (maxValue < 1) return 3;
        if (maxValue < 10) return 2;
        if (maxValue < 100) return 1;
        return 0;
      }

      // 计算变化率
      const changeRate = avgValue > 0 ? range / avgValue : 0;

      // 优先基于变化率决定精度（无论数值大小）
      if (changeRate < 0.001) return 4;  // 变化率小于0.1%，显示4位小数
      if (changeRate < 0.01) return 3;   // 变化率小于1%，显示3位小数
      if (changeRate < 0.05) return 2;   // 变化率小于5%，显示2位小数
      if (changeRate < 0.1) return 1;    // 变化率小于10%，显示1位小数

      // 变化率较大时，基于数值大小决定格式
      if (maxValue < 1) return 3;
      if (maxValue < 10) return 2;
      if (maxValue < 100) return 1;
      if (maxValue < 1000 && range < 50) return 1;  // 百位数但变化范围小于50时显示1位小数

      return 0;  // 大数值且变化率大时显示整数
    },

    // 获取优化的y轴配置 - 针对小范围高精度数据
    getOptimizedYAxisConfig(data) {
      if (!data || data.length === 0) return {};

      const maxValue = Math.max(...data);
      const minValue = Math.min(...data);
      const range = maxValue - minValue;
      const avgValue = data.reduce((sum, val) => sum + val, 0) / data.length;
      const changeRate = avgValue > 0 ? range / avgValue : 0;

      // 如果变化率很小（小于2%），使用优化配置来放大显示效果
      if (changeRate < 0.02 && avgValue > 100) {
        const buffer = range * 0.1; // 10%的缓冲区
        const optimizedMin = Math.max(0, minValue - buffer);
        const optimizedMax = maxValue + buffer;

        return {
          min: optimizedMin,
          max: optimizedMax,
          splitNumber: 8, // 增加刻度数量
          tofix: 3 // 显示3位小数
        };
      }

      // 默认配置
      return {
        tofix: this.getYAxisTofix(data),
        min: this.getYAxisMin(data)
      };
    },

    // 动态最小值设置
    getYAxisMin(data, chartType = 'power') {
      if (!data || data.length === 0) return 0;

      const minValue = Math.min(...data);
      const maxValue = Math.max(...data);
      const range = maxValue - minValue;
      const avgValue = data.reduce((sum, val) => sum + val, 0) / data.length;

      // 如果所有数据都是0或负数，返回0
      if (maxValue <= 0) return 0;

      // 单条数据的特殊处理
      if (data.length === 1) {
        // 单条数据时，设置一个合理的最小值来显示数据点
        if (minValue > 100) {
          return Math.floor(minValue * 0.9);
        } else if (minValue > 10) {
          return Math.floor(minValue * 0.8);
        } else if (minValue > 1) {
          return Math.floor(minValue * 0.5);
        }
        return 0;
      }

      // 计算变化率（范围相对于平均值的比例）
      const changeRate = avgValue > 0 ? range / avgValue : 0;

      // 如果变化率很小（数据很接近），需要放大显示效果
      if (changeRate < 0.1 && minValue > 0) {
        // 变化率小于10%时，使用更激进的最小值策略
        const buffer = range * 2; // 增加2倍范围作为缓冲
        const suggestedMin = Math.max(0, minValue - buffer);

        // 对于电压类数据，特殊处理
        if (chartType === 'voltage' && minValue > 50) {
          return Math.max(suggestedMin, minValue * 0.9);
        }

        return suggestedMin;
      }

      // 对于变化率较小但不是很小的情况
      if (changeRate < 0.3 && minValue > 0) {
        const buffer = range * 0.5;
        const suggestedMin = Math.max(0, minValue - buffer);

        // 电压类数据的特殊处理
        if (chartType === 'voltage' && minValue > 100) {
          return Math.max(suggestedMin, minValue * 0.95);
        }

        return suggestedMin;
      }

      // 对于电压类数据，如果最小值较大，适当提高起始点
      if (chartType === 'voltage' && minValue > 100) {
        return Math.floor(minValue * 0.98);
      }

      // 默认情况
      return 0;
    },

    // 切换能源使用量图表显示模式
    switchPowerChartMode(mode) {
      if (this.powerChartMode === mode) return;

      this.powerChartMode = mode;

      // 重新初始化能源使用量图表
      this.$nextTick(() => {
        this.initPowerChart();
      });
    },

    // 切换电压/压力图表显示模式
    switchVoltageChartMode(mode) {
      if (this.voltageChartMode === mode) return;

      this.voltageChartMode = mode;

      // 重新初始化电压图表
      this.$nextTick(() => {
        this.initVoltageChart();
      });
    },

    // 切换电流/流量图表显示模式
    switchCurrentChartMode(mode) {
      if (this.currentChartMode === mode) return;

      this.currentChartMode = mode;

      // 重新初始化电流图表
      this.$nextTick(() => {
        this.initCurrentChart();
      });
    },



    // 日期变更处理
    handleDateChange(dateArray) {
      // DayPicker返回的是数组格式，取第一个元素作为选中日期
      this.selectedDate = dateArray && dateArray.length > 0 ? dateArray[0] : '';
      this.selectedDateArray = dateArray || [];
      // 日期变更时不自动加载数据，等用户点击查询按钮
    },

    // 使用防抖处理设备类型变更
    debouncedLoadDeviceList(value) {
      if (this.deviceTypeChangeTimer) {
        clearTimeout(this.deviceTypeChangeTimer);
      }

      if (!value || this.deptId === '-1') {
        this.deviceList = [];
        this.selectedDeviceNo = '';
        return;
      }

      this.deviceTypeChangeTimer = setTimeout(() => {
        this.loadDeviceList(this.deptId, value);
      }, 300);
    },

    // 设备类型变更处理
    handleDeviceTypeChange(selectedValue) {
      console.log('设备类型变更事件触发:', selectedValue);
      this.selectedDeviceTypeId = selectedValue;

      // 清空设备选择
      this.selectedDeviceNo = '';

      if (this.deptId !== '-1' && selectedValue) {
        console.log('加载设备列表，部门ID:', this.deptId, '设备类型ID:', selectedValue);
        this.loadDeviceList(this.deptId, selectedValue);
      }
    },

    // 设备变更处理
    handleDeviceChange(selectedValue) {
      console.log('设备变更事件触发:', selectedValue);
      this.selectedDeviceNo = selectedValue;
    },

    // 加载带日期的数据
    loadDataWithDate() {
      if (this.deptId === '-1') {
        uni.showToast({
          title: '请先选择部门',
          icon: 'none'
        });
        return;
      }

      if (!this.selectedDeviceTypeId) {
        uni.showToast({
          title: '请选择设备类型',
          icon: 'none'
        });
        return;
      }

      if (!this.selectedDeviceNo) {
        uni.showToast({
          title: '请选择设备',
          icon: 'none'
        });
        return;
      }

      if (!this.selectedDate) {
        uni.showToast({
          title: '请选择日期',
          icon: 'none'
        });
        return;
      }

      this.loadHistoryData(this.deptId, this.selectedDeviceTypeId, this.selectedDeviceNo);
    },

    // 筛选节点
    filterTree(value) {
      if (!value) return;
      // 在uniapp中实现树形筛选
      this.treeData = this.treeData.filter(node => {
        return node.label.indexOf(value) !== -1;
      });
    },

    // 节点单击事件
    handleNodeClick(data) {
      if (this.isRequesting) {
        uni.showToast({
          title: '数据正在加载中，请稍候...',
          icon: 'none'
        });
        return;
      }

      this.deptId = String(data.id);

      if (this.selectedDeviceTypeId) {
        this.loadDeviceList(this.deptId, this.selectedDeviceTypeId);
      }
    },

    // 加载部门树
    async loadDepartmentTree() {
      try {
        const res = await getDeptTree();
        if (res.code === 200 && res.data) {
          this.deptTreeList = this.processDeptData(res.data);
          console.log('部门树数据:', this.deptTreeList);
        } else {
          console.error('获取部门树数据失败:', res);
          uni.showToast({
            title: '获取部门树数据失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载部门树失败:', error);
        uni.showToast({
          title: '加载部门树失败',
          icon: 'none'
        });
      }
    },

    // 处理部门数据
    processDeptData(depts) {
      if (!depts) return [];

      return depts.map(dept => ({
        id: dept.id,
        label: dept.label,
        children: dept.children ? this.processDeptData(dept.children) : [],
        isOpen: false
      }));
    },

    // 切换部门选择框的显示状态
    toggleDeptSelect() {
      this.showDeptSelect = !this.showDeptSelect;
    },

    // 处理部门选择
    onDeptSelect(node) {
      console.log('选择部门:', node);
      this.deptId = String(node.id);
      this.selectedDeptName = node.label;
      this.showDeptSelect = false;
      
      // 如果已经选择了设备类型，则加载设备列表
      if (this.selectedDeviceTypeId) {
        console.log('加载设备列表，参数:', { deptId: this.deptId, deviceTypeId: this.selectedDeviceTypeId });
        this.loadDeviceList(this.deptId, this.selectedDeviceTypeId);
      }
    },

    // 加载设备用途列表
    async loadDeviceUseList() {
      try {
        console.log('开始加载设备用途列表');
        const params = {
          type: this.type
        };
        const res = await qryDeviceUse(params);
        console.log('设备用途列表响应:', res);
        
        if (res.code === 200 && res.data) {
          this.deviceUseList = res.data;
          console.log('设置设备用途列表:', this.deviceUseList);

          this.typeNameMap = {};
          this.deviceUseList.forEach(item => {
            this.typeNameMap[item.id] = item.typeName;
          });

          if (this.deviceUseList.length > 0) {
            this.selectedDeviceTypeId = this.deviceUseList[0].id;
            console.log('设置默认设备类型ID:', this.selectedDeviceTypeId);
            console.log('当前部门ID:', this.deptId);

            // 只有在部门已选择时才加载设备列表
            if (this.deptId !== '-1') {
              console.log('加载默认设备列表');
              this.loadDeviceList(this.deptId, this.selectedDeviceTypeId);
            }
          }
        }
      } catch (error) {
        console.error('加载设备用途列表失败:', error);
        uni.showToast({
          title: '加载设备用途列表失败',
          icon: 'none'
        });
      }
    },

    // 加载设备列表
    async loadDeviceList(orgId, confId) {
      if (!orgId || !confId) {
        console.warn('加载设备列表参数无效:', { orgId, confId });
        return;
      }

      const requestId = Date.now().toString();
      this.deviceListRequestId = requestId;

      try {
        if (this.currentDeviceListRequest) {
          this.currentDeviceListRequest = null;
        }

        this.loadingDeviceList = true;
        console.log('开始加载设备列表，参数:', { orgId, confId });

        const params = {
          orgId: Number(orgId),
          confId: Number(confId)
        };

        this.currentDeviceListRequest = qryWegDevice(params);
        const res = await this.currentDeviceListRequest;

        if (this.deviceListRequestId !== requestId) {
          console.log('请求已过期，忽略响应');
          return;
        }

        this.deviceList = [];
        this.selectedDeviceNo = '';

        if (res.code === 200 && res.data && res.data.length > 0) {
          console.log('获取设备列表成功:', res.data);
          this.deviceList = res.data;

          if (this.deviceList.length > 0) {
            this.selectedDeviceNo = this.deviceList[0].dhDeviceNo;
            console.log('设置默认设备:', this.selectedDeviceNo);
          }
        } else {
          console.warn('获取设备列表结果为空或未成功:', res);
          uni.showToast({
            title: `未查询到设备类型[${this.getConfIdName(confId)}]的设备列表数据`,
            icon: 'none'
          });
        }
      } catch (error) {
        if (this.deviceListRequestId === requestId) {
          console.error('加载设备列表失败:', error);
          if (error.message && error.message.indexOf('请勿重复提交') > -1) {
            console.warn('忽略重复提交错误');
          } else {
            uni.showToast({
              title: '加载设备列表失败: ' + (error.message || '未知错误'),
              icon: 'none'
            });
          }
        }
      } finally {
        this.currentDeviceListRequest = null;
        if (this.deviceListRequestId === requestId) {
          this.loadingDeviceList = false;
        }
      }
    },

    // 加载历史数据
    async loadHistoryData(orgId, confId, deviceNo) {
      if (orgId === '-1' || !confId || !deviceNo) {
        console.warn('加载历史数据参数无效:', { orgId, confId, deviceNo });
        return;
      }

      if (this.isRequesting) {
        console.log('已有请求正在进行中，忽略本次请求');
        return;
      }

      try {
        this.isRequesting = true;
        this.loadingHistoryData = true;
        this.historyData = {
          powerData: null,
          voltageData: null,
          electricData: null,
          waterData: null,
          gasData: null
        };

        // 显示加载提示
        uni.showLoading({
          title: '正在加载数据...',
          mask: true
        });

        const maxRetries = 3;
        let retryCount = 0;
        let success = false;

        while (retryCount < maxRetries && !success) {
          try {
            console.log(`第${retryCount + 1}次尝试加载历史数据`);
            const data = await this.loadHistoryDataByDate(orgId, confId, deviceNo);
            if (data) {
              this.historyData = {
                powerData: data.powerData || null,
                voltageData: data.voltageData || null,
                electricData: data.electricData || null,
                waterData: data.waterData || null,
                gasData: data.gasData || null
              };
              success = true;
              console.log('历史数据加载成功');
              // 数据加载成功后，确保在下一个tick初始化图表
              this.$nextTick(() => {
                console.log('数据更新后初始化图表');
                setTimeout(() => {
                  this.initCharts();
                }, 300);
              });
            }
          } catch (err) {
            retryCount++;
            console.error(`第${retryCount}次加载失败:`, err);
            
            if (retryCount < maxRetries) {
              console.log(`等待1秒后重试...`);
              await new Promise(resolve => setTimeout(resolve, 1000));
            } else {
              uni.showToast({
                title: '加载历史数据失败，请稍后重试',
                icon: 'none'
              });
            }
          }
        }

        if (!success) {
          uni.showToast({
            title: '加载历史数据失败，请稍后重试',
            icon: 'none'
          });
        }

        // 检查是否有任何数据
        const hasData = this.historyData.powerData ||
                       this.historyData.voltageData ||
                       this.historyData.electricData ||
                       this.historyData.waterData ||
                       this.historyData.gasData;

        if (!hasData) {
          uni.showToast({
            title: '未查询到历史数据',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载历史数据失败:', error);
        uni.showToast({
          title: '加载历史数据失败: ' + (error.message || '未知错误'),
          icon: 'none'
        });
      } finally {
        this.loadingHistoryData = false;
        this.isRequesting = false;
        uni.hideLoading();
      }
    },

    // 根据日期加载历史数据
    async loadHistoryDataByDate(deptId, confId, deviceNo) {
      const params = {
        orgId: Number(deptId),
        confId: Number(confId),
        dhDeviceNo: deviceNo,
        type: this.type,
        qryDay: this.selectedDate
      };

      console.log('请求历史数据参数:', params);
      
      try {
        const res = await wegHisQry(params);
        if (res.code === 200 && res.data) {
          console.log('获取历史数据成功:', res.data);
          return res.data;
        } else {
          console.warn('获取历史数据结果为空或未成功:', res);
          return null;
        }
      } catch (err) {
        console.error('获取历史数据失败:', err);
        throw err;
      }
    },

    // 获取配置ID对应的名称
    getConfIdName(confId) {
      if (!confId || !this.deviceUseList.length) {
        return '未知类型';
      }
      return this.typeNameMap[confId] || '未知类型';
    },

    // 初始化所有图表
    initCharts() {
      console.log('initCharts被调用');
      console.log('当前类型:', this.type);
      console.log('历史数据:', this.historyData);
      this.disposeCharts();

      // 检查主图表数据（使用量）
      const hasMainData = (this.type === 2 && this.historyData.waterData) ||
                         (this.type === 3 && this.historyData.gasData) ||
                         (this.type === 1 && this.historyData.powerData);

      if (hasMainData) {
        console.log('初始化能源使用量图表');
        this.initPowerChart();
      }

      // 只有电表需要显示电压和电流图表
      if (this.type === 1) {
        if (this.historyData.voltageData) {
          console.log('初始化电压图表');
          this.initVoltageChart();
        }

        if (this.historyData.electricData) {
          console.log('初始化电流图表');
          this.initCurrentChart();
        }
      }
    },

    // 初始化能源使用量图表
    initPowerChart() {
      // 根据类型获取对应的数据
      let sourceData;
      if (this.type === 2) {
        // 水表数据
        sourceData = this.historyData.waterData;
        console.log('水表数据:', sourceData);
      } else if (this.type === 3) {
        // 气表数据
        sourceData = this.historyData.gasData;
        console.log('气表数据:', sourceData);
      } else {
        // 电表数据
        sourceData = this.historyData.powerData;
      }

      if (!sourceData || sourceData.length === 0) {
        console.log('没有能源使用量数据或数据为空');
        console.log('sourceData:', sourceData);
        return;
      }

      console.log('开始处理数据，数据长度:', sourceData.length);
      console.log('数据内容:', sourceData);

      let timeData, valueData;
      const title = this.resourceTitleMap[this.type].title;

      if (this.powerChartMode === 'hourly') {
        // 按小时聚合数据
        const hourlyData = {};
        sourceData.forEach(item => {
          const time = new Date(item.time);
          const hourKey = `${String(time.getHours()).padStart(2, '0')}:00`;
          if (!hourlyData[hourKey]) {
            hourlyData[hourKey] = {
              total: 0,
              count: 0
            };
          }
          hourlyData[hourKey].total += parseFloat(item.value);
          hourlyData[hourKey].count += 1;
        });

        // 转换为数组并计算平均值
        timeData = [];
        valueData = [];
        Object.keys(hourlyData).sort().forEach(hour => {
          timeData.push(hour);
          valueData.push(hourlyData[hour].total / hourlyData[hour].count);
        });
      } else {
        // 原始数据模式
        const sortedData = [...sourceData].sort((a, b) => {
          return new Date(a.time) - new Date(b.time);
        });

        timeData = sortedData.map(item => {
          const time = new Date(item.time);
          const hours = String(time.getHours()).padStart(2, '0');
          const minutes = String(time.getMinutes()).padStart(2, '0');
          return `${hours}:${minutes}`;
        });

        valueData = sortedData.map(item => parseFloat(item.value));
      }

      console.log('处理后的时间数据:', timeData);
      console.log('处理后的数值数据:', valueData);

      // 单条数据的特殊处理
      if (valueData.length === 1) {
        console.log('检测到单条数据，进行特殊处理');
        // 为单条数据设置合适的Y轴范围
        const singleValue = valueData[0];
        const yAxisMax = singleValue > 0 ? Math.ceil(singleValue * 1.2) : 10;

        // 单条数据也使用优化配置
        const optimizedConfig = this.getOptimizedYAxisConfig(valueData);

        this.powerChartOpts = {
          ...this.powerChartOpts,
          yAxis: {
            ...this.powerChartOpts.yAxis,
            ...optimizedConfig,
            max: yAxisMax // 单条数据保持原有的max逻辑
          }
        };
      } else {
        // 多条数据的正常处理 - 使用优化配置
        const optimizedConfig = this.getOptimizedYAxisConfig(valueData);

        console.log('Y轴优化配置:', optimizedConfig);

        this.powerChartOpts = {
          ...this.powerChartOpts,
          yAxis: {
            ...this.powerChartOpts.yAxis,
            ...optimizedConfig
          }
        };
      }

      // 直接更新图表数据
      const seriesName = this.type === 1 ? `${title}使用量` : title;
      this.powerChartData = {
        categories: timeData,
        series: [{
          name: seriesName,
          data: valueData
        }]
      };

      console.log('最终图表数据:', this.powerChartData);

      // 强制触发图表重新渲染
      this.$nextTick(() => {
        console.log('强制刷新图表');
      });
    },

    // 初始化电压图表（仅用于电表和气表）
    initVoltageChart() {
      if (!this.historyData.voltageData) {
        console.log('没有电压数据');
        return;
      }

      let timeData, valueData;
      const name = this.resourceTitleMap[this.type].vName;

      if (this.voltageChartMode === 'hourly') {
        // 按小时聚合数据
        const hourlyData = {};
        this.historyData.voltageData.forEach(item => {
          const time = new Date(item.time);
          const hourKey = `${String(time.getHours()).padStart(2, '0')}:00`;
          if (!hourlyData[hourKey]) {
            hourlyData[hourKey] = {
              total: 0,
              count: 0
            };
          }
          hourlyData[hourKey].total += parseFloat(item.value);
          hourlyData[hourKey].count += 1;
        });

        // 转换为数组并计算平均值
        timeData = [];
        valueData = [];
        Object.keys(hourlyData).sort().forEach(hour => {
          timeData.push(hour);
          valueData.push(hourlyData[hour].total / hourlyData[hour].count);
        });
      } else {
        // 原始数据模式
        const sortedData = [...this.historyData.voltageData].sort((a, b) => {
          return new Date(a.time) - new Date(b.time);
        });

        timeData = sortedData.map(item => {
          const time = new Date(item.time);
          const hours = String(time.getHours()).padStart(2, '0');
          const minutes = String(time.getMinutes()).padStart(2, '0');
          return `${hours}:${minutes}`;
        });

        valueData = sortedData.map(item => parseFloat(item.value));
      }

      // 动态更新Y轴配置 - 使用优化配置
      const optimizedConfig = this.getOptimizedYAxisConfig(valueData);

      // 创建新的配置对象以确保响应式更新
      this.voltageChartOpts = {
        ...this.voltageChartOpts,
        yAxis: {
          ...this.voltageChartOpts.yAxis,
          ...optimizedConfig
        }
      };

      // 直接更新图表数据
      this.voltageChartData = {
        categories: timeData,
        series: [{
          name: name,
          data: valueData
        }]
      };
    },

    // 初始化电流图表（仅用于电表和气表）
    initCurrentChart() {
      if (!this.historyData.electricData) {
        console.log('没有电流数据');
        return;
      }

      let timeData, valueData;
      const name = this.resourceTitleMap[this.type].cName;

      if (this.currentChartMode === 'hourly') {
        // 按小时聚合数据
        const hourlyData = {};
        this.historyData.electricData.forEach(item => {
          const time = new Date(item.time);
          const hourKey = `${String(time.getHours()).padStart(2, '0')}:00`;
          if (!hourlyData[hourKey]) {
            hourlyData[hourKey] = {
              total: 0,
              count: 0
            };
          }
          hourlyData[hourKey].total += parseFloat(item.value);
          hourlyData[hourKey].count += 1;
        });

        // 转换为数组并计算平均值
        timeData = [];
        valueData = [];
        Object.keys(hourlyData).sort().forEach(hour => {
          timeData.push(hour);
          valueData.push(hourlyData[hour].total / hourlyData[hour].count);
        });
      } else {
        // 原始数据模式
        const sortedData = [...this.historyData.electricData].sort((a, b) => {
          return new Date(a.time) - new Date(b.time);
        });

        timeData = sortedData.map(item => {
          const time = new Date(item.time);
          const hours = String(time.getHours()).padStart(2, '0');
          const minutes = String(time.getMinutes()).padStart(2, '0');
          return `${hours}:${minutes}`;
        });

        valueData = sortedData.map(item => parseFloat(item.value));
      }

      // 动态更新Y轴配置 - 使用优化配置
      const optimizedConfig = this.getOptimizedYAxisConfig(valueData);

      // 创建新的配置对象以确保响应式更新
      this.currentChartOpts = {
        ...this.currentChartOpts,
        yAxis: {
          ...this.currentChartOpts.yAxis,
          ...optimizedConfig
        }
      };

      // 直接更新图表数据
      this.currentChartData = {
        categories: timeData,
        series: [{
          name: name,
          data: valueData
        }]
      };
    },

    // 销毁图表实例
    disposeCharts() {
      // 不需要销毁图表实例，因为使用的是qiun-data-charts组件
    },

    // 修改更新图表方法
    updateCharts(data) {
      const categories = data.map(item => item.time);
      
      // 更新使用量图表
      this.powerChartData = {
        categories: categories,
        series: [{
          name: '使用量',
          data: data.map(item => item.value)
        }]
      };

      // 更新压力图表
      this.voltageChartData = {
        categories: categories,
        series: [{
          name: '压力',
          data: data.map(item => item.voltage)
        }]
      };

      // 更新流量图表
      this.currentChartData = {
        categories: categories,
        series: [{
          name: '流量',
          data: data.map(item => item.current)
        }]
      };
    },

    // 添加图表点击事件处理方法
    getPowerChartIndex(e) {
      console.log('使用量图表点击事件:', e);
    },

    getVoltageChartIndex(e) {
      console.log('压力图表点击事件:', e);
    },

    getCurrentChartIndex(e) {
      console.log('流量图表点击事件:', e);
    }
  }
};
</script>

<style lang="scss">
.monitoringContainer {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 20rpx;
  box-sizing: border-box;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dept-section {
  position: relative;
  width: 100%;
  z-index: 999;
  margin-bottom: 20rpx;

  .tree-select {
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    background: #fff;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
  }

  /* 下拉树状选择框 */
  .tree-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #fff;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;

    .tree-scroll {
      max-height: 400rpx;
      padding: 20rpx;
    }
  }
}

/* 添加树节点的样式 */
::v-deep .tree-item {
  .tree-node {
    padding: 10rpx 0;
    display: flex;
    align-items: center;
  }

  .tree-children {
    margin-left: 20rpx;
  }
}

.data-section {
  flex: 1;
  
  .filter-section {
    background-color: #f5f7fa;
    padding: 20rpx;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
    
    .filter-item {
      margin-bottom: 20rpx;
      
      .filter-label {
        font-size: 28rpx;
        font-weight: bold;
        margin-right: 20rpx;
        color: #333;
      }
      
      .picker {
        height: 80rpx;
        line-height: 80rpx;
        border: 1px solid #dcdfe6;
        border-radius: 8rpx;
        padding: 0 20rpx;
        background-color: #ffffff;
        font-size: 28rpx;
      }

      // uni-data-select样式调整
      ::v-deep .uni-data-select {
        .uni-stat__select {
          .uni-stat-box {
            .uni-select {
              .uni-select__input-box {
                height: 80rpx;
                line-height: 80rpx;
                border: 1px solid #dcdfe6;
                border-radius: 8rpx;
                padding: 0 20rpx;
                background-color: #ffffff;
                font-size: 28rpx;
              }
            }
          }
        }
      }
    }
    
    .query-btn {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      background-color: #409eff;
      color: #ffffff;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin-top: 20rpx;
      
      &[disabled] {
        background-color: #c0c4cc;
      }
    }
  }
  
  .data-display {
    flex: 1;
    
    .empty-content {
      height: 200rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #909399;
      font-size: 28rpx;
    }
    
    .charts-container {
      .chart-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0;
        background-color: #fff;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
        
        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20rpx 20rpx 10rpx 20rpx;
          width: 100%;
          box-sizing: border-box;
        }

        .chart-title {
          font-size: 28rpx;
          font-weight: bold;
          color: #303133;
          flex: 1;
        }

        .chart-switch {
          display: flex;
          border-radius: 6rpx;
          overflow: hidden;
          border: 1rpx solid #dcdfe6;
        }

        .switch-btn {
          padding: 8rpx 16rpx;
          font-size: 24rpx;
          background-color: #ffffff;
          color: #606266;
          border: none;
          border-right: 1rpx solid #dcdfe6;
          transition: all 0.3s;

          &:last-child {
            border-right: none;
          }

          &.active {
            background-color: #409eff;
            color: #ffffff;
          }

          &:not(.active):hover {
            background-color: #f5f7fa;
          }
        }
        
        .chart {
          width: 100%;
          height: 500rpx;
          display: block;
          margin: 0 auto;
        }
      }
    }
  }
}

/* 适配不同屏幕尺寸 */
@media screen and (max-height: 700px) {
  .data-section .data-display .charts-container .chart-card .chart {
    height: 400rpx;
  }
}

@media screen and (max-height: 600px) {
  .data-section .data-display .charts-container .chart-card .chart {
    height: 350rpx;
  }
}
</style>
