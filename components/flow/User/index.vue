<template>
  <view class="container">
    <view class="flex-row">
      <!-- 部门数据 -->
      <view class="left-column">
        <view class="head-container">
          <uni-easyinput
              v-model="deptName"
              placeholder="请输入部门名称"
              clearable
              @confirm="handleQuery"
          />
        </view>
        <view class="tree-container">
          <tree-node
              v-for="node in deptOptions"
              :key="node.id"
              :node="node"
              :selectedId="selectedDept"
              @node-click="handleNodeClick"/>
        </view>
      </view>

      <!-- 用户数据 -->
      <view class="right-column">
        <!-- 搜索表单 -->
        <uni-forms :model="queryParams" ref="queryForm" v-if="showSearch">
          <view class="search-form">
            <uni-forms-item label="用户名称">
              <uni-easyinput
                  v-model="queryParams.userName"
                  placeholder="请输入用户名称"
                  clearable
                  @confirm="handleQuery"
              />
            </uni-forms-item>
            <uni-forms-item label="手机号码">
              <uni-easyinput
                  v-model="queryParams.phonenumber"
                  placeholder="请输入手机号码"
                  clearable
                  @confirm="handleQuery"
              />
            </uni-forms-item>
            <view class="button-group">
              <button type="primary" size="mini" @click="handleQuery">搜索</button>
              <button size="mini" @click="resetQuery">重置</button>
            </view>
          </view>
        </uni-forms>

        <!-- 多选表格 -->
        <uni-table v-if="checkType === 'multiple'"
                   ref="multiTable"
                   :loading="loading"
                   :data="userList"
                   border
                   stripe
                   emptyText="暂无数据"
        >
          <uni-tr>
            <uni-th width="80" align="center">
              <checkbox-group @change="handleSelectAll">
                <checkbox :checked="isAllSelected" />
              </checkbox-group>
            </uni-th>
            <uni-th v-if="columns[0].visible" align="center">用户编号</uni-th>
            <uni-th v-if="columns[1].visible" align="center">登录账号</uni-th>
            <uni-th v-if="columns[2].visible" align="center">用户姓名</uni-th>
            <uni-th v-if="columns[3].visible" align="center">部门</uni-th>
            <uni-th v-if="columns[4].visible" align="center">手机号码</uni-th>
          </uni-tr>
          <uni-tr v-for="row in userList" :key="row.userId">
            <uni-td width="80" align="center">
              <checkbox-group @change="(e) => handleCheckboxChange(e, row)">
                <checkbox :value="row.userId" :checked="selectedIds.includes(row.userId)" />
              </checkbox-group>
            </uni-td>
            <uni-td v-if="columns[0].visible" align="center">{{ row.userId }}</uni-td>
            <uni-td v-if="columns[1].visible" align="center">{{ row.userName }}</uni-td>
            <uni-td v-if="columns[2].visible" align="center">{{ row.nickName }}</uni-td>
            <uni-td v-if="columns[3].visible" align="center">{{ row.dept && row.dept.deptName }}</uni-td>
            <uni-td v-if="columns[4].visible" align="center">{{ row.phonenumber }}</uni-td>
          </uni-tr>
        </uni-table>

        <!-- 单选表格 -->
        <uni-table v-if="checkType === 'single'"
                   ref="singleTable"
                   :loading="loading"
                   :data="userList"
                   border
                   stripe
                   emptyText="暂无数据"
        >
          <uni-tr>
            <uni-th width="80" align="center">选择</uni-th>
            <uni-th v-if="columns[0].visible" align="center">用户编号</uni-th>
            <uni-th v-if="columns[1].visible" align="center">登录账号</uni-th>
            <uni-th v-if="columns[2].visible" align="center">用户姓名</uni-th>
            <uni-th v-if="columns[3].visible" align="center">部门</uni-th>
            <uni-th v-if="columns[4].visible" align="center">手机号码</uni-th>
          </uni-tr>
          <uni-tr v-for="row in userList" :key="row.userId">
            <uni-td width="80" align="center">
              <radio-group @change="handleSingleUserSelect">
                <radio
                    :value="row.userId"
                    :checked="radioSelected === row.userId"
                />
              </radio-group>
            </uni-td>
            <uni-td v-if="columns[0].visible" align="center">{{ row.userId }}</uni-td>
            <uni-td v-if="columns[1].visible" align="center">{{ row.userName }}</uni-td>
            <uni-td v-if="columns[2].visible" align="center">{{ row.nickName }}</uni-td>
            <uni-td v-if="columns[3].visible" align="center">{{ row.dept && row.dept.deptName }}</uni-td>
            <uni-td v-if="columns[4].visible" align="center">{{ row.phonenumber }}</uni-td>
          </uni-tr>
        </uni-table>

        <!-- 分页器 -->
        <uni-pagination
            v-if="total > 0"
            :total="total"
            :pageSize="queryParams.pageSize"
            :current="queryParams.pageNum"
            :pageSizeRange="[5, 10]"
            @change="handlePaginationChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
import TreeNode from '@/components/flow/TreeNode.vue';
import { getDeptTree } from "@/api/commservice/comSysDept";
import { getListUser } from "@/api/commservice/comUser";
import { StrUtil } from "@/utils/StrUtil";

export default {
  name: "FlowUser",
  components: {
    TreeNode
  },
  props: {
    selectValues: {
      type: [Number, String, Array],
      default: null,
      required: false
    },
    checkType: {
      type: String,
      default: 'multiple',
      required: true
    },
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      userList: [],
      deptOptions: [],
      deptName: undefined,
      selectedDept: null,
      selectedIds: [], // 多选模式选中的id数组
      isAllSelected: false, // 是否全选
      radioSelected: null, // 单选模式选中的id
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      },
      columns: [
        { key: 'userId', label: '用户编号', visible: true },
        { key: 'userName', label: '登录账号', visible: true },
        { key: 'nickName', label: '用户姓名', visible: true },
        { key: 'dept', label: '部门', visible: true },
        { key: 'phonenumber', label: '手机号码', visible: true }
      ]
    };
  },
  watch: {
    selectValues: {
      immediate: true,
      handler(val) {
        if (val) {
          if (this.checkType === 'multiple') {
            this.selectedIds = Array.isArray(val) ? val : [val];
          } else {
            this.radioSelected = val;
          }
        }
      }
    }
  },
  created() {
    this.getList();
    this.getDeptTree();
  },
  methods: {
    getList() {
      this.loading = true;
      getListUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getDeptTree() {
      getDeptTree().then(response => {
        const addExpandedProp = (nodes) => {
          nodes.forEach(node => {
            this.$set(node, 'expanded', false);
            if (node.children && node.children.length) {
              addExpandedProp(node.children);
            }
          });
        };

        this.deptOptions = response.data;
        addExpandedProp(this.deptOptions);
      });
    },
    handleNodeClick(node) {
      this.selectedDept = node.id;
      this.queryParams.deptId = node.id;
      this.handleQuery();
    },
    handleCheckboxChange(e, row) {
      const checked = e.detail.value.length > 0;
      if (checked) {
        if (!this.selectedIds.includes(row.userId)) {
          this.selectedIds.push(row.userId);
        }
      } else {
        const index = this.selectedIds.indexOf(row.userId);
        if (index > -1) {
          this.selectedIds.splice(index, 1);
        }
      }
      this.emitMultipleSelection();
    },
    handleSelectAll(e) {
      const checked = e.detail.value.length > 0;
      this.isAllSelected = checked;
      if (checked) {
        this.selectedIds = this.userList.map(user => user.userId);
      } else {
        this.selectedIds = [];
      }
      this.emitMultipleSelection();
    },
    emitMultipleSelection() {
      const selection = this.userList.filter(user =>
          this.selectedIds.includes(user.userId)
      );
      this.$emit('handleUserSelect', selection);
    },
    handleSingleUserSelect(e) {
      const userId = e.detail.value;
      this.radioSelected = userId;
      const selectedUser = this.userList.find(user => user.userId == userId);
      if (selectedUser) {
        this.$emit('handleUserSelect', selectedUser);
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 5,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      };
      this.selectedDept = null;
      this.handleQuery();
    },
    handlePaginationChange(e) {
      this.queryParams.pageNum = e.current;
      this.queryParams.pageSize = e.pageSize;
      this.getList();
    }
  }
};
</script>

<style>
.container {
  padding: 20rpx;
}

.flex-row {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.left-column {
  width: 25%;
  padding-right: 20rpx;
}

.right-column {
  width: 75%;
  padding-left: 20rpx;
}

.head-container {
  margin-bottom: 20rpx;
}

.tree-container {
  max-height: 600rpx;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4rpx;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.button-group {
  display: flex;
  gap: 20rpx;
}

button {
  margin: 0;
}

/deep/ .uni-table {
  margin-top: 20rpx;
}

/deep/ .uni-table-tr {
  height: 80rpx;
}

/deep/ .uni-table-th {
  font-weight: bold;
}

/deep/ .uni-table--border {
  border: 1px solid #ebeef5;
}

/deep/ .uni-table-td checkbox,
/deep/ .uni-table-td radio {
  transform: scale(0.8);
}

.tree-container::-webkit-scrollbar {
  width: 6rpx;
}

.tree-container::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3rpx;
}

.tree-container::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

@media screen and (max-width: 768px) {
  .flex-row {
    flex-direction: column;
  }

  .left-column,
  .right-column {
    width: 100%;
    padding: 0;
  }

  .left-column {
    margin-bottom: 20rpx;
  }

  .search-form {
    flex-direction: column;
  }

  .button-group {
    justify-content: space-between;
    width: 100%;
  }

  /deep/ .uni-table-tr {
    height: 60rpx;
  }

  /deep/ .uni-table {
    font-size: 24rpx;
  }
}
</style>