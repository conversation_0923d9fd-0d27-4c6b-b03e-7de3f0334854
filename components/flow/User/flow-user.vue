<template>
  <view class="container">
      <view class="flex-row">
        <!-- 部门数据改为下拉框 -->
            <uni-data-picker
              v-model="selectedDeptId"
              :localdata="deptTreeData"
              :clear="true"
              placeholder="请选择部门"
              popup-title="选择部门"
              style="width: 95%;"
              @change="handleDeptChange"
              @popupclosed="handleDeptPopupClosed"
              @nodeclick="handleDeptNodeClick"/>
      </view>
      <view class="flex-row" style="margin-top: 5px;">
        <!-- 用户数据 -->
        <!--<view class="right-column">-->
        <!-- 搜索表单 -->
        <uni-forms  :model="queryParams" ref="queryForm" v-if="showSearch">
            <view class="search-form">
              <uni-forms-item label="用户名" style="display: flex; align-items: center; width: 100%;">
                <view style="flex: 1;">
                  <uni-easyinput
                      v-model="queryParams.userName"
                      placeholder="请输入用户名称"
                      clearable
                      @confirm="handleQuery"/>
                </view>
              </uni-forms-item>
              <uni-forms-item label="手机号" style="display: flex; align-items: flex-start; width: 100%;">
                <view style="flex: 1;">
                  <uni-easyinput
                      v-model="queryParams.phonenumber"
                      placeholder="请输入手机号码"
                      clearable
                      @confirm="handleQuery"/>
                </view>
              </uni-forms-item>
              <view class="button-group">
                <button type="primary" size="mini" @click="handleQuery">搜索</button>
                <button size="mini" @click="resetQuery">重置</button>
              </view>
            </view>
        </uni-forms>
      </view>
      <view class="flex-row">
<!--        <view class="table-container">-->
            <!-- 多选表格 -->
            <uni-table v-if="checkType === 'multiple'"
                       ref="multiTable"
                       :loading="loading"
                       :data="userList"
                       border
                       stripe
                       emptyText="暂无数据">
              <uni-tr>
                <uni-th width="80" align="center">
                  <checkbox-group @change="handleCheckboxChange4SelectedAllUsers4Multi">
                    <checkbox :checked="isSelectedAllUsers4Multi" />
                  </checkbox-group>
                </uni-th>
                <uni-th v-if="columns[0].visible" align="center">用户编号</uni-th>
                <uni-th v-if="columns[1].visible" align="center">登录账号</uni-th>
                <uni-th v-if="columns[2].visible" align="center">用户姓名</uni-th>
                <uni-th v-if="columns[3].visible" align="center">部门</uni-th>
                 <uni-th v-if="columns[4].visible" align="center">手机号码</uni-th>
              </uni-tr>
              <uni-tr v-for="row in userList" :key="row.userId">
                <uni-td width="80" align="center">
                  <checkbox-group @change="(e) => handleCheckboxChange4SelectedOneUser4Multi(e, row)">
                    <checkbox :value="row.userId" :checked="selectedUserIds4Multi.includes(row.userId)" />
                  </checkbox-group>
                </uni-td>
                <uni-td v-if="columns[0].visible" align="center">{{ row.userId }}</uni-td>
                <uni-td v-if="columns[1].visible" align="center">{{ row.userName }}</uni-td>
                <uni-td v-if="columns[2].visible" align="center">{{ row.nickName }}</uni-td>
                <uni-td v-if="columns[3].visible" align="center">{{ row.dept && row.dept.deptName }}</uni-td>
                 <uni-td v-if="columns[4].visible" align="center">{{ row.phonenumber }}</uni-td>
              </uni-tr>
            </uni-table>
            <!-- 单选表格 -->
            <uni-table v-if="checkType === 'single'"
                       ref="singleTable"
                       :loading="loading"
                       :data="userList"
                       border
                       stripe
                       emptyText="暂无数据">
              <uni-tr>
                <uni-th width="80" align="center">选择</uni-th>
                <uni-th v-if="columns[0].visible" align="center">用户编号</uni-th>
                <uni-th v-if="columns[1].visible" align="center">登录账号</uni-th>
                <uni-th v-if="columns[2].visible" align="center">用户姓名</uni-th>
                <uni-th v-if="columns[3].visible" align="center">部门</uni-th>
                 <uni-th v-if="columns[4].visible" align="center">手机号码</uni-th>
              </uni-tr>
              <uni-tr v-for="row in userList" :key="row.userId">
                <uni-td width="80" align="center">
                  <radio-group @change="handleRadioGroupChange4SelectedOneUser4Single">
                    <radio
                        :value="row.userId"
                        :checked="selectedUserId4Single === row.userId"
                    />
                  </radio-group>
                </uni-td>
                <uni-td v-if="columns[0].visible" align="center">{{ row.userId }}</uni-td>
                <uni-td v-if="columns[1].visible" align="center">{{ row.userName }}</uni-td>
                <uni-td v-if="columns[2].visible" align="center">{{ row.nickName }}</uni-td>
                <uni-td v-if="columns[3].visible" align="center">{{ row.dept && row.dept.deptName }}</uni-td>
                 <uni-td v-if="columns[4].visible" align="center">{{ row.phonenumber }}</uni-td>
              </uni-tr>
            </uni-table>
<!--        </view>-->
      </view>
      <view class="flex-row">
          <!-- 分页器 -->
          <uni-pagination
              v-if="total > 0"
              :total="total"
              :pageSize="queryParams.pageSize"
              :current="queryParams.pageNum"
              :pageSizeRange="[5, 10]"
              @change="handlePaginationChange"/>
      </view>
  </view>
</template>

<script>
import { getDeptTree } from "@/api/commservice/comSysDept";
import { getListUser } from "@/api/commservice/comUser";


export default {
  name: "FlowUser",
  components: {
  },
  props: {
    selectValues: {
      type: [Number, String, Array],
      default: null,
      required: false
    },
    checkType: {
      type: String,
      default: 'multiple',
      required: true
    },
  },
  data() {
    return {
      lastSelectedDeptTreeItem:null,
      deptTreeData: [],
      deptName: undefined,
      selectedDeptId: null,
      loading: true,
      showSearch: true,
      total: 0,
      userList: [],

      selectedUserIds4Multi: [], // 多选模式选中的id数组
      isSelectedAllUsers4Multi: false, // 是否全选
      selectedUserId4Single: null, // 单选模式选中的id
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      },
      columns: [
        { key: 'userId', label: '用户编号', visible: false },
        { key: 'userName', label: '登录账号', visible: false },
        { key: 'nickName', label: '用户姓名', visible: true },
        { key: 'dept', label: '部门', visible: true },
        { key: 'phonenumber', label: '手机号码', visible: false }
      ]
    };
  },
  watch: {
    selectValues: {
      immediate: true,
      handler(val) {
        if (val) {
          if (this.checkType === 'multiple') {
            this.selectedUserIds4Multi = Array.isArray(val) ? val : [val];
          } else {
            this.selectedUserId4Single = val;
          }
        }
      }
    },
    // userList 的监听，并在 userList 变化时执行特定逻辑。当 userList 发生变化时，如果 selectUserList 不为空且包含用户 ID，则选中对应的行
    userList: {
      handler(newVal) {

          this.$nextTick(() => {
            if (this.checkType === 'multiple') {
              this.$refs.multiTable.clearSelection();
              if (this.selectedUserIds4Multi && this.selectedUserIds4Multi.length > 0) {
                this.selectedUserIds4Multi?.forEach(userId4MultiItem => {
                  const user = newVal.find(item => userId4MultiItem == item.userId);
                  console.log("user",user)
                  if (user) {
                    this.$refs.multiTable.toggleRowSelection(user, true);
                  }
                });
              }
            } else if (this.checkType === 'single') {
              this.$refs.singleTable.clearSelection();
              const user = newVal.find(item => this.selectedUserId4Single == item.userId);
              console.log("single-user",user)
              if (user) {
                this.$refs.singleTable.toggleRowSelection(user, true);
              }
            }
          });

      },
      immediate: true, // 立即生效
      deep: true  // 监听对象或数组的时候，要用到深度监听
    }
  },
  created() {
    this.getList();
    this.getDeptTreeData();
  },
  methods: {
    getList() {
      this.loading = true;
      getListUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 获取部门下拉选项
    getDeptTreeData() {
         getDeptTree().then(response => {
           this.deptTreeData = this.transformDeptTree(response.data);
         });
    },
       
           // 转换部门树为级联选择数据格式
     transformDeptTree(tree) {
       const transform = (nodes) => {
         return nodes.map(node => ({
           text: node.label,
           value: node.id,
           children: node.children && node.children.length > 0
             ? transform(node.children)
             : undefined
         }));
       };
       return transform(tree);
     },
    initLastSelDeptItem() {
        this.lastSelectedDeptTreeItem = null
    },
    handleDeptNodeClick(e) {
      //把当前选中的值作为上一次选中的节点值
      this.lastSelectedDeptTreeItem = e
    },
    handleDeptPopupClosed() {
      //当关闭弹窗的时候，把上一次选中的节点值赋给selectedDeptId
      this.$nextTick(() => {
          if (!this.lastSelectedDeptTreeItem)
            return
          this.selectedDeptId = this.lastSelectedDeptTreeItem.value
          this.queryParams.deptId = this.lastSelectedDeptTreeItem.value
          this.handleQuery();
      });
    },
    // 处理部门选择变化, 如果当前没有选中部门则把最后一个选中的节点置空
    handleDeptChange(e) {
      if(e.detail.value.length <= 0)
        this.initLastSelDeptItem()
    },
    handleCheckboxChange4SelectedAllUsers4Multi(e) {
      // 全选,当全选勾上，e.detail.value大小为1的数组，e.detail.value.length值为1;当全选取消勾选，e.detail.value大小为空数组,e.detail.value.length值为0
      const checked = e.detail.value.length > 0;
      this.isSelectedAllUsers4Multi = checked;
      if (checked) {
        this.selectedUserIds4Multi = this.userList.map(user => user.userId);
      } else {
        this.selectedUserIds4Multi = [];
      }
      this.emitMultipleSelection();
    },
    handleCheckboxChange4SelectedOneUser4Multi(e, row) {
      // checkbox勾上，e.detail.value大小为1的数组，e.detail.value.length值为1;当取消勾选，e.detail.value大小为空数组,e.detail.value.length值为0
      const checked = e.detail.value.length > 0;
      if (checked) {
        if (!this.selectedUserIds4Multi.includes(row.userId)) {
          this.selectedUserIds4Multi.push(row.userId);
        }
      } else {
        const index = this.selectedUserIds4Multi.indexOf(row.userId);
        if (index > -1) {
          this.selectedUserIds4Multi.splice(index, 1);
        }
      }
      this.emitMultipleSelection();
    },

    emitMultipleSelection() {
      //这段代码的作用是从 this.roleList 中筛选出那些 roleId 存在于 this.selectedRoleIds4Multi 数组中的角色对象，并将结果赋值给 selection 变量
      const selection = this.userList.filter(user =>
          this.selectedUserIds4Multi.includes(user.userId)
      );
      this.$emit('handleUserSelect', selection);
    },
    handleRadioGroupChange4SelectedOneUser4Single(e) {
      const userId = e.detail.value;
      this.selectedUserId4Single = userId;
      const selectedUser = this.userList.find(user => user.userId == userId);
      if (selectedUser) {
        this.$emit('handleUserSelect', selectedUser);
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 5,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      };
      this.selectedDeptId = -1;
      this.handleQuery();
    },
    handlePaginationChange(e) {
      this.queryParams.pageNum = e.current;
      this.queryParams.pageSize = 4;
      this.getList();
    }
  }
};
</script>

<style>
.container {
  padding: 20rpx;
  width: 95%;
}

.flex-row {
  display: flex;
  flex-direction: row;
  width: 95%;
}

.left-column {
  width: 25%;
  padding-right: 20rpx;
}

.right-column {
  width: 100%;
  padding-left: 20rpx;
}

.head-container {
  margin-bottom: 20rpx;
}

.search-form {
  display: flex;
  flex-wrap: wrap;

}


.button-group {
  display: flex;
  gap: 20rpx;
  padding-bottom: 25rpx;
}

button {
  margin: 0;
}
/* 新增或修改以下样式 */
.table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  width: 100%;
}
.uni-table {
	overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* 移动端弹性滚动 */
    width: 100%;
    position: relative;
}




/deep/ .uni-table {
  display: inline-block !important;
  min-width: 150% !important; /* 强制超出容器 */
}
/deep/ .uni-table-tr {
  display: inline-flex !important;
  white-space: nowrap !important;
  width: max-content !important;
}
/deep/ .uni-table-th,
/deep/ .uni-table-td {
  display: inline-block !important;
  min-width: 200rpx !important; /* 按实际列宽调整 */
  vertical-align: top; /* 对齐优化 */
}
/* 表头固定 */
/* .uni-table-th {
  position: sticky !important;
  top: 0;
  z-index: 3;
  background: #f8f8f8 !important;
} */
/* 固定第一列 */
/deep/ .uni-table-th:first-child,
/deep/ .uni-table-td:first-child {
  position: sticky !important;
  left: 0;
  z-index: 30;
  background: #fff;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}
/deep/ .uni-table-th:not(:first-child),
/deep/ .uni-table-td:not(:first-child) {
  display: inline-block;
  min-width: 120rpx; /* 根据实际内容调整 */
}

/deep/ .uni-table-td checkbox,
/deep/ .uni-table-td radio {
  transform: scale(0.8);
}

@media screen and (max-width: 768px) {
  .flex-row {
    flex-direction: column;
  }

  .left-column,
  .right-column {
    width: 100%;
    padding: 0;
  }

  .left-column {
    margin-bottom: 20rpx;
  }

  .search-form {
    flex-direction: column;
  }

  .button-group {
    justify-content: space-between;
    width: 100%;
  }
	.table-container {
    max-height: 60vh; /* 限制最大高度 */
  }
  
  .uni-table {
    font-size: 24rpx; /* 缩小字体 */
  }
  
  .uni-table-td, .uni-table-th {
    padding: 8rpx 12rpx !important; /* 缩小单元格间距 */
  }
  /deep/ .uni-table-tr {
    height: 60rpx;
  }

  /deep/ .uni-table {
    font-size: 24rpx;
  }
}
</style>