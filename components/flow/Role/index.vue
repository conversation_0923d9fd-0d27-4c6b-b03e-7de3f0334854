<template>
  <view class="app-container">
    <!-- 搜索区域 -->
    <view class="search-form" v-show="showSearch">
      <uni-forms :model="queryParams" ref="queryForm">
        <uni-forms-item label="角色名称" name="roleName">
          <uni-easyinput
              v-model="queryParams.roleName"
              placeholder="请输入角色名称"
              clearable
              @confirm="handleQuery"
          />
        </uni-forms-item>
        <view class="button-group">
          <button type="primary" size="mini" @click="handleQuery">搜索</button>
          <button size="mini" @click="resetQuery">重置</button>
        </view>
      </uni-forms>
    </view>

    <!-- 多选表格 -->
    <uni-table v-show="checkType === 'multiple'"
               ref="dataTable"
               :loading="loading"
               :data="roleList"
               @selection-change="handleMultipleRoleSelect"
    >
      <uni-tr>
        <uni-th width="50" align="center">
          <uni-checkbox />
        </uni-th>
        <uni-th width="120">角色编号</uni-th>
        <uni-th width="150">角色名称</uni-th>
        <uni-th width="150">权限字符</uni-th>
        <uni-th width="100">显示顺序</uni-th>
        <uni-th width="180" align="center">创建时间</uni-th>
      </uni-tr>
      <uni-tr v-for="row in roleList" :key="row.roleId">
        <uni-td width="50" align="center">
          <uni-checkbox :value="row.roleId" />
        </uni-td>
        <uni-td>{{ row.roleId }}</uni-td>
        <uni-td>{{ row.roleName }}</uni-td>
        <uni-td>{{ row.roleKey }}</uni-td>
        <uni-td>{{ row.roleSort }}</uni-td>
        <uni-td align="center">{{ parseTime(row.createTime) }}</uni-td>
      </uni-tr>
    </uni-table>

    <!-- 单选表格 -->
    <uni-table v-show="checkType === 'single'"
               :loading="loading"
               :data="roleList"
               @selection-change="handleSingleRoleSelect"
    >
      <uni-tr>
        <uni-th width="55" align="center">选择</uni-th>
        <uni-th width="120">角色编号</uni-th>
        <uni-th width="150">角色名称</uni-th>
        <uni-th width="150">权限字符</uni-th>
        <uni-th width="100">显示顺序</uni-th>
        <uni-th width="180" align="center">创建时间</uni-th>
      </uni-tr>
      <uni-tr v-for="row in roleList" :key="row.roleId">
        <uni-td width="55" align="center">
          <uni-radio-group v-model="radioSelected">
            <uni-radio :value="row.roleId"></uni-radio>
          </uni-radio-group>
        </uni-td>
        <uni-td>{{ row.roleId }}</uni-td>
        <uni-td>{{ row.roleName }}</uni-td>
        <uni-td>{{ row.roleKey }}</uni-td>
        <uni-td>{{ row.roleSort }}</uni-td>
        <uni-td align="center">{{ parseTime(row.createTime) }}</uni-td>
      </uni-tr>
    </uni-table>

    <!-- 分页器 -->
    <uni-pagination
        v-show="total > 0"
        :total="total"
        :pageSize="queryParams.pageSize"
        :current="queryParams.pageNum"
        :pageSizeRange="[5,10]"
        @change="handlePaginationChange"
    />
  </view>
</template>

<script>
import { listRole } from "@/api/system/role";
import { StrUtil } from "@/utils/StrUtil";

export default {
  name: "FlowRole",
  props: {
    selectValues: {
      type: [Number, String, Array],
      default: null,
      required: false
    },
    checkType: {
      type: String,
      default: 'multiple',
      required: false
    },
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      roleList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        roleName: undefined,
        roleKey: undefined,
        status: undefined
      },
      radioSelected: null,
      selectRoleList: []
    };
  },
  watch: {
    selectValues: {
      handler(newVal) {
        if (StrUtil.isNotBlank(newVal)) {
          if (typeof newVal === 'number' || typeof newVal === 'string') {
            this.radioSelected = newVal;
          } else {
            this.selectRoleList = newVal;
          }
        }
      },
      immediate: true
    },
    roleList: {
      handler(newVal) {
        if (StrUtil.isNotBlank(newVal) && this.selectRoleList.length > 0) {
          this.$nextTick(() => {
            // 更新选中状态的逻辑需要根据uni-table的API进行调整
            this.selectRoleList?.split(',').forEach(key => {
              const selectedRow = newVal.find(item => key == item.roleId);
              if (selectedRow) {
                // 这里需要调用uni-table的选中方法
                this.$refs.dataTable?.toggleRowSelection(selectedRow, true);
              }
            });
          });
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listRole(this.queryParams).then(response => {
        this.roleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleMultipleRoleSelect(selection) {
      const idList = selection.map(item => item.roleId);
      const nameList = selection.map(item => item.roleName);
      this.$emit('handleRoleSelect', idList.join(','), nameList.join(','));
    },
    handleSingleRoleSelect(selection) {
      if (selection && selection.length > 0) {
        const selectedRole = selection[0];
        this.radioSelected = selectedRole.roleId;
        this.$emit('handleRoleSelect', this.radioSelected.toString(), selectedRole.roleName);
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams.roleName = undefined;
      this.handleQuery();
    },
    handlePaginationChange(e) {
      this.queryParams.pageNum = e.current;
      this.queryParams.pageSize = e.pageSize;
      this.getList();
    }
  }
};
</script>

<style>
.app-container {
  padding: 20rpx;
}

.search-form {
  margin-bottom: 20rpx;
}

.button-group {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

uni-button {
  margin: 0;
}

.uni-table {
  margin-top: 20rpx;
}
</style>