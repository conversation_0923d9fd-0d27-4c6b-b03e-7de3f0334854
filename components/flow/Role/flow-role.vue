<template>
  <view class="container">
    <view class="flex-row" style="margin-top: 5px;">
      <!-- 用户数据 -->
      <!--<view class="right-column">-->
      <!-- 搜索表单 -->
      <uni-forms  :model="queryParams" ref="queryForm" v-if="showSearch">
        <view class="search-form">
          <uni-forms-item label="角色名称" name="roleName" style="display: flex; align-items: center; width: 100%;">
            <view style="flex: 1;">
              <uni-easyinput
                  v-model="queryParams.roleName"
                  placeholder="请输入角色名称"
                  clearable
                  @confirm="handleQuery"/>
            </view>
          </uni-forms-item>
          <view class="button-group">
            <button type="primary" size="mini" @click="handleQuery">搜索</button>
            <button size="mini" @click="resetQuery">重置</button>
          </view>
        </view>
      </uni-forms>
    </view>
    <view class="flex-row">
      <!-- 多选表格 -->
      <uni-table v-if="checkType === 'multiple'"
                 ref="multiTable"
                 :loading="loading"
                 :data="roleList"
                 border
                 stripe
                 emptyText="暂无数据">
        <uni-tr>
          <uni-th width="80" align="center">
            <checkbox-group @change="handleCheckboxChange4SelectedAllRoles4Multi">
              <checkbox :checked="isSelectedAllRoles4Multi" />
            </checkbox-group>
          </uni-th>
          <uni-th align="center">角色编号</uni-th>
          <uni-th align="center">角色名称</uni-th>
        </uni-tr>
        <uni-tr v-for="row in roleList" :key="row.roleId">
          <uni-td width="80" align="center">
            <checkbox-group @change="(e) => handleCheckboxChange4SelectedOneRole4Multi(e, row)">
              <checkbox :value="row.roleId.toString()" :checked="selectedRoleIds4Multi.includes(row.roleId)" />
            </checkbox-group>
          </uni-td>
          <uni-td align="center">{{ row.roleId }}</uni-td>
          <uni-td align="center">{{ row.roleName }}</uni-td>
        </uni-tr>
      </uni-table>
      <!-- 单选表格 -->
      <uni-table v-if="checkType === 'single'"
                 ref="singleTable"
                 :loading="loading"
                 :data="roleList"
                 border
                 stripe
                 emptyText="暂无数据">
        <uni-tr>
          <uni-th  align="center">选择</uni-th>
          <uni-th align="center">角色编号</uni-th>
          <uni-th align="center">角色名称</uni-th>
        </uni-tr>
        <uni-tr v-for="row in roleList" :key="row.roleId">
          <uni-td width="80" align="center">
            <radio-group @change="handleRadioGroupChange4SelectedOneRole4Single">
              <radio
                  :value="row.roleId"
                  :checked="selectedRoleId4Single === row.roleId"
              />
            </radio-group>
          </uni-td>
          <uni-td align="center">{{ row.roleId }}</uni-td>
          <uni-td align="center">{{ row.roleName }}</uni-td>>
        </uni-tr>
      </uni-table>
      <!--        </view>-->
    </view>
    <view class="flex-row">
      <!-- 分页器 -->
      <uni-pagination
          v-if="total > 0"
          :total="total"
          :pageSize="queryParams.pageSize"
          :current="queryParams.pageNum"
          :pageSizeRange="[5, 10]"
          @change="handlePaginationChange"/>
    </view>
  </view>
</template>

<script>
import { listRole } from "@/api/system/role";

export default {
  name: "FlowRole",
  props: {
    selectValues: {
      type: [Number, String, Array],
      default: null,
      required: false
    },
    checkType: {
      type: String,
      default: 'multiple',
      required: false
    },
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      roleList: [],

      selectedRoleIds4Multi: [], // 多选模式选中的id数组
      isSelectedAllRoles4Multi: false, // 是否全选
      selectedRoleId4Single: null, // 单选模式选中的id

      queryParams: {
        pageNum: 1,
        pageSize: 4,
        roleName: undefined,
        roleKey: undefined,
        status: undefined
      },
      radioSelected: null,
      selectRoleList: []
    };
  },
  watch: {
    selectValues: {
      handler(val) {
        if (val) {
          if (this.checkType === 'multiple') {
            this.selectedRoleIds4Multi = Array.isArray(val) ? val : [val];
          } else {
            this.selectedRoleId4Single = val;
          }
        }
      },
      immediate: true
    },
    roleList: {
      handler(newVal) {
        this.$nextTick(() => {
          if (this.checkType === 'multiple') {
            this.$refs.multiTable.clearSelection();
            if (this.selectedRoleIds4Multi && this.selectedRoleIds4Multi.length > 0) {
              this.selectedRoleIds4Multi?.forEach(roleId4MultiItem => {
                const role = newVal.find(item => roleId4MultiItem == item.roleId);
                console.log("role",role)
                if (role) {
                  this.$refs.multiTable.toggleRowSelection(role, true);
                }
              });
            }
          } else if (this.checkType === 'single') {
            this.$refs.singleTable.clearSelection();
            const role = newVal.find(item => this.selectedRoleId4Single == item.roleId);
            console.log("single-role",role)
            if (role) {
              this.$refs.singleTable.toggleRowSelection(role, true);
            }
          }
        });
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listRole(this.queryParams).then(response => {
        this.roleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleCheckboxChange4SelectedAllRoles4Multi(e) {
      // 全选,当全选勾上，e.detail.value大小为1的数组，e.detail.value.length值为1;当全选取消勾选，e.detail.value大小为空数组,e.detail.value.length值为0
      const checked = e.detail.value.length > 0;
      this.isSelectedAllRoles4Multi = checked;
      if (checked) {
         this.selectedRoleIds4Multi = this.roleList.map(role => role.roleId);
      } else {
         this.selectedRoleIds4Multi = [];
      }
      this.emitMultipleSelection();
    },
    handleCheckboxChange4SelectedOneRole4Multi(e, row) {
      // checkbox勾上，e.detail.value大小为1的数组，e.detail.value.length值为1;当取消勾选，e.detail.value大小为空数组,e.detail.value.length值为0
      const checked = e.detail.value.length > 0;
      if (checked) {
        if (!this.selectedRoleIds4Multi.includes(row.roleId)) {
          this.selectedRoleIds4Multi.push(row.roleId);
        }
      } else {
        const index = this.selectedRoleIds4Multi.indexOf(row.roleId);
        if (index > -1) {
          this.selectedRoleIds4Multi.splice(index, 1);
        }
      }
      this.emitMultipleSelection();
    },
    emitMultipleSelection() {
      //这段代码的作用是从 this.roleList 中筛选出那些 roleId 存在于 this.selectedRoleIds4Multi 数组中的角色对象，并将结果赋值给 selection 变量
      const selection = this.roleList.filter(role =>
          this.selectedRoleIds4Multi.includes(role.roleId)
      );
      this.$emit('handleRoleSelect', selection);
    },

    handleRadioGroupChange4SelectedOneRole4Single(e) {
      if (e && e.detail) {
         const roleId = e.detail.value;
         this.selectedRoleId4Single = roleId;
         const selectedRole = this.roleList.find(role => role.roleId == roleId);
         if (selectedRole) {
            this.$emit('handleRoleSelect', this.selectedRoleId4Single.toString());
         }
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams.roleName = undefined;
      this.handleQuery();
    },
    handlePaginationChange(e) {
      this.queryParams.pageNum = e.current;
      this.queryParams.pageSize = 4;
      this.getList();
    }
  }
};
</script>

<style>
.container {
  padding: 20rpx;
  width: 95%;
}

.flex-row {
  display: flex;
  flex-direction: row;
  width: 95%;
}

.left-column {
  width: 25%;
  padding-right: 20rpx;
}

.right-column {
  width: 100%;
  padding-left: 20rpx;
}

.head-container {
  margin-bottom: 20rpx;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  //gap: 1rpx;
  //margin-bottom: 1rpx;
}


.button-group {
  display: flex;
  gap: 20rpx;
}

button {
  margin: 0;
}
/* 新增或修改以下样式 */
.table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  width: 100%;
}
.uni-table {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* 移动端弹性滚动 */
  width: 100%;
  position: relative;
}




/deep/ .uni-table {
  display: inline-block !important;
  min-width: 150% !important; /* 强制超出容器 */
}
/deep/ .uni-table-tr {
  display: inline-flex !important;
  white-space: nowrap !important;
  width: max-content !important;
}
/deep/ .uni-table-th,
/deep/ .uni-table-td {
  display: inline-block !important;
  min-width: 200rpx !important; /* 按实际列宽调整 */
  vertical-align: top; /* 对齐优化 */
}
/* 表头固定 */
/* .uni-table-th {
  position: sticky !important;
  top: 0;
  z-index: 3;
  background: #f8f8f8 !important;
} */
/* 固定第一列 */
/deep/ .uni-table-th:first-child,
/deep/ .uni-table-td:first-child {
  position: sticky !important;
  left: 0;
  z-index: 30;
  background: #fff;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}
/deep/ .uni-table-th:not(:first-child),
/deep/ .uni-table-td:not(:first-child) {
  display: inline-block;
  min-width: 120rpx; /* 根据实际内容调整 */
}

/deep/ .uni-table-td checkbox,
/deep/ .uni-table-td radio {
  transform: scale(0.8);
}

@media screen and (max-width: 768px) {
  .flex-row {
    flex-direction: column;
  }

  .left-column,
  .right-column {
    width: 100%;
    padding: 0;
  }

  .left-column {
    margin-bottom: 20rpx;
  }

  .search-form {
    flex-direction: column;
  }

  .button-group {
    justify-content: space-between;
    width: 100%;
  }
  .table-container {
    max-height: 60vh; /* 限制最大高度 */
  }

  .uni-table {
    font-size: 24rpx; /* 缩小字体 */
  }

  .uni-table-td, .uni-table-th {
    padding: 8rpx 12rpx !important; /* 缩小单元格间距 */
  }
  /deep/ .uni-table-tr {
    height: 60rpx;
  }

  /deep/ .uni-table {
    font-size: 24rpx;
  }
}
</style>