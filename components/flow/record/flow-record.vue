<!-- components/parser-nuoyi-form/child-components/Timeline.vue -->
<template>
  <scroll-view scroll-y class="timeline-container">
    <view class="timeline">
      <view
          class="timeline-item"
          v-for="(item, index) in flowRecordList"
          :key="index"
      >
        <view>
          <text
              class="timeline-icon"
              :class="{
              'icon-check': !!item.finishTime,
              'success': !!item.finishTime,
              'icon-time': !item.finishTime,
              'waiting': !item.finishTime
            }"
          ></text>
        </view>
        <view class="timeline-content">
          <text class="task-name">{{ item.taskName }}</text>
          <view class="record-card">
            <view v-if="item.assigneeName" class="record-item">
              <text class="label">
                <text class="iconfont icon-user"></text>
                办理人
              </text>
              <view class="content">
                {{ item.assigneeName }}
                <text class="dept-tag">{{ item.deptName }}</text>
              </view>
            </view>

            <view v-if="item.candidate" class="record-item">
              <text class="label">
                <text class="iconfont icon-user"></text>
                候选办理
              </text>
              <text class="content">{{ item.candidate }}</text>
            </view>

            <view class="record-item">
              <text class="label">
                <text class="iconfont icon-time"></text>
                接收时间
              </text>
              <text class="content">{{ item.createTime }}</text>
            </view>

            <view v-if="item.finishTime" class="record-item">
              <text class="label">
                <text class="iconfont icon-time"></text>
                处理时间
              </text>
              <text class="content">{{ item.finishTime }}</text>
            </view>

            <view v-if="item.duration" class="record-item">
              <text class="label">
                <text class="iconfont icon-time"></text>
                耗时
              </text>
              <text class="content">{{ item.duration }}</text>
            </view>

            <view v-if="item.comment" class="record-item">
              <text class="label">
                <text class="iconfont icon-comment"></text>
                处理意见
              </text>
              <text class="content">{{ item.comment.comment }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'FlowRecord',

  props: {
    flowRecordList: {
      type: Array,
      required: true
    }
  }
}
</script>

<style scoped>
.timeline-container {
  /* 设置时间线容器的样式 */
  height: 100%;
}

.timeline {
  padding: 30rpx;
}

.timeline-item {
  display: flex;
  margin-bottom: 40rpx;
}

.timeline-icon {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  margin-top: 10rpx;
}

.icon-check {
  background-color: #67c23a;
}

.icon-time {
  background-color: #909399;
}

.timeline-content {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.record-card {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.record-item {
  display: flex;
  margin-bottom: 16rpx;
}

.label {
  width: 160rpx;
  color: #909399;
  font-size: 26rpx;
}

.content {
  flex: 1;
  font-size: 26rpx;
}

.dept-tag {
  display: inline-block;
  background-color: #f4f4f5;
  color: #909399;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-left: 12rpx;
}
</style>