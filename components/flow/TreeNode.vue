<template>
  <view class="tree-item">
    <view
        class="tree-node"
        :class="{
        'active': selectedId === node.id,
        'child-node': level > 0
      }"
        :style="{ paddingLeft: level * 20 + 'rpx' }"
        @click="handleNodeClick"
    >
      <view class="tree-expand" @click.stop="toggleExpand">
        <text v-if="hasChildren" class="expand-icon">
          {{ node.expanded ? '▼' : '▶' }}
        </text>
        <text v-else class="placeholder-icon">●</text>
      </view>
      <text class="tree-label">{{ node.label }}</text>
    </view>

    <view v-if="node.expanded && hasChildren" class="tree-children">
      <tree-node
          v-for="child in node.children"
          :key="child.id"
          :node="child"
          :level="level + 1"
          :selectedId="selectedId"
          @node-click="$emit('node-click', $event)"
      />
    </view>
  </view>
</template>

<script>
export default {
  name: 'TreeNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    },
    selectedId: {
      type: [String, Number],
      default: null
    }
  },
  computed: {
    hasChildren() {
      return this.node.children && this.node.children.length > 0;
    }
  },
  methods: {
    toggleExpand() {
      this.$set(this.node, 'expanded', !this.node.expanded);
    },
    handleNodeClick() {
      this.$emit('node-click', this.node);
    }
  }
};
</script>

<style scoped>
.tree-item {
  width: 100%;
}

.tree-node {
  display: flex;
  align-items: center;
  padding: 16rpx;
  cursor: pointer;
  transition: background-color 0.3s;
}

.tree-node:hover {
  background-color: #f5f7fa;
}

.tree-node.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.tree-expand {
  width: 40rpx;
  text-align: center;
}

.expand-icon {
  font-size: 24rpx;
  color: #666;
}

.placeholder-icon {
  font-size: 20rpx;
  color: #999;
}

.tree-label {
  flex: 1;
  padding-left: 10rpx;
}

.child-node {
  font-size: 28rpx;
}
</style>