<template>
  <view>
    <view class="py-flow-root">
      <view
          :class="bortherNum == 1 && treeData.completed && hasStart ? 'py-list-content py-flow-node py-flow-node-one py-flow-completed' : ((bortherNum != 1 && treeData.completed && hasStart) ? 'py-list-content py-flow-node py-flow-completed' : ((bortherNum == 1 && !treeData.completed && hasStart) ? 'py-list-content py-flow-node py-flow-node-one' : 'py-list-content py-flow-node'))">

        <uni-icons custom-prefix="iconfont" type="py-icon-user"
                   :color="treeData.completed && hasStart ? '#56bb56' : ''"
                   v-if="treeData.type == 'user' && treeData._id && !treeData._id.includes('_remove_')"></uni-icons>
        <uni-icons custom-prefix="iconfont" type="py-icon-circle"
                   :color="treeData.completed && hasStart ? '#56bb56' : ''"
                   v-else-if="treeData.type == 'start' && treeData._id && !treeData._id.includes('_remove_')"></uni-icons>
        <uni-icons custom-prefix="iconfont" type="py-icon-exclusive-gateway"
                   :color="treeData.completed && hasStart ? '#56bb56' : ''"
                   v-else-if="treeData.type == 'gateway' && treeData._id && !treeData._id.includes('_remove_')"></uni-icons>
        <uni-icons custom-prefix="iconfont" type="py-icon-circleo"
                   :color="treeData.completed && hasStart ? '#56bb56' : ''"
                   v-else-if="treeData.type == 'end' && treeData._id && !treeData._id.includes('_remove_')"></uni-icons>
        <uni-icons custom-prefix="iconfont" type="py-icon-transfer"
                   :color="treeData.completed && hasStart ? '#56bb56' : ''"
                   v-else-if="treeData._id && treeData._id.includes('_remove_')"></uni-icons>
        <u--text :text="treeData._name" align="center"
                 :color="treeData.completed && hasStart ? '#56bb56' : '#303133'"></u--text>
      </view>

      <view v-if="treeData.children">
        <view class="py-flow-arrow">
          <u-icon name="arrow-downward"></u-icon>
        </view>

        <u-row justify="center" align="top">
          <u-col v-for="nodeChild in treeData.children" :span="12 / treeData.children.length"
                 :key="nodeChild._id">
            <PyBpmnTreeNode :treeData="nodeChild" :bortherNum="treeData.children.length"
                            :hasStart="hasStart"></PyBpmnTreeNode>
          </u-col>
        </u-row>

      </view>

    </view>


  </view>
</template>
<script>
import PyBpmnTreeNode from "@/components/py/py-bpmn-tree-node/py-bpmn-tree-node.vue";

export default {
  name: "PyBpmnTreeNode",
  props: {
    // 树形数据
    treeData: {
      type: Object
    },

    // 兄弟数
    bortherNum: {
      type: Number,
      default: 1
    },

    // 是否启动
    hasStart: {
      type: Boolean,
      default: false
    }
  },
  components: {
    PyBpmnTreeNode
  },
  data() {
    return {
      // 没有删除节点数量
      notRemoveCount: 1
    }
  }
}
</script>
<style lang="css" scoped>
.py-flow-node {
  position: relative;
  display: flex;
  height: 10vh;
  width: 80%;
  margin-left: calc(10% - 10rpx);
}

.py-flow-node-one {
  width: 60%;
  margin-left: calc(20% - 10rpx);
}

.py-flow-arrow {
  display: flex;
  justify-content: center;
}

.py-flow-completed {
  /* background-color: #56bb56; */
  background-color: rgba(86, 187, 86, 0.2);
}
</style>