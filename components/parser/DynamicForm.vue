<template>
	<view >
		<uni-forms ref="dynamicFormRef" :model="formModel"   :label-width="formModel.labelWidth">
			<view :span="12" v-for="item in formModel.fields" :key="item.__vModel__">
				<template>
					<uni-forms-item class="list-item-class"
						:label="item.__config__.label ? item.__config__.label + '：' : ''" :prop="item.__vModel__">
						<!-- 输入框 -->
						<!-- @input="item.inputEvent && item.inputEvent($event, item.key)" 事件回调，可处理数据变化-->
						<uni-easyinput v-if="item.__config__.tag == 'el-input'"
                          :placeholder="item.placeholder || '请输入' + item.__config__.label"
                          v-model="localFormContent[item.__vModel__]"
                          :type='text'
                          :maxlength="item.maxlength || 100000"
                          @input="input_event($event, item.__vModel__)"
                         :disabled="formModel.disabled"
                          clearable>
            </uni-easyinput>

						<!-- 下拉框 -->
						<view v-else-if="item.__config__.tag == 'el-select'">
							<uni-data-select :disabled="formModel.disabled"
                               v-model="localFormContent[item.__vModel__]"
								               :localdata="item.__slot__.options"
                               @change="onSelect($event, item.__vModel__)">
							</uni-data-select>
						</view>

						<!-- 单选框 -->
						<view v-else-if="item.__config__.tag == 'el-radio-group'" 
						    :disabled="formModel.disabled">
							<!-- #ifdef APP-PLUS -->
							<view class="radio-list">
								<view
									v-for="(optItem, index) in item.__slot__.options"
									:key="optItem.value"
									class="radio-item"
									:class="{ 'disabled': formModel.disabled }"
									@tap="handleRadioTap(optItem, item.__vModel__)"
								>
									<view class="radio-wrapper">
										<view class="radio" :class="{ 'checked': String(localFormContent[item.__vModel__]) === String(optItem.value) }"></view>
									</view>
									<text class="radio-label">{{ optItem.label }}</text>
								</view>
							</view>
							<!-- #endif -->

							<!-- #ifndef APP-PLUS -->
							<radio-group v-model="localFormContent[item.__vModel__]"
							             @change="rg_changeEvent($event, item.__vModel__)">
								<label class="uni-list-cell uni-list-cell-pd radio-group-item-vertical-direction"
                       v-for="(optItem, index) in item.__slot__.options" :key="String(optItem.value)">
									<view>
										<radio :value="String(optItem.value)"
                           :checked="String(localFormContent[item.__vModel__]) == String(optItem.value) "
                           :disabled="formModel.disabled"/>
									</view>
									<view>{{optItem.label}}</view>
								</label>
							</radio-group>
							<!-- #endif -->
						</view>
					</uni-forms-item>
				</template>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	export default {
		name: 'DynamicForm',
		props: {
			// form表单JSON结构对象，包含表单字段、表单配置等
			formModel: {
				type: Object,
				required: true,
			},
      // form表单的数据，即表单中各个字段的值
			formContent: {
				type: Object,
				required: true,
			},
		},
		computed: {},
		watch: {
      formContent: function (newValue, oldValue) {
       // console.log("newValue", newValue)
       // console.log("oldValue", oldValue)
        this.localFormContent = Object.assign({},newValue)
       // console.log("this.localFormContent", this.localFormContent)
      },
      formModel: function (newValue, oldValue) {
       // console.log("formModel--newValue", newValue)
      //  console.log("formModel---oldValue", oldValue)
       // console.log("this.formModel.fields", this.formModel.fields)
        // 将el-select中text属性label映射为text
        for (let i = 0; i < newValue.fields.length; i++){
          const item = newValue.fields[i]
          if (item.__config__.tag == 'el-select'){
            item.__slot__.options = item.__slot__.options.map(optItem => ({
              value: optItem.value,
              text: optItem.label
            }))
            //console.log("item.__slot__.options", item.__slot__.options)
          }
        }
      }
		},
		data() {
			return {
				localFormContent: {},
        text: 'Hello, Vue!'
			};
		},
    created() {

    },
    // 必须要在mounted生命周期，因为created生命周期组件可能尚未创建完毕
    mounted() {

    },
		methods: {
			input_event(e, key) {
				this.$set(this.localFormContent, key, e)
        //console.log("this.localFormContent", this.localFormContent)
        //console.log("this.$refs['dynamicFormRef']", this.$refs['dynamicFormRef'])

			},
			// 下拉框
			onSelect(e, key) {
        this.$set(this.localFormContent, key, e)
				console.log("this.localFormContent", this.localFormContent)
        console.log("this.$refs['dynamicFormRef']", this.$refs['dynamicFormRef'])
			},
			rg_changeEvent(e, key) {
        this.$set(this.localFormContent, key, e.detail.value)
				//console.log("this.localFormContent", this.localFormContent)
        //console.log("this.$refs['dynamicFormRef']", this.$refs['dynamicFormRef'])
			},
			// APP平台单选框点击事件
			handleRadioTap(optItem, key) {
				if (this.formModel.disabled) return;
				const value = String(optItem.value);
				this.$set(this.localFormContent, key, value);
				this.$emit('change', { key, value });
			},
			// 需要提交和重置功能可以通过ref调用如下方法，或自行修改
			submitForm() {
        this.$emit('submit', this.localFormContent);
			},
			getData(){
				//console.log("父组件点击：getData", this.localFormContent)
        this.$emit('getData',this.localFormContent);
			}
		},

	};
</script>

<style lang="scss">
	.uni-forms {
		background-color: #fff;
		padding: 0 30rpx;
		display: flex;
		// margin: 10rpx 0;
	}

	.uni-forms-item {
		// display: flex;
		// flex-direction: column;
		// padding: 0 0 10rpx 0;
		margin: 0 0 30rpx 0;
	}
  .radio-group-item-vertical-direction{
    display: flex;
    justify-content: left;
    align-items: center;
  }
	.mPlaceholder {
		color: rgb(192, 196, 204);
	}

	.buttons {
		// display: flex;
		// padding: 20rpx 0 0 20rpx;

		.button {
			background-color: #027eff;
			color: #fff;
			// margin-right: 20rpx;
		}
	}
	
	.radio-list {
		width: 100%;
	}

	.radio-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		background-color: #fff;
		
		&:active {
			background-color: #f5f7fa;
		}

		&.disabled {
			opacity: 0.6;
			pointer-events: none;
		}

		.radio-wrapper {
			width: 40rpx;
			height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.radio {
				width: 36rpx;
				height: 36rpx;
				border: 2rpx solid #dcdfe6;
				border-radius: 50%;
				background-color: #fff;
				
				&.checked {
					background-color: #409EFF;
					border-color: #409EFF;
					position: relative;
					
					&::after {
						content: '';
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						width: 16rpx;
						height: 16rpx;
						background-color: #fff;
						border-radius: 50%;
					}
				}
			}
		}

		.radio-label {
			margin-left: 20rpx;
			font-size: 28rpx;
			color: #333;
		}
	}
</style>