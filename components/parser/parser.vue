<template>
	<div>
		<uni-forms ref="DynamicParserFormRef" :label-width="formConfig.labelWidth">
			<view :span="12" v-for="item in formModel.fields" :key="item.__vModel__">
				<template>
					<uni-forms-item class="list-item-class"
						:label="item.__config__.label ? item.__config__.label + '：' : ''" :prop="item.__vModel__">
						<!-- 输入框 -->
						<!-- @input="item.inputEvent && item.inputEvent($event, item.key)" 事件回调，可处理数据变化-->
						<uni-easyinput v-if="item.__config__.tag == 'el-input'"
							:placeholder="item.placeholder || '请输入' + item.__config__.label"
							v-model="formContent[item.__vModel__]" :type='text' :maxlength="item.maxlength || 100000"
							@input="input_event($event, item.__vModel__)" :disabled="formModel.disabled"
							clearable></uni-easyinput>

						<!-- 下拉框 -->
						<view v-else-if="item.__config__.tag == 'el-select'">
							<!-- 数据有显示，没有显示提示信息 -->
							<!-- 							<view @click="true">
								<text
									v-if="formModel[item.__vModel__]">{{ item.__slot__.options.find((v) => v.value == formContent[item.__vModel__]).label }}</text>
								<text v-else
									class="mPlaceholder">{{ item.placeholder || '请选择' + item.__config__.label }} </text>
							</view> -->
							<uni-data-select :disabled="formModel.disabled" v-model="formContent[item.__vModel__]"
								:localdata="item.__slot__.options.map(option => ({
								value: option.value,
								text: option.label  // 将label映射为text
							}))" @change="onSelect($event, item.__vModel__)">
							</uni-data-select>
						</view>

						<!-- 单选框 -->
						<view class="uni-list" v-else-if="item.__config__.tag == 'el-radio-group'" 
						    :disabled="formModel.disabled">
							<radio-group v-model="formContent[item.__vModel__]" 
							             @change="rg_changeEvent($event, item.__vModel__)">
								<label class="uni-list-cell uni-list-cell-pd"  v-for="(optItem, index) in item.__slot__.options"
									:key="String(optItem.value)">
									<view>
										<radio :value="String(optItem.value)" :checked="String(formContent[item.__vModel__]) == String(optItem.value) "/>
									</view>
									<view>{{optItem.label}}</view>
								</label>
							</radio-group>
						</view>
						
						<!-- <radio-group v-else-if="item.__config__.tag == 'el-radio-group'" :disabled="formModel.disabled"
							v-model="formContent[item.__vModel__]" @change="rg_changeEvent($event, item.__vModel__)">
							<label class="uni-list-cell uni-list-cell-pd"
								style="display: flex;justify-content: center;align-items: center;">
								<view v-for="opt in item.__slot__.options" :key="opt.value">
									<radio :value="String(opt.value)" name="radio">{{opt.label}}</radio>
								</view>
							</label>
						</radio-group> -->
						
						
						<!-- 
						<uni-group v-else-if="item.__config__.tag == 'el-radio-group'" :disabled="formModel.disabled"
							v-model="formContent[item.__vModel__]"
							@change="rg_changeEvent($event, item.__vModel__)"
							>
							<label class="radio" v-for="opt in item.__slot__.options" :key="opt.value">
							    <radio :value="String(opt.value)"  name="radio">{{opt.label}}</radio>
							</label>
						</uni-group> -->


					</uni-forms-item>
				</template>
			</view>
		</uni-forms>
	</div>
</template>

<script>
	export default {
		name: 'DynamicParserForm',
		props: {
			// 表单的额外配置
			// form表单的所有字段
			formModel: {
				type: Object,
				required: true,
			},
			formContent: {
				type: Object,
				required: true,
			},
			formConfig: {
				type: Object,
				default: () => {
					return {
						labelWidth: '150px',
					};
				},
			},
		},
		computed: {},
		watch: {

		},
		data() {
			return {
				text: 'Hello, Vue!'
			};
		},
		methods: {
			input_event(e, key) {
				this.formContent[key] = e
				// this.$set(, key, e)
			},
			// 下拉框
			onSelect(e, key) {
				//console.log("下拉框e", e)
				this.formContent[key] = (e);
				//console.log("this.formContent", this.formContent)
			},
			rg_changeEvent(e, key) {
				//console.log("单选框e", e)
				//console.log(key, 'key');
			 //	console.log("单选框e.detail", e.detail)
				this.formContent[key] = e.detail.value
				// this.$set(this.formContent, key, Number(e.detail.value))
				//console.log("this.formContent", this.formContent)
			},
			// 需要提交和重置功能可以通过ref调用如下方法，或自行修改
			submitForm() {
				this.$refs['DynamicParserFormRef'].validate((valid) => {
					if (!valid) return;
					this.$emit('onSubmit', {
						formContent: this.formContent
					}); // 发送 formContent
				});
			},
			getData(){
				console.log("父组件点击：getData", this.formContent)
				this.$emit('getData',this.formContent)
			}
		},
		// 必须要在mounted生命周期，因为created生命周期组件可能尚未创建完毕
		mounted() {},
	};
</script>

<style lang="scss">
	// .radio-group{
	// 	z-index: 1000;
	// }
	// .uni-data-select{
	// 	z-index: 990;
	// }
	.uni-forms {
		background-color: #fff;
		padding: 0 30rpx;
		display: flex;
		// margin: 10rpx 0;
	}

	.uni-forms-item {
		// display: flex;
		// flex-direction: column;
		// padding: 0 0 10rpx 0;
		margin: 0 0 30rpx 0;
	}

	.mPlaceholder {
		color: rgb(192, 196, 204);
	}

	.buttons {
		// display: flex;
		// padding: 20rpx 0 0 20rpx;

		.button {
			background-color: #027eff;
			color: #fff;
			// margin-right: 20rpx;
		}
	}
</style>