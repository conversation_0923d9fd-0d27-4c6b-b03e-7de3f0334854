<template>
	<view >
    <uni-forms ref="dynamicFormParser" :model="formModel"   :label-width="formModel.labelWidth">
         <DynamicFormItemParser @setData="setData" :listFields="formModel.fields" :formContent="formContent">
		     </DynamicFormItemParser>
		</uni-forms>
	</view>
</template>

<script>
   import DynamicFormItemParser from '@/components/parser/DynamicFormItemParser'
	export default {
		name: 'DynamicFormParser',
    // 注册的组件
    components: {
      DynamicFormItemParser
    },
		props: {
			// form表单JSON结构对象，包含表单字段、表单配置等
			formModel: {
				type: Object,
				required: true,
			},
      // form表单的数据，即表单中各个字段的值
			formContent: {
				type: Object,
				required: true,
			},
		},
		computed: {},
		watch: {
      formContent: function (newValue, oldValue) {
       // console.log("newValue", newValue)
       // console.log("oldValue", oldValue)
        this.localFormContent = Object.assign({},newValue)
       // console.log("this.localFormContent", this.localFormContent)
      },
      formModel: function (newValue, oldValue) {
        // console.log("newValue", newValue)
        // console.log("oldValue", oldValue)
        this.listFields = Array.from(newValue.fields)//Object.assign([],newValue.fields)
		console.log("this.listFields", this.listFields)

      },
		},
		data() {
			return {
				localFormContent: {},
        listFields: [],
        text: 'Hello, Vue!'
			};
		},
    created() {
      this.listFields = Array.from(this.formModel.fields)//Object.assign([],this.formModel.fields)
      console.log("created",this.listFields)
    },
    // 必须要在mounted生命周期，因为created生命周期组件可能尚未创建完毕
    mounted() {
 console.log("mounted",this.formModel.fields)
    },
		methods: {
			// 需要提交和重置功能可以通过ref调用如下方法，或自行修改
			submitForm() {
        this.$emit('submit', this.localFormContent);
			},
			getData(){
				//console.log("父组件点击：getData", this.localFormContent)
        this.$emit('getData',this.localFormContent);
			},
      setData(key,value){
        console.log("setData", key)
        console.log("setData", value)
        console.log("this.localFormContent", this.localFormContent)
        this.$set(this.localFormContent, key,value)
        console.log("this.localFormContent", this.localFormContent)
      }
		},

	};
</script>

<style lang="scss">
	.uni-forms {
		background-color: #fff;
		padding: 0 20rpx;
		display: flex;
		 //margin: 10rpx 0;
	}

	.uni-forms-item {
		// display: flex;
		// flex-direction: column;
		// padding: 0 0 10rpx 0;
		margin: 0 0 30rpx 0;
	}
  .radio-group-item-vertical-direction{
    display: flex;
    justify-content: left;
    align-items: center;
  }
	.mPlaceholder {
		color: rgb(192, 196, 204);
	}

	.buttons {
		// display: flex;
		// padding: 20rpx 0 0 20rpx;

		.button {
			background-color: #027eff;
			color: #fff;
			// margin-right: 20rpx;
		}
	}
</style>