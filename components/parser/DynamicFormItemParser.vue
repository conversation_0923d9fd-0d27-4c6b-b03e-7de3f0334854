<template>
	<view class="flex flex-wrap">
	<!--  -->
		<!-- :style="{ flex: item.__config__.span / 24 }" -->
		<view
		 v-for="item in listFields" :key="item.__vModel__" 	:style="item.type === 'flex' ? { width: Number(100 / (childrenLength > 1 ? 1 : childrenLength)) + '%' } : { width: Number(100 / (childrenLength > 4 ? 5 : childrenLength)) + '%' }" >
			<!-- 水平行 -->
			<view v-if="item.__vModel__ == undefined &&
                    (item.type == 'flex' || item.type == 'default' )&&
                    item.__config__.children &&
                    item.__config__.layout &&
                    item.__config__.layout == 'rowFormItem'">
				<DynamicFormItemParser @setData="setData" :listFields="item.__config__.children" 
				:childrenLength="item.__config__.children.length"
				:layout="item.__config__.layout" :formContent="formContent">
				</DynamicFormItemParser>
			</view>
			<view v-else >
		
				<uni-forms-item
					:label="item.__config__.label"
					label-width="auto"
					 :prop="item.__vModel__">
					<!-- 输入框 -->
			<!-- {{layout}}{{childrenLength}} -->
					<!-- @input="item.inputEvent && item.inputEvent($event, item.key)" 事件回调，可处理数据变化-->
					<!-- <view class="" v-if="item.__config__.tayg == 'el-input'">
					</view> -->
			<uni-easyinput
			v-if="item.__config__.tag == 'el-input'" 
				:placeholder="item.placeholder || '请输入' + item.__config__.label"
				v-model="localFormContent[item.__vModel__]" :type="'text'" :maxlength="item.maxlength || 100000"
				@input="input_event($event, item.__vModel__)" :disabled="listFields.disabled" clearable>
				<template #right v-if="item.__slot__ && item.__slot__.append">
					<text style="margin-right: 24rpx;">{{ item.__slot__.append }}</text>
				</template>
			</uni-easyinput>


					<!-- 下拉框 -->
					<view v-else-if="item.__config__.tag == 'el-select'">
						<uni-data-select :disabled="listFields.disabled" v-model="localFormContent[item.__vModel__]"
							:localdata="item.__slot__.options" @change="onSelect($event, item.__vModel__)">
						</uni-data-select>
					</view>

					<!-- 单选框 -->
					<view class="uni-list" v-else-if="item.__config__.tag == 'el-radio-group'"
						:disabled="listFields.disabled">
						<radio-group v-model="localFormContent[item.__vModel__]"
							@change="rg_changeEvent($event, item.__vModel__)">
							<label class="uni-list-cell uni-list-cell-pd radio-group-item-vertical-direction"
								v-for="(optItem, index) in item.__slot__.options" :key="String(optItem.value)">
								<view>
									<radio :value="String(optItem.value)"
										:checked="String(localFormContent[item.__vModel__]) == String(optItem.value) "
										:disabled="listFields.disabled" />
								</view>
								<view>{{optItem.label}}</view>
							</label>
						</radio-group>
					</view>

					<!-- 评分框 -->
					<view v-else-if="item.__config__.tag == 'el-rate'">
						<uni-rate v-model="localFormContent[item.__vModel__]" :max="5" :size="24" touchable
							:is-fill="false" @change="rateChange($event, item.__vModel__)"
							:class="{ 'small-star': isSmallStar }">
						</uni-rate>
					</view>

					<!-- 开关按钮 -->
					<view v-if="item.__config__.tag == 'el-switch'">
						<switch v-model="localFormContent[item.__vModel__]" color="#11a983" style="transform:scale(1)"
							@change="handleSwitchChange($event, item.__vModel__)" />
					</view>

					<!-- 上传图片 -->
					<view v-if="item.__config__.tag == 'el-upload'" class="upload-container">
						<view class="upload-box" @click="chooseImage(item.__vModel__)">
							<text style="font-size: 45px">+</text>
						</view>
						<image v-if="localFormContent[item.__vModel__]" :src="localFormContent[item.__vModel__]"
							class="uploaded-image" />
					</view>

          <!-- 手写签名 -->
          <view v-if="item.__config__.tag == 'handSign'">
            <!-- #ifdef VUE2 -->
            <jp-signature-popup
                v-model="localFormContent[item.__vModel__]"
                :required="item.__config__.required"
                :buttonText="item.__config__.buttonText"
                :disabled="item.disabled"
                @change="onSignChange"
            />
            <view v-if="localFormContent[item.__vModel__]">
              <button @click="uploadMinio(item.__vModel__)">上传签名</button>
<!--              <image :src="localFormContent[item.__vModel__]" style="width: 200px;" mode="widthFix"></image>-->
            </view>
            <!-- #endif -->
          </view>
				</uni-forms-item>
			</view>
		</view>
	</view>
</template>

<script>
import {uploadMinio} from 'api/system/user'
	export default {
		name: 'DynamicFormItemParser',

		props: {
			// form表单JSON结构对象，包含表单字段、表单配置等
			listFields: {
				type: Array,
				default: () => [],
			},
			// form表单的数据，即表单中各个字段的值
			formContent: {
				type: Object,
				required: true,
			},
			layout:{
				// col  row
				type:String,
				default:'colFormItem'
			},
			childrenLength:{
				// 每行有多少个元素
				type:Number,
				default:1
			}
		},
		computed: {},
		watch: { 
			formContent: function(newValue, oldValue) {
				// console.log("newValue", newValue)
				// console.log("oldValue", oldValue)
				this.localFormContent = Object.assign({}, newValue)
				// console.log("this.localFormContent", this.localFormContent)
			},
			listFields: function(newValue, oldValue) {
				// console.log("listFields--newValue", newValue)
				//  console.log("listFields---oldValue", oldValue)
				// console.log("this.listFields.fields", this.listFields.fields)
				// 将el-select中text属性label映射为text
				for (let i = 0; i < newValue.length; i++) {
					const item = newValue[i]
					if (item.__config__.tag == 'el-select') {
						item.__slot__.options = item.__slot__.options.map(optItem => ({
							value: optItem.value,
							text: optItem.label
						}))
						//console.log("item.__slot__.options", item.__slot__.options)
					}
				}
			}
		},
		data() {
			return {
				localFormContent: {},
				text: 'Hello, Vue!',
				// 评分框选中属性
				isSmallStar: false,
				image: '',
				image2: '',

			};
		},
		created() {
      this.initializeFormContent(); // 初始化表单内容
		},
		// 必须要在mounted生命周期，因为created生命周期组件可能尚未创建完毕
		mounted() {},
		methods: {
			toPop1() {
				this.$refs.signature1.toPop()
			},
      initializeFormContent() {
        this.listFields.forEach(item => {
          // 如果存在默认值，则将其设置到 localFormContent 中
          if (item.__vModel__ && item.__config__.defaultValue !== undefined) {
            this.$set(this.localFormContent, item.__vModel__, item.__config__.defaultValue);
          }
        });
      },
			input_event(e, key) {
				this.$set(this.localFormContent, key, e)
				console.log("input_event--this.localFormContent", this.localFormContent)
				//console.log("this.$refs['dynamicFormRef']", this.$refs['dynamicFormRef'])
				this.setData(key, e);
			},
			// 下拉框
			onSelect(e, key) {
				this.$set(this.localFormContent, key, e)
				console.log("onSelect--this.localFormContent", this.localFormContent)
				//console.log("this.$refs['dynamicFormRef']", this.$refs['dynamicFormRef'])
				this.setData(key, e)
			},
			rg_changeEvent(e, key) {
				this.$set(this.localFormContent, key, e.detail.value)
				console.log("rg_changeEvent-this.localFormContent", this.localFormContent)
				//console.log("this.$refs['dynamicFormRef']", this.$refs['dynamicFormRef'])
				this.setData(key, e.detail.value)
			},
			// 评分框
			rateChange(e, key) {
				this.isSmallStar = true;
				this.$set(this.localFormContent, key, e.value); // 更新表单数据中对应键的值
				console.log("rateChange-this.localFormContent", this.localFormContent)
				// 调用 setData 方法将更新后的数据传递回父组件
				this.setData(key, e.value);
			},
			handleSwitchChange(e, key) {
				this.$set(this.localFormContent, key, e.target.checked);
				console.log("handleSwitchChange-this.localFormContent", this.localFormContent)
				this.$emit('setData', key, e.target.checked);
			},
			// 选择图片的方法
			chooseImage(key) {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						this.$set(this.localFormContent, key, res.tempFilePaths[0]);
						console.log("Uploaded image path:", res.tempFilePaths[0]);
						this.setData(key, res.tempFilePaths[0]);
					}
				});
			},
      onSignChange(e, key) {
        // 使用 e 处理签名变化
        this.$set(this.localFormContent, key, e);
        console.log("handleSignChange - this.localFormContent", this.localFormContent);
        this.$emit('setData', key, e);
      },
      async uploadMinio (){
        console.log("this.localFormContent",this.localFormContent)
        const signatureData = this.localFormContent.priceBuyDeptUserSign;
        console.log("signatureData",signatureData)
        let data = {
          name: 'file',
          filePath: signatureData
        }
        console.log(data)
        try {
          let res = await uploadMinio(data); // 调用上传接口
          if (res.code === 200) {
            // this.formData.inspectAudioUrlList.push(res.url); // 将返回的 URL 添加到列表中
            console.log("签名上传成功", res);
            uni.showToast({
              title: "签名上传成功",
              icon: "success"
            });
          } else {
            uni.showToast({
              title: "上传失败",
              icon: "none"
            });
          }
        } catch (error) {
          console.error("上传签名出错:", error);
          uni.showToast({
            title: "上传失败",
            icon: "none"
          });
        }
      },
			// 需要提交和重置功能可以通过ref调用如下方法，或自行修改
			submitForm() {
				this.$emit('submit', this.localFormContent);
			},
			getData() {
				//console.log("父组件点击：getData", this.localFormContent)
				this.$emit('getData', this.localFormContent);
			},
			setData(key, value) {
				var that = this
				console.log("--setData", key)
				console.log("--setData", value)
				console.log("before --that.localFormContent", that.localFormContent)
				that.$set(that.localFormContent, key, value)
				console.log("after --that.localFormContent", that.localFormContent)
				this.$emit('setData', key, value);

			}
		},

	};
</script>

<style lang="scss">
	.col__item{
		flex: 0 0 100%;
	}
	.row__item{
		flex: 0 1 auto;
	}
	.uni-forms {
		background-color: #fff;
		//padding: 0 30rpx;
		display: flex;
		flex-wrap: wrap;
		// margin: 10rpx 0;
	}

	.uni-forms-item {
		display: flex;
		/* 允许内部内容以 flex 排列 */
		//flex-direction: column; /* 您可能希望内部元素纵向排列 */
		padding: 0 0 10rpx 0;
		margin: 0 0 30rpx 0;
	}

	.radio-group-item-vertical-direction {
		display: flex;
		justify-content: left;
		align-items: center;
	}

	.mPlaceholder {
		color: rgb(192, 196, 204);
	}

	.buttons {
		// display: flex;
		// padding: 20rpx 0 0 20rpx;

		.button {
			background-color: #027eff;
			color: #fff;
			// margin-right: 20rpx;
		}
	}

	.horizontal-space-around {
		//display: flex;
		//justify-content: space-around;
	}

	.horizontal-center {
		display: flex;
		justify-content: center;
	}

	.vertical-top {
		display: flex;
		align-items: flex-start;
	}

	//.small-star .uni-rate__icon {
	//	transform: scale(0.8);
	//	/* 缩小星星大小为原来的80% */
	//}

	.upload-container {
		display: flex;
		align-items: center;
		/* 垂直居中对齐 */
	}

	.upload-box {
		width: 135px;
		/* 根据需要调整宽度 */
		height: 150px;
		/* 根据需要调整高度 */
		border: 1px dashed #ccc;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		color: #999;
		font-size: 24px;
		/* 调整字体大小 */
	}

	.upload-box text {
		font-size: 45px;
		/* 调整“+”符号的大小 */
		line-height: 150px;
		/* 使“+”符号垂直居中 */
		color: #8c939d;
		/* 可选：调整颜色 */
	}

	.uploaded-image {
		width: 135px;
		/* 设置与上传框相同的宽度 */
		height: 150px;
		/* 设置固定高度以便于预览 */
		margin-left: 10px;
		/* 设定图片与上传框之间的间距 */
	}

	.unit {
		display: inline-block;
		margin-left: 10px;
		/* 给单位添加一些间距 */
		color: #999;
		/* 可选：改变单位文字颜色 */
	}

	.signature-canvas {
		width: 50%;
		/* 80% 宽度，适应父容器 */
		height: 50px;
		/* 设置高度 */
		border: 1px solid #ccc;
		background-color: #f5f5f5;
		margin: 10px 0;
	}

	.popup-content {
		padding: 20px;
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
	}

	.popup-buttons {
		display: flex;
		justify-content: space-between;
		margin-top: 20px;
	}
</style>