// 获取当前窗口高度
export function getWindowHeight() {
    return new Promise((resolve, reject) => {
        uni.getSystemInfo({
            success: res => {
                let windowHeight = res && res.windowHeight;
                if (windowHeight) {
                    windowHeight = windowHeight + "px";
                    resolve(windowHeight);
                } else {
                    resolve("");
                }
            }
        });
    })
}

// hex转rgba，本质是将16进制转成10进制
export function convertColorHexToRgba(hexStr) {
    // 去除#号
    let excludePrefix = hexStr.replace("#", "");
    // hex值的长度
    let hexStrLength = excludePrefix.length;
    // hex的格式 #bfa #bbffaa #bbffaaff（最后的ff表示透明度），这里判断是正确的格式
    if (hexStr.startsWith("#") && (hexStrLength == 3 || hexStrLength == 6 || hexStrLength == 8)) {
        let hexArr = [];
        // 最终的rgba字符串
        let rgbaStr = "";
        // 针对不同格式分别处理
        switch (hexStrLength) {
            // 如果是3位
            case 3:
                rgbaStr = `${excludePrefix
                    .split("")
                    .map(hex => parseInt(hex.repeat(2), 16))
                    .join(", ")}, 1`;
                break;
            // 如果是6位
            case 6:
                for (let i = 0; i < 3; i++) {
                    hexArr.push(excludePrefix.slice(i * 2, i * 2 + 2));
                }
                rgbaStr = `${hexArr.map(hex => parseInt(hex, 16)).join(", ")}, 1`;
                break;
            // 如果是8位
            case 8:
                for (let i = 0; i < 4; i++) {
                    hexArr.push(excludePrefix.slice(i * 2, i * 2 + 2));
                }
                rgbaStr = `${hexArr
                    .map((hex, index) => index < 3 && parseInt(hex, 16))
                    .filter(item => item)
                    .join(", ")}, ${(parseInt(hexArr[3], 16) / 255).toFixed(2)}`;
                break;
        }
        let rgbaArray = rgbaStr.split(",");
        let rgbaObj = {
            r: rgbaArray[0],
            g: rgbaArray[1],
            b: rgbaArray[2],
            a: rgbaArray[3],
        };
        return rgbaObj;
    } else {
        return {
            r: 0,
            g: 0,
            b: 0,
            a: 1
        }
    }
}

// 获取当前变量类型
export function getVarType(value) {

    if (value === undefined) {
        return "undefined";
    } else if (Array.isArray(value)) {
        return "array";
    } else if (value.constructor == Object) {
        return "object";
    } else if (value.constructor == String) {
        return "string";
    } else if (value.constructor == Number) {
        return "number";
    } else if (value.constructor == Boolean) {
        return "boolean";
    } else if (value.constructor == Date) {
        return "date";
    }
}

// 将对象转换为对应类型
export function getFormData(formList) {
    let formData = {};
    formList.forEach(form => {
        let varName = form["varName"];
        let varType = form["varType"];
        let varValue = form["varValue"];
        switch (varType) {
            case "undefined":
                form["varValue"] = undefined;
                break;
            case "array":
                form["varValue"] = JSON.parse(varValue); // 字符串转数组
                break;
            case "object":
                form["varValue"] = JSON.parse(varValue); // 字符串转对象
                break;
            case "string":
                form["varValue"] = varValue; // 已经是字符串
                break;
            case "number":
                form["varValue"] = Number(varValue); // 转换为数字
                break;
            case "boolean":
                form["varValue"] = varValue === "true"; // 字符串 "true"/"false" 转布尔值
                break;
            case "date":
                form["varValue"] = new Date(varValue); // 转换为 Date 对象
                break;
            default:
                form["varValue"] = varValue; // 未知类型，直接返回原始值
        }
        formData[varName] = form["varValue"];
    });
    return formData;
}
// 深拷贝对象
export function deepClone(obj) {
    const _toString = Object.prototype.toString

    // null, undefined, non-object, function
    if (!obj || typeof obj !== 'object') {
        return obj
    }

    // DOM Node
    if (obj.nodeType && 'cloneNode' in obj) {
        return obj.cloneNode(true)
    }

    // Date
    if (_toString.call(obj) === '[object Date]') {
        return new Date(obj.getTime())
    }

    // RegExp
    if (_toString.call(obj) === '[object RegExp]') {
        const flags = []
        if (obj.global) { flags.push('g') }
        if (obj.multiline) { flags.push('m') }
        if (obj.ignoreCase) { flags.push('i') }

        return new RegExp(obj.source, flags.join(''))
    }

    const result = Array.isArray(obj) ? [] : obj.constructor ? new obj.constructor() : {}

    for (const key in obj) {
        result[key] = deepClone(obj[key])
    }

    return result
}
