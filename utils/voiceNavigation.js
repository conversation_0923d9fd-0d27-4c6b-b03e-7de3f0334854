/**
 * 语音导航工具类
 * 用于处理语音识别后的文本，提取关键词，并进行页面导航
 */
import { toast } from '@/utils/common.js';

// 定义导航关键词映射表
const NAVIGATION_KEYWORDS = [
  // 资产相关
  {
    keywords: ['资产', '资产列表', '查看资产', '资产查询', '查询资产'],
    route: '/pages/assetsList/assetsList',
    description: '资产列表'
  },
  {
    keywords: ['资产详情', '查看资产详情', '资产信息'],
    action: 'assetDetail',
    description: '资产详情'
  },
  {
    keywords: ['租赁资产', '租赁', '资产租赁'],
    route: '/pages/leaseAsset/leaseAsset',
    description: '租赁资产'
  },
  
  // 维修相关
  {
    keywords: ['报修', '维修', '设备报修', '报修单', '我要报修', '提交报修'],
    route: '/pages/qrRepair/index',
    description: '设备报修'
  },
  {
    keywords: ['我的报修', '我的维修', '查看报修', '报修记录', '维修记录'],
    route: '/pages/repairList/myRepair',
    description: '我的报修'
  },
  {
    keywords: ['部门报修', '部门维修', '部门报修记录'],
    route: '/pages/repairList/deptRepair',
    description: '部门报修'
  },
  {
    keywords: ['医院报修', '医院维修', '全院报修', '医院报修记录'],
    route: '/pages/repairList/hospRepair',
    description: '医院报修'
  },
  {
    keywords: ['扫码报修', '扫描报修', '扫一扫报修'],
    route: '/pages/scanRepair/scanRepair',
    description: '扫码报修'
  },
  {
    keywords: ['水电报修', '水电维修'],
    route: '/pages/HydropowerRepair/HydropowerRepair',
    description: '水电报修'
  },
  
  // 个人中心相关
  {
    keywords: ['我的', '个人中心', '个人信息', '用户中心', '用户信息'],
    route: '/pages/mine/index',
    description: '个人中心'
  },
  {
    keywords: ['修改密码', '更改密码', '密码修改', '密码设置', '设置密码'],
    route: '/pages/mine/pwd/index',
    description: '修改密码'
  },
  {
    keywords: ['设置', '系统设置', '应用设置'],
    route: '/pages/mine/setting/index',
    description: '设置'
  },
  {
    keywords: ['关于', '关于我们', '应用信息', '版本信息'],
    route: '/pages/mine/about/index',
    description: '关于'
  },
  {
    keywords: ['帮助', '使用帮助', '帮助中心'],
    route: '/pages/mine/help/index',
    description: '帮助'
  },
  
  // 任务相关
  {
    keywords: ['工单', '任务', '我的任务', '待办任务', '待办'],
    route: '/pages/myTasks/index',
    description: '我的任务'
  },
  {
    keywords: ['已办任务', '已完成任务', '已办', '已完成'],
    route: '/pages/myDoneTask/myDoneTask',
    description: '已办任务'
  },
  {
    keywords: ['待办任务', '待处理任务', '待办'],
    route: '/pages/todoTasks/todoTasks',
    description: '待办任务'
  },
  {
    keywords: ['快速任务', '快速工单'],
    route: '/pages/taskFast/taskFast',
    description: '快速任务'
  },
  
  // 巡检相关
  {
    keywords: ['巡检', '设备巡检', '巡检任务', '巡检记录'],
    route: '/pages/inspect/index',
    description: '设备巡检'
  },
  {
    keywords: ['巡检计划', '已检计划', '巡检安排'],
    route: '/pages/inspect/inspectedPlan',
    description: '巡检计划'
  },
  
  // 能源相关
  {
    keywords: ['能耗', '能源', '能源管理', '能耗统计', '能源统计'],
    route: '/pages/energyConsumptionStatic/energyConsumptionStatic',
    description: '能耗统计'
  },
  {
    keywords: ['医院能耗', '医院能源', '全院能耗'],
    route: '/pages/energyConsumptionStatic/hospEnergyConsumptionStatic',
    description: '医院能耗'
  },
  {
    keywords: ['能源排名', '能耗排名', '能源排行', '能耗排行'],
    route: '/pages/energyRank/energyRank',
    description: '能源排名'
  },
  {
    keywords: ['能源可视化', '能耗可视化'],
    route: '/pages/VisualizationEnergy/VisualizationEnergy',
    description: '能源可视化'
  },
  {
    keywords: ['电力报警', '电力告警', '电力警报'],
    route: '/pages/EnergyMgt/electricityAlarm',
    description: '电力报警'
  },
  {
    keywords: ['电力历史', '电力历史记录', '电力历史数据'],
    route: '/pages/EnergyMgt/electricityHistory',
    description: '电力历史'
  },
  {
    keywords: ['气体管理', '气体'],
    route: '/pages/Energymanagement/Gasmanagement',
    description: '气体管理'
  },
  
  // 消息相关
  {
    keywords: ['消息', '通知', '消息中心', '通知中心', '我的消息', '我的通知'],
    route: '/pages/msgCenter/msg/viewMsg',
    description: '消息中心'
  },
  {
    keywords: ['报警', '告警', '警报', '报警信息'],
    route: '/pages/msgCenter/alarm/viewAlarm',
    description: '报警信息'
  },
  
  // 工作流相关
  {
    keywords: ['工作流', '流程', '我的流程'],
    route: '/pages/myFlow/index',
    description: '我的流程'
  },
  
  // 合同相关
  {
    keywords: ['合同', '合同管理', '查看合同'],
    route: '/pages/contract/contract',
    description: '合同管理'
  },
  
  // 客户相关
  {
    keywords: ['客户', '客户管理', '查看客户'],
    route: '/pages/customer/customer',
    description: '客户管理'
  },
  
  // 工作台相关
  {
    keywords: ['工作台', '首页', '主页', '工作', '工作页面'],
    route: '/pages/work/index',
    description: '工作台'
  },
  
  // 功能性操作
  {
    keywords: ['扫码', '扫一扫', '扫描', '扫描二维码', '扫描条码'],
    action: 'scan',
    description: '扫码'
  },
  {
    keywords: ['搜索', '查找', '查询', '搜一搜'],
    action: 'search',
    description: '搜索'
  },
  {
    keywords: ['二维码', '生成二维码', '查看二维码'],
    route: '/pages/qrCode/qrCode',
    description: '二维码'
  },
  {
    keywords: ['签名', '电子签名', '手写签名'],
    route: '/pages/signature/signature',
    description: '电子签名'
  }
];

/**
 * 从文本中提取关键词并获取匹配的导航项
 * @param {String} text 识别的文本
 * @returns {Array} 匹配的导航项数组
 */
export const extractKeywordsAndNavigate = (text) => {
  console.log('[语音导航] 开始提取关键词, 识别文本:', text);
  
  if (!text) {
    console.log('[语音导航] 识别文本为空');
    return [];
  }
  
  // 将文本转为小写，便于匹配
  const lowerText = text.toLowerCase();
  
  // 匹配到的导航项
  const matchedItems = [];
  
  // 遍历关键词映射表，查找匹配项
  NAVIGATION_KEYWORDS.forEach(item => {
    // 检查文本中是否包含任一关键词
    const isMatched = item.keywords.some(keyword => lowerText.includes(keyword));
    
    if (isMatched) {
      console.log('[语音导航] 匹配到关键词:', item.description);
      matchedItems.push(item);
    }
  });
  
  console.log('[语音导航] 匹配结果数量:', matchedItems.length);
  return matchedItems;
};

/**
 * 执行导航操作
 * @param {Object} navigationItem 导航项
 */
export const executeNavigation = (navigationItem) => {
  console.log('[语音导航] 执行导航操作:', navigationItem);
  
  if (!navigationItem) {
    console.error('[语音导航] 导航项为空');
    return;
  }
  
  // 如果是路由导航
  if (navigationItem.route) {
    console.log('[语音导航] 导航到路由:', navigationItem.route);
    uni.navigateTo({
      url: navigationItem.route,
      fail: (err) => {
        console.error('[语音导航] 导航失败:', err);
        // 尝试使用switchTab
        uni.switchTab({
          url: navigationItem.route,
          fail: (switchErr) => {
            console.error('[语音导航] switchTab失败:', switchErr);
            toast('无法导航到该页面');
          }
        });
      }
    });
  } 
  // 如果是特定动作
  else if (navigationItem.action) {
    console.log('[语音导航] 执行特定动作:', navigationItem.action);
    
    switch (navigationItem.action) {
      case 'scan':
        console.log('[语音导航] 执行扫码操作');
        uni.scanCode({
          success: (res) => {
            console.log('[语音导航] 扫码成功:', res);
            // 处理扫码结果
          },
          fail: (err) => {
            console.error('[语音导航] 扫码失败:', err);
            toast('扫码失败');
          }
        });
        break;
        
      case 'search':
        console.log('[语音导航] 执行搜索操作');
        // 实现搜索功能
        toast('搜索功能开发中');
        break;
        
      case 'assetDetail':
        console.log('[语音导航] 执行资产详情操作');
        // 实现跳转到资产详情页，可能需要先选择资产
        toast('请先选择要查看的资产');
        break;
        
      default:
        console.warn('[语音导航] 未知的动作类型:', navigationItem.action);
        toast('该功能暂未实现');
    }
  } else {
    console.error('[语音导航] 导航项没有route或action属性');
    toast('无法执行该操作');
  }
};

/**
 * 显示导航选择弹窗
 * @param {Array} navigationItems 导航项数组
 * @returns {Promise} 导航结果Promise
 */
export const showNavigationDialog = (navigationItems) => {
  console.log('[语音导航] 显示导航选择弹窗, 选项数量:', navigationItems.length);
  
  return new Promise((resolve, reject) => {
    // 如果没有匹配项
    if (!navigationItems || navigationItems.length === 0) {
      console.log('[语音导航] 没有匹配的导航项');
      toast('未能识别您的意图');
      reject(new Error('没有匹配的导航项'));
      return;
    }
    
    // 如果只有一个匹配项，直接导航
    if (navigationItems.length === 1) {
      console.log('[语音导航] 只有一个匹配项，直接导航');
      executeNavigation(navigationItems[0]);
      resolve(navigationItems[0]);
      return;
    }
    
    // 如果有多个匹配项，显示选择弹窗
    console.log('[语音导航] 有多个匹配项，显示选择弹窗');
    
    const items = navigationItems.map(item => ({
      text: item.description,
      value: item
    }));
    
    uni.showActionSheet({
      itemList: items.map(item => item.text),
      success: (res) => {
        console.log('[语音导航] 用户选择了:', items[res.tapIndex].text);
        const selectedItem = items[res.tapIndex].value;
        executeNavigation(selectedItem);
        resolve(selectedItem);
      },
      fail: (err) => {
        console.log('[语音导航] 用户取消选择');
        reject(new Error('用户取消选择'));
      }
    });
  });
}; 