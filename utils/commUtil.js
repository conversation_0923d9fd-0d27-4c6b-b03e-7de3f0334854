import config from "../config";

export const CommUtil = {

    /*
     检测是否为外网访问
     */
    isInternalNetwork: function() {
        const baseUrl = new URL(config.baseUrl);
        if (baseUrl.hostname === "**************")
            return 0;
        return 1;
    },
    replaceUrlDomain:function (url){
        if (url == undefined)
            return url;
        if (url == null)
            return url;
        if (url == '')
            return url;
        const baseUrl = new URL(config.baseUrl);
        const targetUrl = new URL(url);
        //医院有内网和外网; 所有的文件资源是存放到存储对象中，如果数据库记录的存储对象的地址为外网地址
        //当前访问的为内网地址，且当前资源地址不是内网地址，则替换为内网地址
        //当前访问的为外网地址，且当前资源地址内网地址，则替换为外网地址
        if (baseUrl.hostname === "**************" &&
            targetUrl.hostname !== "**************" ) {
            targetUrl.hostname = baseUrl.hostname;
        }else if (baseUrl.hostname !== "**************" &&
            targetUrl.hostname === "**************"){
            targetUrl.hostname = "zhhq.gzzlyy.com";
        }
        return targetUrl.toString();
    },
    /*
    {
      "label": "上传到Minio",
      "tag": "el-upload",
      "tagIcon": "upload",
      "layout": "colFormItem",
      "defaultValue": "[{\"name\":\"https://zhhq.gzzlyy.com:10006/hrss-data/2024/12/28/test_20241228111835A002.png\",\"url\":\"https://zhhq.gzzlyy.com:10006/hrss-data/2024/12/28/test_20241228111835A002.png\"}]",
      "showLabel": true,
      "labelWidth": null,
      "required": true,
      "span": 24,
      "showTip": false,
      "buttonText": "点击上传",
      "regList": [
          {
              "required": true,
              "message": "上传到Minio不能为空"
          }
      ],
      "changeTag": true,
      "fileSize": 2,
      "sizeUnit": "MB",
      "document": "https://element.eleme.cn/#/zh-CN/component/upload",
      "formId": 101,
      "renderKey": "1011725003178742"
  }
  {
      "label": "手写签名上传到Minio",
      "tag": "handSign",
      "tagIcon": "upload",
      "layout": "colFormItem",
      "defaultValue": "https://zhhq.gzzlyy.com:10006/hrss-data/2024/12/28/esignature_20241228111859A003.jpg",
      "showLabel": true,
      "labelWidth": null,
      "required": true,
      "span": 24,
      "showTip": false,
      "buttonText": "点击上传",
      "regList": [
          {
              "required": true,
              "message": "手写签名上传到Minio不能为空"
          },
          {
              "required": true,
              "message": "手写签名上传到Minio不能为空"
          }
      ],
      "changeTag": true,
      "fileSize": 2,
      "sizeUnit": "MB",
      "document": "https://element.eleme.cn/#/zh-CN/component/upload",
      "formId": 105,
      "renderKey": "1051725003185016"
  }
     */
    handleUrl4OSS:function (config){
        if (config == undefined)
            return;
        if (config == null)
            return;
        if (config.tag == undefined)
            return;
        if (config.tag == null)
            return;
        if (config.tag !== 'el-upload' && config.tag !== 'handSign')
            return;
        if (config.defaultValue == undefined){
            config.defaultValue = (config.tag == 'handSign')? '' : JSON.stringify([]);
            return;
        }

        if (config.defaultValue == null){
            config.defaultValue = (config.tag == 'handSign')? '' : JSON.stringify([]);
            return;
        }
        if (config.defaultValue == '')
            return;

        //手写签名组件,defaultValue为一个图片地址
        if (config.tag == 'handSign'){
            config.defaultValue = CommUtil.replaceUrlDomain(config.defaultValue);
            return;
        }
        //upload组件defaultValue为数组[name  url]
        let ary = JSON.parse(config.defaultValue)
        if (ary == undefined)
            return;
        if (ary == null)
            return;
        if (Array.isArray(ary) == false)
            return;
        for (let i = 0; i < ary.length; i++) {
            let item = ary[i];
            if (item.url == undefined)
                continue;
            if (item.url == null)
                continue;
            if (item.url == '')
                continue;
            item.url = CommUtil.replaceUrlDomain(item.url);

            // if (item.name == undefined)
            //   continue;
            // if (item.name == null)
            //   continue;
            // if (item.name == '')
            //   continue;
            // item.name = CommUtil.replaceUrlDomain(item.name);
        }
        //转为json字符串
        config.defaultValue = JSON.stringify(ary);
    },


}

