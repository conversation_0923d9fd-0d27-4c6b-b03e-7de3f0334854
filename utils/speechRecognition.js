/**
 * 百度语音识别API工具类
 */
import config from '@/config.js';
import { toast } from '@/utils/common.js';
import { uploadAudio } from '@/api/system/user'; // 导入已封装好的上传音频接口

// 百度语音识别API配置
const BAIDU_API = {
  // 这些值需要从百度智能云平台获取
  APP_ID: '119356547', // 填入您的百度语音识别APP_ID
  API_KEY: 'TazeQE3A6GjeQNiDm5zFkliH', // 填入您的百度语音识别API_KEY
  SECRET_KEY: 'j9rhLChjRyvwwHFUwKJBm78Rexom8WRw', // 填入您的百度语音识别SECRET_KEY
  TOKEN_URL: 'https://aip.baidubce.com/oauth/2.0/token',
  ASR_URL: 'http://vop.baidu.com/server_api'
};

// 获取百度语音识别的访问令牌
const getBaiduToken = async () => {
  console.log('[语音识别] 开始获取百度语音识别token');
  try {
    const tokenUrl = `${BAIDU_API.TOKEN_URL}?grant_type=client_credentials&client_id=${BAIDU_API.API_KEY}&client_secret=${BAIDU_API.SECRET_KEY}`;
    console.log('[语音识别] 请求token URL:', tokenUrl);
    
    const response = await uni.request({
      url: tokenUrl,
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('[语音识别] 百度token响应:', response);
    
    if (response && response[1] && response[1].data && response[1].data.access_token) {
      console.log('[语音识别] 获取token成功:', response[1].data.access_token);
      return response[1].data.access_token;
    } else {
      console.error('[语音识别] 获取token失败, 响应数据结构不正确');
      throw new Error('获取百度语音识别token失败');
    }
  } catch (error) {
    console.error('[语音识别] 获取token出错:', error);
    toast('语音服务初始化失败');
    return null;
  }
};

// 录音管理器
let recorderManager = null;
let innerAudioContext = null;
let tempFilePath = '';

// 初始化录音管理器和音频播放器
const initRecorder = () => {
  console.log('[语音识别] 初始化录音管理器');
  try {
    if (!recorderManager) {
      // 初始化录音管理器
      console.log('[语音识别] 创建录音管理器');
      recorderManager = uni.getRecorderManager();
    }
    
    if (!innerAudioContext) {
      // 初始化音频播放器
      console.log('[语音识别] 创建音频播放器');
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.autoplay = false;
    }
    
    return recorderManager;
  } catch (error) {
    console.error('[语音识别] 初始化录音管理器失败:', error);
    return null;
  }
};

/**
 * 开始语音识别
 * @param {Function} onRecognitionStart 开始录音回调
 * @param {Function} onRecognitionEnd 结束录音回调
 * @param {Function} onRecognitionSuccess 识别成功回调，返回识别文本
 * @param {Function} onRecognitionError 识别失败回调
 */
export const startSpeechRecognition = (onRecognitionStart, onRecognitionEnd, onRecognitionSuccess, onRecognitionError) => {
  console.log('[语音识别] 开始语音识别流程');
  try {
    const recorder = initRecorder();
    if (!recorder) {
      console.error('[语音识别] 初始化录音管理器失败');
      if (onRecognitionError) onRecognitionError('初始化录音管理器失败');
      return null;
    }
    
    // 检查平台
    const systemInfo = uni.getSystemInfoSync();
    console.log('[语音识别] 当前平台信息:', systemInfo.platform);
    
    // 设置录音参数
    const options = {
      // duration: 60000, // 最长录音时间，单位ms
      // sampleRate: 16000, // 采样率，必须为16000
      // numberOfChannels: 1, // 录音通道数，必须为1（单声道）
      // encodeBitRate: 48000, // 编码码率
      format: 'PCM', // 音频格式，使用pcm格式
      // frameSize: 50 // 指定帧大小
    };
    
    console.log('[语音识别] 设置录音参数:', options);
    
    // 录音开始事件
    recorder.onStart(() => {
      console.log('[语音识别] 录音开始');
      if (onRecognitionStart) onRecognitionStart();
    });
    
    // 录音结束事件
    recorder.onStop(async (res) => {
      console.log('[语音识别] 录音结束, 结果:', res);
      if (onRecognitionEnd) onRecognitionEnd();
      
      try {
        // 获取录音文件临时路径
        tempFilePath = res.tempFilePath;
        console.log('[语音识别] 录音文件路径:', tempFilePath);
        
        // 获取音频文件的base64内容
        try {
          // 上传音频文件获取base64内容
          console.log('[语音识别] 开始获取音频文件base64内容');
          const fileData = await getAudioBase64(tempFilePath);
          
          if (!fileData) {
            console.error('[语音识别] 无法获取音频文件内容');
            if (onRecognitionError) onRecognitionError('无法读取录音文件');
            return;
          }
          
          console.log("base64音频", fileData.base64);
          console.log('[语音识别] 获取音频base64成功, 长度:', fileData.base64.length);
          console.log('[语音识别] 原始音频文件字节数:', fileData.size);
          
          // 获取百度访问令牌
          const token = await getBaiduToken();
          if (!token) {
            console.error('[语音识别] 获取百度token失败');
            if (onRecognitionError) onRecognitionError('获取语音识别授权失败');
            return;
          }
          
          // 获取设备信息作为cuid
          const cuid = systemInfo.deviceId || systemInfo.platform + '_' + systemInfo.model || 'hrss_applet';
          console.log('[语音识别] 设备ID:', cuid);
          
          // 调用百度语音识别API
          console.log('[语音识别] 开始调用百度语音识别API');
          const response = await uni.request({
            url: BAIDU_API.ASR_URL,
            method: 'POST',
            header: {
              'Content-Type': 'application/json'
            },
            data: {
              format: 'pcm',       // 语音文件的格式
              rate: 16000,         // 采样率，固定值16000
              channel: 1,          // 声道数，固定值1
              cuid: cuid,          // 用户唯一标识
              token: token,        // 开放平台获取到的开发者access_token
              speech: fileData.base64,    // 本地语音文件的二进制语音数据，需要进行base64编码
              len: fileData.size,  // 原始音频文件的字节数
              dev_pid: 1537        // 普通话输入法模型，dev_pid参数见官方文档
            }
          });
          
          console.log('[语音识别] 百度语音识别响应:', response);
          
          // 处理识别结果
          if (response && response[1] && response[1].data && response[1].data.result) {
            const recognizedText = response[1].data.result[0];
            console.log('[语音识别] 识别成功, 结果:', recognizedText);
            if (onRecognitionSuccess) onRecognitionSuccess(recognizedText);
          } else {
            console.error('[语音识别] 语音识别返回错误:', response && response[1] && response[1].data);
            if (onRecognitionError) onRecognitionError('语音识别失败');
          }
        } catch (error) {
          console.error('[语音识别] 处理录音文件失败:', error);
          if (onRecognitionError) onRecognitionError('处理录音文件失败: ' + error.message);
        }
      } catch (error) {
        console.error('[语音识别] 语音识别处理出错:', error);
        if (onRecognitionError) onRecognitionError('语音识别处理出错');
      }
    });
    
    // 录音错误事件
    recorder.onError((error) => {
      console.error('[语音识别] 录音错误:', error);
      if (onRecognitionError) onRecognitionError(error.errMsg || '录音出错，可能是权限问题');
    });
    
    // 开始录音
    console.log('[语音识别] 开始录音');
    recorder.start(options);
    
    // 返回录音管理器，方便外部控制
    return recorder;
  } catch (error) {
    console.error('[语音识别] 启动录音失败:', error);
    if (onRecognitionError) onRecognitionError('启动录音失败，请检查权限设置');
    return null;
  }
};

/**
 * 停止语音识别
 */
export const stopSpeechRecognition = () => {
  console.log('[语音识别] 停止语音识别');
  if (recorderManager) {
    recorderManager.stop();
  }
};

/**
 * 播放录音
 */
export const playRecording = () => {
  console.log('[语音识别] 播放录音:', tempFilePath);
  if (tempFilePath && innerAudioContext) {
    innerAudioContext.src = tempFilePath;
    innerAudioContext.play();
  }
};

/**
 * 获取音频文件的base64编码
 * @param {String} filePath 文件路径
 * @returns {Promise<Object>} 包含base64编码和原始文件大小的对象
 */
const getAudioBase64 = (filePath) => {
  console.log('[语音识别] 开始获取音频文件base64:', filePath);
  return new Promise((resolve, reject) => {
    // 先获取文件信息，得到文件大小
    uni.getFileInfo({
      filePath: filePath,
      success: (fileInfo) => {
        console.log('[语音识别] 获取文件信息成功:', fileInfo);
        const originalFileSize = fileInfo.size;
        
        // 参考inspectPlaceDetails.vue中的方法，准备上传数据
        const data = {
          name: 'file',
          filePath: filePath
        };
        
        // 使用封装好的上传接口
        uploadAudio(data)
          .then(res => {
            console.log('[语音识别] 上传音频成功:', res);
            
            if (res && res.code === 200 && res.url) {
              console.log('[语音识别] 上传成功，获取到URL:', res.url);
              
              // 从URL下载文件并转换为base64
              downloadFileAsBase64(res.url)
                .then(base64Data => {
                  console.log('[语音识别] 成功将PCM文件转换为base64, 长度:', base64Data.length);
                  resolve({
                    base64: base64Data,
                    size: originalFileSize
                  });
                })
                .catch(error => {
                  console.error('[语音识别] 下载文件或转换base64失败:', error);
                  reject(error);
                });
            } else {
              console.error('[语音识别] 上传响应格式错误:', res);
              reject(new Error('上传响应格式错误'));
            }
          })
          .catch(error => {
            console.error('[语音识别] 上传音频失败:', error);
            reject(error);
          });
      },
      fail: (error) => {
        console.error('[语音识别] 获取文件信息失败:', error);
        reject(error);
      }
    });
  });
};

/**
 * 从URL下载文件并转换为base64格式
 * @param {String} url 文件URL
 * @returns {Promise<String>} base64编码的文件内容
 */
const downloadFileAsBase64 = (url) => {
  console.log('[语音识别] 开始下载文件:', url);
  // const urls = "https://zhhq.gzzlyy.com:10006/hrss-data/2025/06/26/1750932037647_20250626180041A010.pcm"
  return new Promise((resolve, reject) => {
    // 直接使用uni.request获取文件内容
    uni.request({
      url: url,
      responseType: 'arraybuffer',
      success: (requestRes) => {
        if (requestRes.statusCode === 200 && requestRes.data) {
          // 将ArrayBuffer转换为Base64
          const base64 = arrayBufferToBase64(requestRes.data);
          console.log('[语音识别] 获取文件内容成功, 数据长度:', base64.length);
          resolve(base64);
        } else {
          console.error('[语音识别] 获取文件内容失败:', requestRes);
          reject(new Error('获取文件内容失败'));
        }
      },
      fail: (err) => {
        console.error('[语音识别] 请求文件内容失败:', err);
        reject(err);
      }
    });
  });
};

/**
 * 将ArrayBuffer转换为Base64字符串
 * @param {ArrayBuffer} buffer 
 * @returns {String} Base64字符串
 */
const arrayBufferToBase64 = (buffer) => {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  
  return btoa(binary);
}; 