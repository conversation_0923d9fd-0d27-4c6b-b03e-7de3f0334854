import { getToken } from '@/utils/auth'
import config from '@/config'

class WebSocketClient {
  constructor(options = {}) {
    this.options = {
      url: '',
      params: {},
      onMessage: null,
      onOpen: null,
      onClose: null,
      onError: null,
      ...options
    }

    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 10
    this.allowReconnect = true
    this.isReconnecting = false
  }

  getWsUrl() {
    const token = getToken()
    if (!token) {
      throw new Error('未找到有效的认证Token')
    }

    // 构建URL参数字符串
    const params = Object.entries({
      Authorization: `Bearer ${token}`,
      ...(this.options.params || {})
    })
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&')

    // 构建完整的WebSocket URL
    const baseUrl = config.baseUrl.replace('http://', 'ws://').replace('https://', 'wss://')
    return `${baseUrl}${this.options.url}${params ? '?' + params : ''}`
  }

  connect() {
    if (this.isConnected) {
      return
    }

    try {
      const wsUrl = this.getWsUrl()
      
      uni.connectSocket({
        url: wsUrl,
        success: () => {
          console.log('WebSocket连接创建成功')
        },
        fail: (error) => {
          console.error('WebSocket连接创建失败:', error)
          if (this.options.onError) {
            this.options.onError(error)
          }
          this.reconnect()
        }
      })

      this.bindEvents()
    } catch (error) {
      console.error('WebSocket连接错误:', error)
      if (this.options.onError) {
        this.options.onError(error)
      }
      this.reconnect()
    }
  }

  bindEvents() {
    uni.onSocketOpen(() => {
      console.log('WebSocket连接已打开')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.isReconnecting = false
      if (this.options.onOpen) {
        this.options.onOpen()
      }
    })

    uni.onSocketMessage((res) => {
      try {
        const message = JSON.parse(res.data)
        if (this.options.onMessage) {
          this.options.onMessage(message)
        }
      } catch (e) {
        if (this.options.onMessage) {
          this.options.onMessage(res.data)
        }
      }
    })

    uni.onSocketError((error) => {
      console.error('WebSocket错误:', error)
      this.isConnected = false
      if (this.options.onError) {
        this.options.onError(error)
      }
      this.reconnect()
    })

    uni.onSocketClose(() => {
      console.log('WebSocket连接已关闭')
      this.isConnected = false
      if (this.options.onClose) {
        this.options.onClose()
      }
      if (this.allowReconnect) {
        this.reconnect()
      }
    })
  }

  reconnect() {
    if (!this.allowReconnect || this.isReconnecting) {
      return
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.isReconnecting = true
      this.reconnectAttempts++

      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 300000)
      console.log(`将在 ${delay/1000} 秒后进行第 ${this.reconnectAttempts} 次重试`)

      setTimeout(() => {
        if (this.allowReconnect) {
          this.connect()
        }
        this.isReconnecting = false
      }, delay)
    } else {
      console.log('达到最大重连次数，停止重连')
      if (this.options.onError) {
        this.options.onError(new Error('连接多次失败，请检查网络设置或刷新页面重试'))
      }
      this.isReconnecting = false
    }
  }

  send(data) {
    if (!this.isConnected) {
      console.error('WebSocket未连接，消息发送失败')
      return
    }

    uni.sendSocketMessage({
      data: typeof data === 'string' ? data : JSON.stringify(data),
      success: () => {
        console.log('消息发送成功')
      },
      fail: (error) => {
        console.error('消息发送失败:', error)
        if (this.options.onError) {
          this.options.onError(error)
        }
      }
    })
  }

  close() {
    this.allowReconnect = false
    if (this.isConnected) {
      uni.closeSocket({
        success: () => {
          console.log('WebSocket已关闭')
        }
      })
    }
    this.isConnected = false
  }
}

export function createWebSocket(options) {
  return new WebSocketClient(options)
} 