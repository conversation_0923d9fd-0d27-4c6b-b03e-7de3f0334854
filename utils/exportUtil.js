import config from '@/config.js'
import { getToken } from '@/utils/auth'

/**
 * 通用Excel导出方法
 * @param {Object} options - 导出配置选项
 * @param {string} options.url - 导出接口的相对路径
 * @param {Object} [options.params={}] - URL查询参数
 * @param {string} [options.baseUrl=config.baseUrl] - 基础URL，默认使用config中的baseUrl
 * @param {Object} [options.header={}] - 额外的请求头
 * @param {string} [options.fileType='xlsx'] - 文件类型，默认为'xlsx'
 * @returns {Promise} 返回一个Promise对象
 */
export function exportExcel(options) {
  return new Promise((resolve, reject) => {
    const {
      url,
      params = {},
      baseUrl = config.baseUrl,
      header = {},
      fileType = 'xlsx'
    } = options

    // 获取token
    const token = getToken()
    if (!token) {
      uni.showToast({
        title: '登录状态已过期，请重新登录',
        icon: 'none'
      })
      reject(new Error('未登录'))
      return
    }

    // 构建完整的URL
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value !== undefined && value !== null ? value : '')}`)
      .join('&')
    const fullUrl = `${baseUrl}${url}${queryString ? '?' + queryString : ''}`

    // 合并请求头
    const defaultHeader = {
      'Authorization': 'Bearer ' + token,
      'content-type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    const finalHeader = { ...defaultHeader, ...header }

    // 下载文件
    uni.downloadFile({
      url: fullUrl,
      header: finalHeader,
      method: 'GET',
      success: (downloadRes) => {
        console.log('下载成功', downloadRes)
        uni.openDocument({
          filePath: downloadRes.tempFilePath,
          fileType: fileType,
          showMenu: true,
          success: () => {
            uni.showToast({
              title: '导出成功',
              icon: 'success'
            })
            resolve(downloadRes)
          },
          fail: (err) => {
            console.error('打开文件失败', err)
            uni.showToast({
              title: '文件已下载，但无法打开',
              icon: 'none'
            })
            reject(err)
          }
        })
      },
      fail: (err) => {
        console.error('下载文件失败', err)
        uni.showToast({
          title: '下载失败，请重试',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
} 