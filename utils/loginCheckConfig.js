/**
 * 登录检查配置文件
 * 用于统一管理登录检查的各种配置项
 */

const loginCheckConfig = {
  // 基础配置
  basic: {
    // 是否启用onShow时的登录检查
    enableOnShowCheck: true,
    // 是否显示友好提示信息
    showFriendlyTips: true,
    // 是否启用调试日志
    enableDebugLog: true
  },

  // 时间配置（毫秒）
  timing: {
    // 后台多长时间后需要重新检查登录（默认30分钟）
    recheckThreshold: 30 * 60 * 1000,
    // 友好提示显示时长
    toastDuration: 2000,
    // 跳转登录页前的延迟时间
    redirectDelay: 1500,
    // Token验证超时时间
    validateTimeout: 10000
  },

  // 提示信息配置
  messages: {
    // 应用启动时未登录
    launchNoToken: '欢迎使用，请先登录',
    // 后台切换时登录失效
    backgroundNoToken: '登录状态已失效，请重新登录',
    // 一般情况下未登录
    generalNoToken: '请先登录',
    // 登录状态验证中
    validating: '验证登录状态...',
    // 登录状态正常
    validSuccess: '登录状态正常',
    // 登录状态异常
    validFailed: '登录状态异常',
    // 需要重新登录的提示
    needRelogin: '您的登录状态已过期，需要重新登录'
  },

  // 平台特定配置
  platform: {
    // 钉钉小程序特殊配置
    dingtalk: {
      // 是否尝试自动登录
      enableAutoLogin: true,
      // 自动登录失败后是否显示特殊提示
      showAutoLoginFailTip: true
    },
    // 微信小程序特殊配置
    wechat: {
      // 微信小程序特定的检查间隔
      recheckThreshold: 20 * 60 * 1000 // 20分钟
    },
    // H5特殊配置
    h5: {
      // H5环境下是否启用页面可见性检查
      enableVisibilityCheck: true
    }
  },

  // 开发环境配置
  development: {
    // 开发环境下是否跳过某些检查
    skipServerValidation: false,
    // 开发环境下的调试信息级别
    debugLevel: 'verbose' // 'none', 'basic', 'verbose'
  },

  // 生产环境配置
  production: {
    // 生产环境下是否启用错误上报
    enableErrorReport: true,
    // 生产环境下的调试信息级别
    debugLevel: 'basic'
  }
}

export default loginCheckConfig

/**
 * 获取当前环境的配置
 * @returns {Object} 合并后的配置对象
 */
export function getCurrentConfig() {
  // 获取默认配置
  const defaultConfig = {
    basic: {
      enableOnShowCheck: true,
      showFriendlyTips: true,
      enableDebugLog: true
    },
    timing: {
      recheckThreshold: 30 * 60 * 1000,
      toastDuration: 2000,
      redirectDelay: 1500,
      validateTimeout: 10000
    },
    messages: {
      launchNoToken: '欢迎使用，请先登录',
      backgroundNoToken: '登录状态已失效，请重新登录',
      generalNoToken: '请先登录',
      validating: '验证登录状态...',
      validSuccess: '登录状态正常',
      validFailed: '登录状态异常',
      needRelogin: '您的登录状态已过期，需要重新登录'
    },
    platform: {
      dingtalk: { enableAutoLogin: true, showAutoLoginFailTip: true },
      wechat: { recheckThreshold: 20 * 60 * 1000 },
      h5: { enableVisibilityCheck: true }
    }
  }

  const config = JSON.parse(JSON.stringify(defaultConfig))

  // 根据环境合并配置
  try {
    // #ifdef H5
    if (config.platform && config.platform.h5) {
      Object.assign(config.timing, config.platform.h5)
    }
    // #endif

    // #ifdef MP-DINGTALK
    if (config.platform && config.platform.dingtalk) {
      Object.assign(config.timing, config.platform.dingtalk)
    }
    // #endif

    // #ifdef MP-WEIXIN
    if (config.platform && config.platform.wechat) {
      Object.assign(config.timing, config.platform.wechat)
    }
    // #endif
  } catch (error) {
    console.error('配置合并失败:', error)
  }

  return config
}

/**
 * 获取当前平台信息
 * @returns {Object} 平台信息
 */
export function getPlatformInfo() {
  try {
    const systemInfo = uni.getSystemInfoSync()

    // 基础平台信息
    const platformInfo = {
      platform: systemInfo.platform || 'unknown',
      platformName: '未知平台'
    }
    console.log('当前平台信息:', platformInfo)

    // 根据编译环境设置平台特定信息（避免重复声明）
    let platformSpecific = {}

    // #ifdef MP-DINGTALK
    platformSpecific = { isDingTalk: true, platformName: '钉钉小程序' }
    // #endif

    // #ifdef MP-WEIXIN
    platformSpecific = { isWeChat: true, platformName: '微信小程序' }
    // #endif

    // #ifdef MP-ALIPAY
    platformSpecific = { isAlipay: true, platformName: '支付宝小程序' }
    // #endif

    // #ifdef H5
    platformSpecific = { isH5: true, platformName: 'H5' }
    // #endif

    // #ifdef APP-PLUS
    platformSpecific = { isApp: true, platformName: 'App' }
    // #endif

    // 合并平台特定信息
    Object.assign(platformInfo, platformSpecific)

    return platformInfo
  } catch (error) {
    console.error('获取平台信息失败:', error)
    return {
      platform: 'unknown',
      platformName: '未知平台'
    }
  }
}
