<template>
  <view class="container">
    <view class="card-detail p20">
      <view class="detail-item" v-for="(field, index) in fields" :key="index">
        <view class="label">{{field.label}}</view>
        <view class="value">{{cardInfo[field.prop] || '暂无数据'}}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cardInfo: {},
      fields: [
        { label: '资产编号', prop: 'cardId' },
        { label: '资产名称', prop: 'cardName' },
        { label: '一级分类', prop: 'class1Id' },
        { label: '一级分类名称', prop: 'class1Name' },
        { label: '二级分类', prop: 'class2Id' },
        { label: '二级分类名称', prop: 'class2Name' },
        { label: '三级分类', prop: 'class3Id' },
        { label: '三级分类名称', prop: 'class3Name' },
        { label: '型号规格', prop: 'cardSpec' },
        { label: '计量单位', prop: 'unitName' },
        { label: '科室编号', prop: 'deptCode' },
        { label: '科室名称', prop: 'deptName' },
        { label: '累计折旧', prop: 'depreciation' },
        { label: '账面净值', prop: 'netValue' },
        { label: '月折旧额', prop: 'monthDepreciation' },
        { label: '折旧状态', prop: 'depreciationStatus' },
        { label: '折旧年限', prop: 'depreciationLife' },
        { label: '使用日期', prop: 'useDate' },
        { label: '报废时间', prop: 'scrapDate' },
        { label: '供应商', prop: 'venderName' },
        { label: '厂家名称', prop: 'factoryName' },
        { label: '备注', prop: 'remark' },
        { label: '累计折旧月数', prop: 'monthDepreciationNum' },
        { label: '旧资产编号', prop: 'oldCardNumber' },
        { label: '存放地点', prop: 'storagePlace' },
        { label: '设备真实状态', prop: 'deviceRealStatus' },
        { label: '到期日期', prop: 'expireDate' },
        { label: '出厂编号', prop: 'factoryNumber' },
        { label: '仓库', prop: 'warehouse' },
        { label: '供货人', prop: 'supplier' },
        { label: '供货人联系方式', prop: 'supplierContact' },
        { label: '报修联系人', prop: 'repairContact' },
        { label: '报修联系电话', prop: 'repairContactPhone' },
        { label: '数量', prop: 'number' },
        { label: '金额', prop: 'price' },
        { label: '生产日期', prop: 'produceDate' },
        { label: '序列号', prop: 'serialNumber' },
        { label: '来源', prop: 'source' }
      ]
    }
  },
  onLoad(options) {
    if (options.cardInfo) {
      this.cardInfo = JSON.parse(decodeURIComponent(options.cardInfo));
      console.log('详情数据：', this.cardInfo);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.card-detail {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  border-bottom: 1px solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    color: #666;
    font-size: 28rpx;
    min-width: 200rpx;
  }
  
  .value {
    flex: 1;
    color: #333;
    font-size: 28rpx;
    text-align: right;
    word-break: break-all;
  }
}
</style> 