<template>
  <view class="container">
    <view class="nav bf">
      <view class="flex align-center p20">
        <uni-easyinput
            prefixIcon="search"
            @focus="onSearchFocus"
            @blur="onSearchBlur"
            @input="onSearchInput"
            v-model="queryParams.cardName"
            placeholder="请输入设备名称"
            :clearable="true"
            @clear="handleClear"
            @confirm="handleSearch"
            confirmType="search"
        >
        </uni-easyinput>
      </view>
      <view class="search-options p20" v-if="flag">
        <view class="option-row">
          <view class="search-label">设备位置</view>
          <uni-easyinput
              v-model="queryParams.storagePlace"
              placeholder="输入设备位置"
              :clearable="true"
          >
          </uni-easyinput>
        </view>
        <view class="option-row">
          <view class="search-label">设备类型</view>
          <uni-easyinput
              v-model="queryParams.class2Name"
              placeholder="输入设备类型"
              :clearable="true"
          >
          </uni-easyinput>
        </view>
        <view class="btn_big" @click="handleSearch">
          搜索
        </view>
        <view class="btn_clear" @click="resetSearch">
          重置
        </view>
      </view>
      <!-- 添加切换按钮 -->
      <view class="switch-container p20">
        <!-- 添加导出按钮 -->
        <view class="export-btn" @click="handleExport">
          导出
        </view>
        <view class="switch-group">
          <view class="switch-text">{{ isWarranty ? '设备过保' : '在保设备' }}</view>
          <view class="switch-btn" :class="{ 'active': isWarranty }" @click="toggleWarrantyStatus">
            <view class="switch-circle" :class="{ 'active': isWarranty }"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="asset_list p20">
      <view class="p20 bf card asset_info" v-for="item in myList" :key="item.cardId">
        <view class="just-sbet " >
          <view class="key">
            设备编号
          </view>
          <view class="">
            {{item.cardNumber}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            设备类型
          </view>
          <view class="">
            {{item.class2Name}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            设备名称
          </view>
          <view class="">
            {{item.cardName}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            设备位置
          </view>
          <view class="">
            {{item.storagePlace}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            设备归属部门
          </view>
          <view class="">
            {{item.deptName}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            故障次数
          </view>
          <view class="">
            {{item.breakDownCount}}
          </view>
        </view> <view class="just-sbet ">
        <view class="key">
           被巡检数
        </view>
        <view class="">
          {{item.inspectedCount}}
        </view>
        </view>
<!--    可扩展功能    -->
<!--        <view class="just-sbet ">-->
<!--          <view class="key">-->
<!--          </view>-->
<!--          &lt;!&ndash; <view class="tips center" @click="hrpRepairReport(item)" >-->
<!--            报修<uni-icons type="forward" color="#fff"></uni-icons>-->
<!--          </view> &ndash;&gt;-->
<!--          <view class="tips center" @click="cardDetail" :data-index="item" >-->
<!--            详情<uni-icons type="forward" color="#fff"></uni-icons>-->
<!--          </view>-->
<!--        </view>-->
      </view>
    </view>

    <!-- 添加分页组件 -->
    <view class="pagination-container" v-if="total > 0">
      <view class="pagination">
        <view class="page-btn" :class="{ disabled: queryParams.pageNum <= 1 }" @click="handlePrevPage">
          上一页
        </view>
        <view class="page-info">
          第 {{queryParams.pageNum}} 页 / 共 {{totalPages}} 页
        </view>
        <view class="page-btn" :class="{ disabled: queryParams.pageNum >= totalPages }" @click="handleNextPage">
          下一页
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {getUserProfile} from "../../api/system/user";
import {getDeviceInfo, getDeviceList} from "../../api/portal/deptInfo";
import { exportExcel } from "../../utils/exportUtil";

export default {

  data() {
    return {
      myList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // hrp资产表格数据
      cardList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //获取WarrantyStatus进行判断
      fetWarrantyStatus: '',
      //设置科室id默认值
      deptId : -1,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warrantyStatus: 1,
        cardName: null,
        class1Id: null,
        class1Name: null,
        class2Id: null,
        class2Name: null,
        class3Id: null,
        class3Name: null,
        deptCode: null,
        deptName: null,
        venderName: null,
        factoryName: null,
        cardId: null,
        storagePlace: null,
        warehouse: null,
        produceDate: null,
        serialNumber: null,
        source: null
      },
      flag: false,
      // 添加在保设备状态，false 表示过保设备，true 表示在保设备
      isWarranty: false,
      // 搜索输入延迟定时器
      searchTimer: null,
    }
  },
  computed: {
    // 计算总页数
    totalPages() {
      return Math.ceil(this.total / this.queryParams.pageSize) || 1;
    }
  },
  onLoad() {
    console.log('页面 onLoad');
    console.log('当前页面的deptid:' , this.deptId)
  },

  onShow() {
    console.log('页面 onShow');
    if (this.deptId && !this.myList.length) {
      this.getDeviceListData();
    }
  },

  onReady() {
    console.log('页面 onReady');
  },

  methods: {
    // 获取设备列表
    getDeviceListData() {
      // 显示加载中提示
      uni.showLoading({
        title: '加载中...'
      });

      console.log('开始获取设备列表，参数：', {
        deptId: this.deptId,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        warrantyStatus: this.isWarranty ? 0 : 1,
        cardName: this.queryParams.cardName,
        storagePlace: this.queryParams.storagePlace,
        class2Name: this.queryParams.class2Name
      });

      getDeviceList({
        deptId: this.deptId,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        warrantyStatus: this.isWarranty ? 0 : 1,  // 1表示在保设备，0表示过保设备
        cardName: this.queryParams.cardName || undefined,
        storagePlace: this.queryParams.storagePlace || undefined,
        class2Name: this.queryParams.class2Name || undefined
      }).then(res => {
        // 隐藏加载提示
        uni.hideLoading();

        console.log('获取设备列表响应：', res);
        if (res.code === 200) {
          this.total = res.total;
          this.myList = res.rows;
          console.log('设备列表数据已更新，条数：', this.myList.length);

          // 如果搜索结果为空，显示提示
          if (this.myList.length === 0) {
            uni.showToast({
              title: '未找到匹配的设备',
              icon: 'none'
            });
          }
        } else {
          console.error('获取设备列表失败：', res.msg);
          uni.showToast({
            title: res.msg,
            icon: 'none'
          });
        }
      }).catch(error => {
        // 隐藏加载提示
        uni.hideLoading();

        console.error('获取设备列表失败：', error);
        uni.showToast({
          title: '获取设备列表失败',
          icon: 'none'
        });
      });
    },

    // 切换在保/过保状态
    toggleWarrantyStatus() {
      this.isWarranty = !this.isWarranty;
      this.queryParams.pageNum = 1; // 重置到第一页
      this.getDeviceListData();
    },

    // 处理上一页
    handlePrevPage() {
      if (this.queryParams.pageNum > 1) {
        this.queryParams.pageNum -= 1;
        this.getDeviceListData();
      }
    },

    // 处理下一页
    handleNextPage() {
      if (this.queryParams.pageNum < this.totalPages) {
        this.queryParams.pageNum += 1;
        this.getDeviceListData();
      }
    },

    // 搜索框获取焦点
    onSearchFocus() {
      this.flag = true;
    },

    // // 搜索框失去焦点
    // onSearchBlur() {
    //   console.log('onSearchBlur 触发，当前 flag:', this.flag);
    //   // 延迟隐藏搜索选项，让用户有时间点击搜索按钮
    //   setTimeout(() => {
    //     if (!this.queryParams.cardName && this.queryParams.storagePlace && this.queryParams.class2Name) {
    //       this.flag = false;
    //       console.log('onSearchBlur 触发，flag 设置为 false');
    //     }
    //   }, 200);
    // },

    // 输入框输入事件
    onSearchInput(value) {
      // 如果输入为空且没有其他搜索条件，自动执行搜索重置
      if (!value && !this.queryParams.storagePlace && !this.queryParams.class2Name) {
        this.handleClear();
      }

      // 实现输入防抖，避免频繁请求
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 如果输入内容达到2个字符以上，自动执行搜索
      if (value && value.length >= 2) {
        this.searchTimer = setTimeout(() => {
          this.handleSearch();
        }, 500); // 500ms延迟
      }
    },

    // 跳转到详情页
    // cardDetail(e) {
    //   const item = e.currentTarget.dataset.index;
    //   uni.navigateTo({
    //     url: `/pages/assetsList/cardDetail?cardInfo=${encodeURIComponent(JSON.stringify(item))}`
    //   });
    // },

    // 搜索
    handleSearch() {
      this.queryParams.pageNum = 1; // 重置到第一页
      this.getDeviceListData();
      this.flag = false; // 隐藏搜索按钮
    },

    // 清空搜索
    handleClear() {
      this.queryParams.cardName = '';
      this.queryParams.pageNum = 1;
      this.getDeviceListData();
      this.flag = false;
    },

    // 重置搜索条件
    resetSearch() {
      this.queryParams.cardName = '';
      this.queryParams.storagePlace = '';
      this.queryParams.class2Name = '';
      this.queryParams.pageNum = 1;
      this.getDeviceListData();
      this.flag = false;
    },

    // 导出设备列表
    handleExport() {
      try {
        uni.showLoading({
          title: '导出中...',
          mask: true
        });
        
        exportExcel({
          url: '/portal/deptInfo/appDeviceExport',
          params: {
            deptId: -1
          }
        }).then(() => {
          console.log('导出成功');
        }).catch(error => {
          console.error('导出失败：', error);
        }).finally(() => {
          uni.hideLoading();
        });
      } catch (error) {
        console.error('导出异常：', error);
        uni.hideLoading();
        uni.showToast({
          title: '导出失败',
          icon: 'none'
        });
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20rpx;
}

.just-sbet {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;

  .key {
    min-width: 140rpx;
    font-weight: 500;
    color: #666;
  }

  view:last-child {
    flex: 1;
    text-align: right;
    margin-left: 20rpx;
    color: #333;
    word-break: break-all;
  }
}

.asset_list {
  .asset_info {
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    background: #fff;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);

    .tips {
      color: #fff;
      background: #3296fa;
      padding: 5rpx 10rpx;
      border-radius: 20rpx;
      margin-left: auto;
      display: inline-flex;
      align-items: center;
    }
  }
}

.pagination-container {
  padding: 20rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;

  .page-btn {
    background: #4095E5;
    color: #fff;
    padding: 10rpx 30rpx;
    border-radius: 30rpx;
    font-size: 28rpx;

    &.disabled {
      background: #ccc;
      opacity: 0.7;
    }
  }

  .page-info {
    font-size: 28rpx;
    color: #666;
  }
}

.container {
  padding-bottom: 120rpx;
}

.nav {
  background: #fff;
  margin-bottom: 20rpx;
}

.btn_big {
  background: #4095E5;
  color: #fff;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;

  &:active {
    opacity: 0.8;
  }
}

.switch-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-group {
  display: flex;
  align-items: center;
}

.export-btn {
  background: #4095E5;
  color: #fff;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
  
  &:active {
    opacity: 0.8;
  }
}

.switch-text {
  font-size: 28rpx;
  margin-right: 20rpx;
  color: #666;
}

.switch-btn {
  width: 100rpx;
  height: 50rpx;
  background-color: #e0e0e0;
  border-radius: 25rpx;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
}

.switch-btn.active {
  background-color: #4095E5;
}

.switch-circle {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 3rpx;
  left: 3rpx;
  transition: all 0.3s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.switch-circle.active {
  transform: translateX(50rpx);
}

.search-options {
  background-color: #f5f5f5;
  border-radius: 12rpx;
  margin-top: 10rpx;
  padding-top: 10rpx;
}

.option-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
  padding-right: 20rpx;
}

.btn_clear {
  background: #f0f0f0;
  color: #666;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 20rpx;

  &:active {
    opacity: 0.8;
  }
}

.btn_big {
  margin-top: 20rpx;
}
</style>