<template>
  <view class="container">
    <view class="nav bf">
      <view class="flex align-center p20">
        <uni-easyinput
            prefixIcon="search"
            @focus="flag=true"
            v-model="queryParams.cardName"
            placeholder="请输入资产名称"
            :clearable="true"
            @clear="handleClear"
        >
        </uni-easyinput>
      </view>
      <view class="p20" v-if="flag">
        <view class="btn_big" @click="handleSearch">
          搜索
        </view>
      </view>
      <view class="p20">
        <view class="btn_big" @click="handleExport">
          导出
        </view>
      </view>
    </view>
    <view class="asset_list p20">
      <view class="p20 bf card asset_info" v-for="item in myList" :key="item.cardId">
        <view class="just-sbet " >
          <view class="key">
            资产编号
          </view>
          <view class="">
            {{item.cardId}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            资产名称
          </view>
          <view class="">
            {{item.cardName}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            使用日期
          </view>
          <view class="">
            {{item.useDate}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            到期时间
          </view>
          <view class="">
            {{item.expireDate}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            科室名称
          </view>
          <view class="">
            {{item.deptName}}
          </view>
        </view>
        <view class="just-sbet ">
          <view class="key">
            设备状态
          </view>
          <view class="status-badge" :class="{
            'status-enabled': item.lifecycleState === 0,
            'status-repair': item.lifecycleState === 1,
            'status-disabled': item.lifecycleState === 2,
            'status-scrapped': item.lifecycleState === 3,
            'status-unknown': item.lifecycleState !== 0 && item.lifecycleState !== 1 && item.lifecycleState !== 2 && item.lifecycleState !== 3
          }">
            {{getStatusName(item.lifecycleState, item.lifecycleStateName)}}
          </view>
        </view>
        <!--        <view class="just-sbet ">-->
        <!--          <view class="key">-->
        <!--            型号规格-->
        <!--          </view>-->
        <!--          <view class="">-->
        <!--            {{item.cardSpec}}-->
        <!--          </view>-->
        <!--        </view>-->
        <view class="just-sbet ">
          <view class="key">
          </view>
          <!-- <view class="tips center" @click="hrpRepairReport(item)" >
            报修<uni-icons type="forward" color="#fff"></uni-icons>
          </view> -->
          <view class="tips center" @click="cardDetail" :data-index="item" >
            详情<uni-icons type="forward" color="#fff"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加分页组件 -->
    <view class="pagination-container" v-if="total > 0">
      <view class="pagination">
        <view class="page-btn" :class="{ disabled: queryParams.pageNum <= 1 }" @click="handlePrevPage">
          上一页
        </view>
        <view class="page-info">
          第 {{queryParams.pageNum}} 页 / 共 {{totalPages}} 页
        </view>
        <view class="page-btn" :class="{ disabled: queryParams.pageNum >= totalPages }" @click="handleNextPage">
          下一页
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {listCard} from "../../api/HRP/card";
import { exportExcel } from "../../utils/exportUtil";

export default {

  data() {
    return {
      myList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // hrp资产表格数据
      cardList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        cardName: null,
        class1Id: null,
        class1Name: null,
        class2Id: null,
        class2Name: null,
        class3Id: null,
        class3Name: null,
        deptCode: null,
        deptName: null,
        venderName: null,
        factoryName: null,
        cardId: null,
        storagePlace: null,
        warehouse: null,
        produceDate: null,
        serialNumber: null,
        source: null
      },
      flag: false,
    }
  },
  computed: {
    // 计算总页数
    totalPages() {
      return Math.ceil(this.total / this.queryParams.pageSize) || 1;
    }
  },
  onLoad() {
    this.getList();
  },

  methods: {
    /** 查询hrp资产列表 */
    getList() {
      this.loading = true;
      listCard(this.queryParams).then(response => {
        if (response && response.rows) {
          this.myList = response.rows;
          this.total = parseInt(response.total) || 0;
          console.log('当前页数据：', this.myList);
          console.log('总条数：', this.total);
          console.log('当前页码：', this.queryParams.pageNum);
          console.log('总页数：', this.totalPages);
        }
        this.loading = false;
      }).catch(error => {
        console.error('获取数据失败：', error);
        this.loading = false;
      });
    },

    // 处理上一页
    handlePrevPage() {
      if (this.queryParams.pageNum > 1) {
        this.queryParams.pageNum -= 1;
        this.getList();
      }
    },

    // 处理下一页
    handleNextPage() {
      if (this.queryParams.pageNum < this.totalPages) {
        this.queryParams.pageNum += 1;
        this.getList();
      }
    },

    // 跳转到详情页
    cardDetail(e) {
      const item = e.currentTarget.dataset.index;
      uni.navigateTo({
        url: `/pages/assetsList/cardDetail?cardInfo=${encodeURIComponent(JSON.stringify(item))}`
      });
    },

    // 搜索
    handleSearch() {
      this.queryParams.pageNum = 1; // 重置到第一页
      this.getList();
      this.flag = false; // 隐藏搜索按钮
    },

    // 清空搜索
    handleClear() {
      this.queryParams.cardName = '';
      this.queryParams.pageNum = 1;
      this.getList();
      this.flag = false;
    },

    // 导出数据
    handleExport() {
      uni.showLoading({
        title: '正在导出...',
        mask: true
      });

      exportExcel({
        url: '/ds/hrp/card/appExport',
        params: {
          deptCode: '',
          deptName: ''
        }
      }).catch(error => {
        console.error('导出失败：', error);
      }).finally(() => {
        uni.hideLoading();
      });
    },

    // 获取状态名称
    getStatusName(lifecycleState, lifecycleStateName) {
      // 如果有状态名称，优先使用状态名称
      if (lifecycleStateName) {
        return lifecycleStateName;
      }

      // 根据状态码返回对应名称
      const statusMap = {
        0: '启用',
        1: '维修',
        2: '停用',
        3: '报废'
      };

      return statusMap[lifecycleState] || '未知';
    },
  }
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20rpx;
}

.just-sbet {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;

  .key {
    min-width: 140rpx; // 设置标题最小宽度
    font-weight: 500;
    color: #666;
  }

  view:last-child {
    flex: 1;
    text-align: right;
    margin-left: 20rpx;
    color: #333;
    word-break: break-all; // 允许在任意字符间换行

    &.tips {
      flex: none;
      width: auto;
    }
  }
}

.asset_list {
  .asset_info {
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    background: #fff;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);

    .tips {
      color: #fff;
      background: #3296fa;
      padding: 5rpx 10rpx;
      border-radius: 20rpx;
      &.center {
        display: inline-flex;
        align-items: center;
      }
    }
  }
}

.pagination-container {
  padding: 20rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;

  .page-btn {
    background: #4095E5;
    color: #fff;
    padding: 10rpx 30rpx;
    border-radius: 30rpx;
    font-size: 28rpx;

    &.disabled {
      background: #ccc;
      opacity: 0.7;
    }
  }

  .page-info {
    font-size: 28rpx;
    color: #666;
  }
}

// 为了防止分页栏遮挡内容，给容器添加底部内边距
.container {
  padding-bottom: 120rpx;
}

.nav {
  background: #fff;
  margin-bottom: 20rpx;
}

.btn_big {
  background: #4095E5;
  color: #fff;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;

  &:active {
    opacity: 0.8;
  }
}

// 状态标签样式
.status-badge {
  padding: 4rpx 12rpx !important;
  border-radius: 12rpx !important;
  font-size: 24rpx !important;
  color: #fff !important;
  text-align: center !important;
  display: inline-block !important;
  white-space: nowrap !important;
  width: auto !important;
  min-width: 60rpx !important;
  max-width: 120rpx !important;
  flex: none !important; // 防止被父级flex影响
  margin-left: 0 !important; // 重置margin

  &.status-enabled {
    background: #52c41a !important; // 启用 - 绿色
    color: #fff !important;
  }

  &.status-repair {
    background: #fa8c16 !important; // 维修 - 橙色
    color: #fff !important;
  }

  &.status-disabled {
    background: #8c8c8c !important; // 停用 - 灰色
    color: #fff !important;
  }

  &.status-scrapped {
    background: #f5222d !important; // 报废 - 红色
    color: #fff !important;
  }

  &.status-unknown {
    background: #d9d9d9 !important; // 未知 - 浅灰色
    color: #fff !important;
  }
}
</style>