<template>
  <view class="containers main-box">
    <button class="button success" @click="zoomViewport(true)">放大</button>
    <button class="button warning" @click="zoomViewport(false)">缩小</button>
    <button class="button info" @click="fitViewport">适中</button>
    <view class="canvas" ref="flowCanvas"></view>
  </view>
</template>

<script>
import { CustomViewer as BpmnViewer } from "@/components/customBpmn";

export default {
  name: "FlowView",
  props: {
    flowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      bpmnViewer: null
    };
  },
  watch: {
    flowData: {
      handler(newVal) {
        if (Object.keys(newVal).length > 0) {
          console.log("this.bpmnViewer", this.bpmnViewer);
          this.bpmnViewer && this.bpmnViewer.destroy();
          this.bpmnViewer = new BpmnViewer({
            container: this.$refs.flowCanvas,
            height: 'calc(100vh - 200px)',
          });
          console.log("this.bpmnViewer2",this.bpmnViewer)
          this.loadFlowCanvas(newVal);
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    console.log("FlowView component mounted");
  },
  methods: {
    async loadFlowCanvas(flowData) {
      console.log("flowData",flowData);
      const self = this;
      try {
        console.log("Importing XML Data:", flowData.xmlData);
        await self.bpmnViewer.importXML(flowData.xmlData);
        console.log("flowData.xmlData",flowData.xmlData);
        self.fitViewport();
        if (flowData.nodeData !== undefined && flowData.nodeData.length > 0) {
          self.fillColor(flowData.nodeData);
        }
      } catch (err) {
        console.error("Error loading flow canvas:", err.message, err.warnings);
      }
    },
    fitViewport() {
      this.bpmnViewer.get('canvas').zoom("fit-viewport", "auto");
    },
    zoomViewport(zoomIn = true) {
      let zoom = this.bpmnViewer.get('canvas').zoom();
      zoom += (zoomIn ? 0.1 : -0.1);
      if (zoom >= 0.2) this.bpmnViewer.get('canvas').zoom(zoom);
    },
    fillColor(nodeData) {
      const canvas = this.bpmnViewer.get('canvas');
      this.bpmnViewer.getDefinitions().rootElements[0].flowElements.forEach(n => {
        const completeTask = nodeData.find(m => m.key === n.id);
        const todoTask = nodeData.find(m => !m.completed);
        const endTask = nodeData[nodeData.length - 1];

        // 核心逻辑与之前相同
        if (n.$type === 'bpmn:UserTask' && completeTask) {
          canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo');
          n.outgoing?.forEach(nn => {
            const targetTask = nodeData.find(m => m.key === nn.targetRef.id);
            if (targetTask) {
              canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo');
              canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo');
            }
            // 并行网关
            else if (n.$type === 'bpmn:ParallelGateway') {
              if (completeTask) {
                canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo')
                n.outgoing?.forEach(nn => {
                  const targetTask = nodeData.find(m => m.key === nn.targetRef.id)
                  if (targetTask) {
                    canvas.addMarker(nn.id, targetTask.completed ? 'highlight' : 'highlight-todo')
                    canvas.addMarker(nn.targetRef.id, targetTask.completed ? 'highlight' : 'highlight-todo')
                  }
                })
              }
            }
            else if (n.$type === 'bpmn:StartEvent') {
              n.outgoing.forEach(nn => {
                const completeTask = nodeData.find(m => m.key === nn.targetRef.id)
                if (completeTask) {
                  canvas.addMarker(nn.id, 'highlight')
                  canvas.addMarker(n.id, 'highlight')
                  return
                }
              })
            }
            else if (n.$type === 'bpmn:EndEvent') {
              if (endTask.key === n.id && endTask.completed) {
                canvas.addMarker(n.id, 'highlight')
                return
              }
            }
          });
        }


      });
    }
  }
};
</script>

<style scoped>
.containers {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.button {
  margin: 5px;
  padding: 10px;
  border: none;
  cursor: pointer;
  color: #fff;
  border-radius: 5px;
}

.success {
  background-color: green;
}

.warning {
  background-color: orange;
}

.info {
  background-color: blue;
}

.canvas {
  flex: 1;
  width: 100%;
  height: 100%;
}

.highlight {
  fill: green !important;
  stroke: green !important;
  fill-opacity: 0.2 !important;
}

.highlight-todo {
  stroke: orange !important;
  stroke-dasharray: 4px !important;
  fill-opacity: 0.2 !important;
}
</style>
