<template>
  <view class="">
    <web-view style="width: 100%; height: 100%;" :src="url"></web-view>

  </view>
</template>

<script>
import config from '../../../config';
export default {
  data() {
    return {
      url: ""
    };
  },
  onLoad(option) {
    let appToken = uni.getStorageSync('App-Token')
    console.log(option.url, 'aaa');
    if (option.url) {
      let baseUrl=config.rouyiUrl+'/myFlowInfo'
      let params = JSON.parse(option.url)
      let url =
          `${baseUrl}?procInsId=${params.procInsId}&executionId=${params.executionId}&deployId=${params.deployId}&taskId=${params.taskId}&taskName=${params.taskName}&startUser=${params.startUser}`

      this.url = url + `&appToken=${appToken}`
    }

    console.log(this.url, '12312');
  },
}
</script>

<style lang="scss">

</style>