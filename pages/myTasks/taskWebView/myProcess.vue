<template>
  <!-- 主要内容卡片 -->
  <view class="main-card">
    <view class="card-header">
      <view class="header-left">
        <text class="icon-document">已发任务</text>
      </view>
      <button size="mini" class="close-btn" @tap="goBack">关闭</button>
    </view>

    <!-- 标签页 -->
    <view class="tabs">
      <view class="tab-header">
        <view
            v-for="(tab, index) in tabs"
            :key="index"
            :class="['tab-item', activeName === tab.name ? 'active' : '']"
            @tap="switchTab(tab.name)"
        >
          {{tab.label}}
        </view>
      </view>

      <view class="tab-content">
        <!-- 表单信息 -->
        <view v-show="activeName === '1'"
              class="tab-pane"
              :style="{height: windowHeight ? 'calc('+ windowHeight + ' - 10rpx - 10rpx - 44px - 5px)' : ''}">
          <view class="form-container">
            <dynamic-form
                :form-config="variablesData"
                :process-variables="param"
            />
          </view>
        </view>

        <!-- 流转记录 -->
        <view v-show="activeName === '2'" class="tab-pane"
              :style="{height: windowHeight ? 'calc('+ windowHeight + ' - 10rpx - 10rpx - 44px - 5px)' : ''}">
          <flow-record :flowRecordList="flowRecordList"></flow-record>
        </view>

        <!-- 流程图 -->
        <view v-if="activeName === '3'"
              class="tab-pane"
              :style="{height: windowHeight ? 'calc('+ windowHeight + ' - 10rpx - 10rpx - 44px - 5px)' : ''}">
          <!--            <flow :flow-data="flowData"/>-->
          <py-bpmn-viewer :flowData="flowData" :imageUrl="processImageUrl"></py-bpmn-viewer>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {flowRecord,getDiagram4Base64} from "@/api/flowable/finished";
import {getProcessVariables, getFlowViewer, flowXmlAndNode} from "@/api/flowable/definition";
import PyBpmnViewer from '@/components/py/py-bpmn-viewer/py-bpmn-viewer.vue';

import FlowRecord from '@/components/flow/record/flow-record.vue';
import DynamicForm from '@/components/parser-nuoyi-form/DynamicForm.vue'
import {
  getWindowHeight,
  getVarType,
  getFormData
} from "@/utils/comUtil";
export default {
  name: "Record",
  components: {
    PyBpmnViewer,
    // Parser,
    //  flow,
    // FlowUser,
    // FlowRole,
    FlowRecord,
    DynamicForm
  },
  props: {},
  data() {
    return {
      deviceUseDeptId: 0,
      // 查询参数
      queryParams: {
        deptId: undefined
      },
      windowHeight: undefined,

      tabs: [
        {name: '1', label: '表单信息'},
        {name: '2', label: '流转记录'},
        {name: '3', label: '流程图'}
      ],

      param: {},
      // 模型xml数据
      xmlData: "",
      flowData: {},
      activeName: '1',
      variablesData: {},
      // 遮罩层
      loading: true,
      flowRecordList: [], // 流程流转数据
      // formConfCopy: {},
      src: null,
      // rules: {}, // 表单校验
      variablesForm: {}, // 流程变量数据
      taskForm: {
        procInsId: "", // 流程实例编号
        instanceId: "", // 流程实例编号
        deployId: "",  // 流程定义编号
        taskId: "",// 流程任务编号
        procDefId: "",  // 流程编号
        multiple: false,
        comment:"", // 意见内容
      },
      // 流程图片的 Data URL
      processImageUrl: '',
    }
  },
  onLoad(options) {
    // 获取窗口高度
    this.getWindowHeight();

    if (options) {
      console.log("options", options);
      console.log("options", JSON.parse(options.url));
      const params = JSON.parse(options.url)
      this.taskName = params.taskName;
      console.log("taskName", this.taskName);
      this.startUser = params.startUser;
      this.taskForm.deployId = params.deployId;
      this.taskForm.taskId = params.taskId;
      this.taskForm.procInsId = params.procInsId;
      this.taskForm.executionId = params.executionId;
      this.processVariables(this.taskForm.taskId)
      this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);
    }
  },
  methods: {
    // 获取窗口高度
    getWindowHeight() {
      getWindowHeight().then(response => {
        this.windowHeight = response;
      })
    },
    getFlowViewer(procInsId,executionId) {
      getFlowViewer(procInsId,executionId).then(res => {
        this.taskList = res.data
      })
    },
    // 切换标签页
    switchTab(name) {
      this.activeName = name
      if (name === '3') {
        this.loadFlowData()
      }
    },
    // 加载流程图数据
    async loadFlowData() {
      try {
        // 1. 加载XML数据
        const xmlRes = await flowXmlAndNode({
          procInsId: this.taskForm.procInsId,
          deployId: this.taskForm.deployId
        });
        this.flowData = xmlRes.data;

        // 2. 加载流程图片
        const imageRes = await getDiagram4Base64(this.taskForm.procInsId);
        if (imageRes.data) {
          // 如果后端返回的是纯base64字符串，需要添加前缀
          this.processImageUrl = `data:image/png;base64,${imageRes.data}`;
        }
      } catch (error) {
        console.error('加载流程数据失败:', error);
        // 错误时清空图片URL
        this.processImageUrl = '';
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    /** 流程流转记录 */
    getFlowRecordList(procInsId, deployId) {
      const that = this
      const params = {procInsId: procInsId, deployId: deployId}
      flowRecord(params).then(res => {
        that.flowRecordList = res.data.flowList;
      }).catch(res => {
        this.goBack();
      })
    },
    /** 获取流程变量内容 */
    processVariables(taskId) {
      if (taskId) {
        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示
        getProcessVariables(taskId).then(res => {
          this.variablesData = res.data.variables;
          this.deviceUseDeptId = (res.data.deviceUseDeptId != undefined) ? res.data.deviceUseDeptId : this.deviceUseDeptId;//设备使用id
          this.param = res.data;
        });
      }
    },
  }
}
</script>
<style lang="scss">
.main-card {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;

  .card-header {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #ebeef5;

    .header-left {
      display: flex;
      align-items: center;
      gap: 20rpx;
      margin-left: 20rpx;
    }

    .close-btn {
      padding: 10rpx 30rpx;
      margin-right: 20rpx;
      font-size: 24rpx;
      color: #fff;
      background-color: #f56c6c;
      border-radius: 4rpx;
    }
  }
}

.tabs {
  .tab-header {
    display: flex;
    border-bottom: 1rpx solid #ebeef5;

    .tab-item {
      flex: 1;
      text-align: center;
      padding: 20rpx 0;
      font-size: 28rpx;
      color: #606266;
      position: relative;

      &.active {
        color: #409eff;
        font-weight: 500;

        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40%;
          height: 4rpx;
          background-color: #409eff;
        }
      }
    }
  }
}
</style>
