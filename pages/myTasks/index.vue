<template>
	<view class="container">
		<view class="nav bf">
			<view class="flex  align-center p20">
				<!-- 			<uni-search-bar type="number" class="w-full" bgColor="#fff" radius="5" placeholder="请输入资产编号" clearButton="auto"
				@confirm="search" /> -->
				<uni-easyinput prefixIcon="search" @focus="flag=true"   v-model="inputName" 
					placeholder="请输入流程名称">
				</uni-easyinput>
				<!-- <uni-icons type="scan" style="margin-left: 20rpx;" size="25" @click="scan"></uni-icons> -->
			</view>
			<view class="p20" v-if="flag">
				<view class="btn_big" @click="search">
					搜索
				</view>

			</view>
		</view>
			<view class="asset_list p20">
				<view class="p20 bf card asset_info" v-for="item in myList" :key="item.id">
					<view class="just-sbet " >
						<view class="key">
							流程编号 
						</view>	
						<view class="">
							{{item.procInsId}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							流程名称 
						</view>	
						<view class="">
							{{item.procDefName}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							提交时间 
						</view>	
						<view class="">
							{{item.createTime}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							流程状态 
						</view>	
						<view class="">
							{{repairStatus(item)}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							当前节点 
						</view>	
						<view class="">
							{{item.taskName}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							办理人 
						</view>	
						<view class="">
							{{getAssigneeName(item)}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
						</view>	
						<!-- <view class="tips center" @click="hrpRepairReport(item)" >
							报修<uni-icons type="forward" color="#fff"></uni-icons>
						</view> -->
						<view class="tips center" @click="myTastDetail" :data-index="item" >
							详情<uni-icons type="forward" color="#fff"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			<!-- 分页栏 -->
			<view class="pagination-container" v-if="total > 0">
				<view class="pagination">
					<view class="page-btn" :class="{ disabled: queryProcessParams.pageNum <= 1 }" @click="handlePrevPage">
						上一页
					</view>
					<view class="page-info">
						第 {{queryProcessParams.pageNum}} 页 / 共 {{totalPages}} 页
					</view>
					<view class="page-btn" :class="{ disabled: queryProcessParams.pageNum >= totalPages }" @click="handleNextPage">
						下一页
					</view>
				</view>
			</view>
	</view>
</template>

<script>
	import {
		myProcessList
	} from '@/api/repair';
	
	export default {
		data() {
			return {
			total:0,

			inputName: "", // 输入的id
			flag: false, // 搜索按钮是否显示
			myList: [],
				// 查询参数
			  queryProcessParams: {
				pageNum: 1,
				pageSize: 10,
				name: null,
				category: null,
				key: null,
				tenantId: null,
				deployTime: null,
				derivedFrom: null,
				derivedFromRoot: null,
				parentDeploymentId: null,
				engineVersion: null
			  },
			  loading: false
			}
		},
	
		onLoad() {
			this.myProcessList()
			
		},
		onShow() {
			this.myProcessList()
		},
		
		methods: {
            repairStatus(item){
				if(item.finishTime){
					return '已完成'
				}
				return '未完成'
			},
			getAssigneeName(item){
				if(item.assigneeName){
					return item.assigneeName
				}
				return item.assigneeDeptName
				
			},
			search() {
				
				this.queryProcessParams.pageNum=1
				this.queryProcessParams.name=this.inputName
				this.myProcessList()
			},
			myTastDetail(item){
				console.log(item)
				var row = item.currentTarget.dataset.index
				
				let params = {
					procInsId: row.procInsId,
					executionId: row.executionId,
					deployId: row.deployId,
					taskId: row.taskId,
					taskName: row.taskName,
					startUser: row.startUserName + "-" + row.startDeptName,
				}
				console.log(params, 'aaa');
				
				// uni.navigateTo({
				// 	url: `/pages/myTasks/taskWebView/taskWebView?url=${JSON.stringify(params)}`
				// })
        uni.navigateTo({
          url: `/pages/myTasks/taskWebView/myProcess?url=${JSON.stringify(params)}`
        })
			},
			async myProcessList() {
				this.loading = true;
				let res = await myProcessList(this.queryProcessParams);
				if(res.data ){
					console.log(res.data)
					this.total = res.data.total || res.data.pages * this.queryProcessParams.pageSize || 0;
					this.myList = res.data.records;
				}
				this.loading = false;
			},
			handlePrevPage() {
				if (this.queryProcessParams.pageNum > 1) {
					this.queryProcessParams.pageNum -= 1;
					this.myProcessList();
					uni.pageScrollTo({ scrollTop: 0, duration: 300 });
				}
			},
			handleNextPage() {
				if (this.queryProcessParams.pageNum < this.totalPages) {
					this.queryProcessParams.pageNum += 1;
					this.myProcessList();
					uni.pageScrollTo({ scrollTop: 0, duration: 300 });
				}
			},
		},
		computed: {
			totalPages() {
				return Math.ceil(this.total / this.queryProcessParams.pageSize) || 1;
			}
		},
	}
</script>

<style lang="scss">
	.annex {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
	}

	.nav {
		position: fixed;
		width: 100%;
		top: 0;
		z-index: 1024;
		box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
	}
	.asset_list{
	margin-top: 135rpx;
	display: grid;
	gap: 20rpx;
	.asset_info{
		display: grid;
		gap:25rpx;
		.tips{
			    color: #fff;
			    background: #3296fa;
			    padding: 5rpx 10rpx;
			    border-radius: 20rpx;
		}
			.key {
				flex-basis: 150rpx;
				margin-right: 30rpx;
			}

	}
	}
	.pagination-container {
		padding: 20rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.pagination {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 40rpx;

		.page-btn {
			background: #4095E5;
			color: #fff;
			padding: 10rpx 30rpx;
			border-radius: 30rpx;
			font-size: 28rpx;

			&.disabled {
				background: #ccc;
				opacity: 0.7;
			}
		}

		.page-info {
			font-size: 28rpx;
			color: #666;
		}
	}

	.container {
		padding-bottom: 120rpx;
	}
</style>