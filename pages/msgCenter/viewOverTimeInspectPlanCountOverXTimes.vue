<template>
  <view class="container">
    <InspectList
        :batch-id="queryParams.assistRepairBatchinfoId"
        :content-type="1"
    />
  </view>
</template>

<script>
import InspectList from '@/components/assistantDecision/InspectList.vue'
export default {
  name: "viewOverTimeInspectPlanCountOverXTimes",
  components: {
    InspectList
  },
  data() {
    return {
      queryParams: {
        assistRepairBatchinfoId: 0
      }
    }
  },
  onLoad(options) {
    console.log("options",options)
    if (options.assistRepairBatchinfoId) {
      this.queryParams.assistRepairBatchinfoId = parseInt(options.assistRepairBatchinfoId)
    }
  }
}


</script>

<style scoped lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
</style>