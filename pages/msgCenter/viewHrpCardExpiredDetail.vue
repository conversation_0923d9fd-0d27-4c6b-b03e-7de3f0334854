<template>
  <view class="container">
    <DaHuaHrpDeviceList
        :show-type="showType"
        :batch-id="queryParams.assistRepairBatchinfoId"
        :content-type="1"
    />
  </view>
</template>

<script>
import DaHuaHrpDeviceList from '@/components/assistantDecision/DaHuaHrpDeviceList.vue'

export default {
  components: {
   DaHuaHrpDeviceList
  },
  data() {
    return {
      showType: 'hrp',
      expiredDevices: [],
      queryParams: {
        assistRepairBatchinfoId: 0 // 定义 queryParam
      }
    };

  },
  onLoad(options) {
    console.log("options",options)
    if (options.assistRepairBatchinfoId) {
      this.queryParams.assistRepairBatchinfoId = parseInt(options.assistRepairBatchinfoId)
    }
    console.log(this.queryParams.assistRepairBatchinfoId)
  }
};
</script>


<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
</style>