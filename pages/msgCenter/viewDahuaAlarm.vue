<template>
  <!-- 详细信息展示 -->
  <view class="alarm-info-admin">
    <view class="alarm-info-card">
      <view class="card-container">
        <view class="card-row">
          <view class="card-col">
            <view class="inner-card">
              <view class="info-box">
                <view class="info-item">
                  <text class="info-label">报警时间：</text>
                  <text>{{ alarmInfo.alarmDate || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">设备名称：</text>
                  <text>{{ alarmInfo.channelName || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">所在组织：</text>
                  <text>{{ alarmInfo.orgName || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">报警等级：</text>
                  <text>{{ alarmInfo.alarmGradeName || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">报警状态：</text>
                  <text>{{ alarmInfo.alarmStatName || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">报警类型：</text>
                  <text>{{ alarmInfo.alarmTypeName || '-' }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { alarmDetail } from "@/api/portal/messageCenter";

export default {
  data() {
    return {
      alarmInfo: {},
      uuid: ''
    };
  },

  onLoad(options) {
    console.log('onLoad:', options);
    this.uuid = options.uuid;
    if (!this.uuid) {
      this.uuid = "alarm_0009fa1cafd14ffe909ceb1ed5ce6515_1735238983979";
    }
    this.getList();
  },

  methods: {
    /** 查询消息列表 */
    getList() {
      const data = {
        uuid: this.uuid
      };
      console.log('getList:', data);
      alarmDetail(data).then(res => {
        console.log("res", res);
        this.alarmInfo = res.data;
      }).catch(error => {
        console.error('获取报警详情失败:', error);
        uni.showToast({
          title: '获取报警详情失败',
          icon: 'none'
        });
      });
    }
  }
};
</script>

<style lang="scss">
.alarm-info-admin {
  padding: 40rpx;

  .alarm-info-card {
    max-width: 1200rpx;
    margin: 0 auto;

    .card-container {
      background-color: #fff;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      padding: 30rpx;

      .inner-card {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
        padding: 20rpx;

        .info-box {
          display: flex;
          flex-direction: column;

          .info-item {
            margin: 20rpx 0;
            display: flex;
            align-items: center;

            .info-label {
              font-weight: bold;
              margin-right: 20rpx;
              color: #333;
              min-width: 160rpx;
            }
          }
        }
      }
    }
  }
}
</style>