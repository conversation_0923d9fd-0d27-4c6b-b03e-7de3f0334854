<template>
  <view class="container">
    <DaHuaHrpDeviceList
        :show-type="showType"
        :batch-id="queryParams.assistRepairBatchinfoId"
        :content-type="0"
    />
    <RepairList
        :batch-id="queryParams.assistRepairBatchinfoId"
    />
    <!--    <view class="fixed-bottom">-->
    <!--      <button class="export-btn" @tap="handleExport">导出</button>-->
    <!--    </view>-->
  </view>
</template>

<script>
import DaHuaHrpDeviceList from '@/components/assistantDecision/DaHuaHrpDeviceList.vue'
import RepairList from '@/components/assistantDecision/RepairList.vue'

export default {
  name: "viewHrpCardRepairCountOverXTimesDetail",
  components: {
    DaHuaHrpDeviceList,
    RepairList
  },
  data() {
    return {
      showType: 'hrp',
      queryParams: {
        assistRepairBatchinfoId: 0
      },
      exportedFilePath: ''
    }
  },
  onLoad(options) {
    if (options.assistRepairBatchinfoId) {
      this.queryParams.assistRepairBatchinfoId = parseInt(options.assistRepairBatchinfoId)
      console.log(this.queryParams.assistRepairBatchinfoId)
    }
  },
  methods: {
  //   // 钉钉小程序导出方法
  //   dingTalkExport() {
  //     uni.showLoading({
  //       title: '正在导出...',
  //       mask: true
  //     });
  //
  //     const baseUrl = 'https://zhhq.gzzlyy.com:10009';
  //     const apiPath = '/portal/statistics/ads/export4MsgCenterAssistantStat/export';
  //     const url = baseUrl + apiPath;
  //
  //     const token = uni.getStorageSync('App-Token');
  //
  //     dd.httpRequest({
  //       url: url,
  //       method: 'POST',
  //       data: {
  //         assistRepairBatchinfoId: this.queryParams.assistRepairBatchinfoId
  //       },
  //       headers: {
  //         'authorization': "Bearer" + " " + token,
  //         'Content-Type': 'application/x-www-form-urlencoded'
  //       },
  //       complete: (res) => {
  //         uni.hideLoading();
  //
  //         try {
  //           if (res.status === 200 && res.data) {
  //             // 创建临时文件名
  //             const fileName = `export_${Date.now()}.xlsx`;
  //             const filePath = `${dd.env.USER_DATA_PATH}/${fileName}`;
  //
  //             // 将数据写入文件
  //             const fileManager = dd.getFileSystemManager();
  //             fileManager.writeFileSync(
  //                 filePath,
  //                 res.data,
  //                 'binary'
  //             );
  //
  //             // 显示文件保存路径
  //             dd.alert({
  //               title: '导出成功',
  //               content: `文件已保存到：${filePath}`,
  //               buttonText: '确定'
  //             });
  //
  //             // 可以将文件路径保存到data中，方便后续使用
  //             this.exportedFilePath = filePath;
  //
  //             console.log('文件保存路径：', filePath);
  //           } else {
  //             dd.alert({
  //               title: '导出失败',
  //               content: '请稍后重试',
  //               buttonText: '确定'
  //             });
  //           }
  //         } catch (error) {
  //           console.error('导出处理失败:', error);
  //           dd.alert({
  //             title: '导出失败',
  //             content: error.message || '处理文件时出错',
  //             buttonText: '确定'
  //           });
  //         }
  //       }
  //     });
  //   },
  //
  //   // 检查是否是Excel文件的辅助方法
  //   isExcelFile(arrayBuffer) {
  //     try {
  //       const header = new Uint8Array(arrayBuffer.slice(0, 4));
  //       return header[0] === 0x50 && header[1] === 0x4B; // PK文件头
  //     } catch (error) {
  //       console.error('检查文件类型失败:', error);
  //       return false;
  //     }
  //   },
  //
  //   // Android导出方法
  //   androidExport() {
  //     uni.showLoading({
  //       title: '正在导出...',
  //       mask: true
  //     });
  //
  //     const baseUrl = 'https://zhhq.gzzlyy.com:10009';
  //     const apiPath = '/portal/statistics/ads/export4MsgCenterAssistantStat/export';
  //     const url = baseUrl + apiPath;
  //
  //     const token = uni.getStorageSync('App-Token');
  //
  //     // 使用安卓的标准下载路径
  //     const downloadPath = '/storage/emulated/0/Download/';
  //     const fileName = `综合数据表_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`;
  //     const fullPath = downloadPath + fileName;
  //
  //     console.log('准备下载到路径：', fullPath);
  //
  //     const dtask = plus.downloader.createDownload(url, {
  //       method: 'POST',
  //       data: {
  //         assistRepairBatchinfoId: this.queryParams.assistRepairBatchinfoId
  //       },
  //       filename: fullPath,  // 使用完整的下载路径
  //       header: {
  //         'authorization': "Bearer" + " " + token,
  //         'Content-Type': 'application/x-www-form-urlencoded'
  //       }
  //     }, (d, status) => {
  //       uni.hideLoading();
  //
  //       console.log('下载完成状态：', status, '下载信息：', d);
  //
  //       if (status === 200) {
  //         // 通知系统扫描新文件
  //         if (plus.os.name === 'Android') {
  //           try {
  //             const main = plus.android.runtimeMainActivity();
  //             const MediaScannerConnection = plus.android.importClass('android.media.MediaScannerConnection');
  //
  //             const scannerCallback = plus.android.implements('android.media.MediaScannerConnection$OnScanCompletedListener', {
  //               onScanCompleted: function (path, uri) {
  //                 console.log('扫描完成！');
  //                 console.log('扫描路径：', path);
  //                 console.log('扫描URI：', uri);
  //               }
  //             });
  //
  //             MediaScannerConnection.scanFile(
  //                 main,
  //                 [fullPath],
  //                 ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  //                 scannerCallback
  //             );
  //           } catch (e) {
  //             console.error('媒体扫描失败：', e);
  //           }
  //         }
  //
  //         uni.showModal({
  //           title: '导出成功',
  //           content: `文件已保存到手机的下载文件夹：\n\n` +
  //               `文件名：${fileName}\n\n` +
  //               `查看路径：手机存储/Download/`,
  //           showCancel: false,
  //           success: () => {
  //             // 尝试打开文件管理器到下载目录
  //             if (plus.os.name === 'Android') {
  //               try {
  //                 const Intent = plus.android.importClass('android.content.Intent');
  //                 const File = plus.android.importClass('java.io.File');
  //                 const Uri = plus.android.importClass('android.net.Uri');  // 正确导入 Uri 类
  //
  //                 const file = new File(fullPath);
  //                 const intent = new Intent(Intent.ACTION_VIEW);
  //                 intent.setDataAndType(
  //                     Uri.fromFile(file),  // 使用正确导入的 Uri 类
  //                     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  //                 );
  //                 plus.android.runtimeMainActivity().startActivity(intent);
  //               } catch (e) {
  //                 console.error('打开文件失败：', e);
  //               }
  //             }
  //           }
  //         });
  //       } else {
  //         uni.showModal({
  //           title: '导出失败',
  //           content: '下载过程中出现错误，请重试',
  //           showCancel: false
  //         });
  //       }
  //     });
  //
  //     dtask.start();
  //
  //     // 监听下载进度
  //     dtask.addEventListener('statechanged', (task, status) => {
  //       if (task.state === 3) {
  //         const progress = Math.round((task.downloadedSize / task.totalSize) * 100);
  //         console.log('下载进度：' + progress + '%');
  //       }
  //     });
  //   },
  //
  //   // 统一的导出处理方法
  //   handleExport() {
  //     // #ifdef MP-DINGTALK
  //     this.dingTalkExport();
  //     // #endif
  //
  //     // #ifdef APP-PLUS
  //     this.androidExport();
  //     // #endif
  //   }
  }
}

</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

//.fixed-bottom {
//  position: fixed;
//  bottom: 0;
//  left: 0;
//  right: 0;
//  padding: 20rpx;
//  background-color: #fff;
//  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
//  z-index: 999;
//}
//
//.export-btn {
//  width: 100%;
//  height: 80rpx;
//  line-height: 80rpx;
//  background-color: #2979ff;
//  color: #fff;
//  border-radius: 8rpx;
//  font-size: 32rpx;
//  border: none;
//
//  &:active {
//    opacity: 0.8;
//  }
//}
</style>