<template>
  <view class="container">
    <DaHuaHrpDeviceList
        :show-type="showType"
        :batch-id="queryParams.assistRepairBatchinfoId"
        :content-type="0"
    />
    <RepairList
        :batch-id="queryParams.assistRepairBatchinfoId"
    />
  </view>
</template>

<script>
import DaHuaHrpDeviceList from '@/components/assistantDecision/DaHuaHrpDeviceList.vue'
import RepairList from '@/components/assistantDecision/RepairList.vue'

export default {
  name: "viewHrpRepairTaskTimeoutDetail",
  components: {
    DaHuaHrpDeviceList,
    RepairList
  },
  data() {
    return {
      showType: 'hrp',
      queryParams: {
        assistRepairBatchinfoId: 0
      }
    }
  },
  onLoad(options) {
    console.log("options",options)
    if (options.assistRepairBatchinfoId) {
      this.queryParams.assistRepairBatchinfoId = parseInt(options.assistRepairBatchinfoId)
    }
  }
}

</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
</style>