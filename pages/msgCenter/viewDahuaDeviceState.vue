<template>
  <!-- 详细信息展示 -->
  <view class="device-info-admin">
    <view class="device-info-card">
      <view class="cards">
        <view class="row">
          <view class="col-24">
            <view class="card">
              <view class="card-header">大华设备离线信息</view>
              <view class="info-box">
                <view class="info-item">
                  <text class="info-label">设备名称：</text>
                  <text>{{ deviceInfo.deviceName }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">渠道名称：</text>
                  <text>{{ deviceInfo.channelName }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">创建时间：</text>
                  <text>{{ deviceInfo.createTime }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">组织名称：</text>
                  <text>{{ deviceInfo.orgName }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { deviceStatusDetail } from "@/api/portal/messageCenter";
export default {
  name: "viewDahuaDeviceState",
  data() {
    return {
      deviceInfo: {
        deviceName: "null",
        channelName: "null",
        createTime: "null",
        orgName: "null",
      },
      uuid: "",
    };
  },
  onLoad(options) {
    console.log('onLoad:', options);
    this.uuid = options.uuid;
    console.log("uuid:" + this.uuid);
    if (!this.uuid) {
      this.uuid = "state_0001097e77e14fc4af6508ba390743ab_1735276458860";
    }
     this.getList();
  },

  methods: {
    /** 查询消息列表 */
    getList() {
      const data = {
        uuid: this.uuid,
      };
      console.log("getList:" + data);
      deviceStatusDetail(data).then((res) => {
        console.log("res" + res);
        this.deviceInfo = res.data;
      });
    },
  },
};
</script>

<style scoped lang="scss">.device-info-admin {
  padding: 20px;

  .device-info-card {
    max-width: 600px;
    margin: 0 auto;

    .info-box {
      display: flex;
      flex-direction: column;

      .info-item {
        margin: 10px 0;
        display: flex;

        .info-label {
          font-weight: bold;
          margin-right: 10px;
        }
      }
    }
    .card {
      background: white;
    }
    // 添加标题样式
    .card-header {
      text-align: center;
      font-size: 18px;
      font-weight: bold;
      padding: 10px;
      color: #333;
    }
  }
}
</style>>