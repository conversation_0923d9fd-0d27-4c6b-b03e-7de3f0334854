<template>
  <view class="msg-container">
    <!-- 第一层：消息类型选择器 -->
    <view class="type-selector">
      <uni-data-select
          v-model="currentTab"
          :localdata="list1"
          :clear="false"
          label="消息类型"
          placeholder="请选择消息类型"
          @change="handleTypeChange"
      />
    </view>

    <!-- 第二层：搜索栏 -->
    <view class="search-bar">
      <uni-search-bar
          v-model="searchKeyword"
          placeholder="搜索消息标题"
          @confirm="updateFilteredMessages"
          radius="5"
          cancelButton="none"
      />
    </view>

    <!-- 第三层：消息状态筛选 -->
    <view class="filter-tabs">
      <view
          v-for="(filter, index) in filters"
          :key="index"
          class="filter-tab"
          :class="{ active: currentFilter === filter.key }"
          @tap="changeFilter(filter.key)"
      >
        {{ filter.label }}
      </view>
    </view>

    <!-- 第四层：消息列表区域（可滚动） -->
    <scroll-view
        class="message-scroll"
        scroll-y
    >
      <view class="message-list">
        <checkbox-group @change="handleSelectionChange">
          <view v-if="allMessages.length > 0">
            <view
                v-for="(item, index) in allMessages"
                :key="index"
                class="message-card"
            >
              <view class="card-header">
                <view class="header-left">
                  <!-- #ifndef APP-PLUS -->
                  <checkbox
                    :value="item.id"
                    :checked="selectedMessages.includes(item.id)"
                  />
                  <!-- #endif -->
                  
                  <!-- #ifdef APP-PLUS -->
                  <view 
                    class="custom-checkbox" 
                    @tap="handleCheckboxTap(item.id)"
                  >
                    <view 
                      class="checkbox-inner" 
                      :class="{ 'checked': selectedMessages.includes(item.id) }"
                    >
                      <text v-if="selectedMessages.includes(item.id)" class="checkbox-icon">✓</text>
                    </view>
                  </view>
                  <!-- #endif -->
                </view>

                <text :class="['status-tag', item.isRead ? 'read' : 'unread']">
                  {{ item.isRead ? '已读' : '未读' }}
                </text>
              </view>

              <view class="card-content">
                <view class="title">{{ item.title }}</view>
                <!-- 添加接收者信息 -->
                <view class="receiver">接收者：{{ item.userName || '暂无' }}</view>
                <view class="content">内容：{{ truncateContent(item.content) }}</view>
              </view>

              <view class="card-footer">
                <text class="time">{{ item.createTime }}</text>
                <view class="action-buttons">
                  <button
                      class="action-btn"
                      :disabled="item.isRead === 1"
                      @tap="markAsRead(item.id)"
                  >已读</button>
                  <button
                      class="action-btn"
                      @tap="readMessage(item)"
                  >详情</button>
                  <button
                      class="action-btn warn"
                      @tap="forwardMessage(item)"
                  >转发</button>
                  <button
                      class="action-btn danger"
                      @tap="deleteRow(item.id)"
                  >删除</button>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="no-data">
            <text class="no-data-text">暂无数据</text>
          </view>
        </checkbox-group>
      </view>
    </scroll-view>

    <!-- 分页栏 -->
    <view class="pagination-container" v-if="outPageTotal > 0">
      <view class="pagination">
        <view class="page-btn" :class="{ disabled: currentPage <= 1 }" @tap="handlePrevPage">
          上一页
        </view>
        <view class="page-info">
          第 {{currentPage}} 页 / 共 {{totalPages}} 页
        </view>
        <view class="page-btn" :class="{ disabled: currentPage >= totalPages }" @tap="handleNextPage">
          下一页
        </view>
      </view>
<!--      <view class="page-jump">-->
<!--        <text>跳转到</text>-->
<!--        <input -->
<!--          type="number" -->
<!--          v-model="jumpPage" -->
<!--          class="jump-input"-->
<!--          @input="handleJumpInput"-->
<!--        />-->
<!--        <text>页</text>-->
<!--        <view class="jump-btn" @tap="handleJumpPage">确定</view>-->
<!--      </view>-->
    </view>

    <!-- 第五层：底部固定的批量操作按钮 -->
    <view class="bottom-actions">
      <button class="batch-btn primary" @tap="markSelectedAsRead">批量已读</button>
      <button class="batch-btn warn" @tap="forwardSelectedMessages">批量转发</button>
      <button class="batch-btn danger" @tap="deleteSelectedMessages">批量删除</button>
    </view>

    <!-- 转发弹窗 -->
    <uni-popup ref="forwardPopup" type="center">
      <view class="forward-dialog">
        <view class="dialog-header">
          <text class="title">转发</text>
          <text class="close" @tap="closeForwardDialog">×</text>
        </view>

        <view class="dialog-body">
          <!-- 第一层：搜索框 -->
<!--          <view class="search-section">-->
<!--            <uni-easyinput-->
<!--                v-model="searchUserKeyword"-->
<!--                placeholder="搜索用户"-->
<!--                @input="filterUsers"-->
<!--            />-->
<!--          </view>-->

          <!-- 第二层：部门选择器 -->
          <view class="dept-section">
            <view class="tree-select" @tap="toggleDeptSelect">
              <text class="selected-text">{{ selectedDeptName || '请选择部门' }}</text>
              <uni-icons type="bottom" size="16" :color="showDeptSelect ? '#2A5EFF' : '#666'"></uni-icons>
            </view>

            <!-- 下拉树状选择框 -->
            <view class="tree-dropdown" v-if="showDeptSelect">
              <scroll-view
                  scroll-y
                  class="tree-scroll"
              >
                <tree-item
                    v-for="dept in deptTreeList"
                    :key="dept.id"
                    :node="dept"
                    @clickNode="onDeptSelect"
                ></tree-item>
              </scroll-view>
            </view>
          </view>

          <!-- 第三层：用户列表 -->
          <view class="user-list-section">
            <scroll-view class="user-list" scroll-y>
              <checkbox-group @change="handleUserSelect">
                <label
                    v-for="user in filteredUserList"
                    :key="user.userId"
                    class="user-item"
                >
                  <!-- #ifndef APP-PLUS -->
                  <checkbox :value="user.userId" :checked="isUserSelected(user)" />
                  <!-- #endif -->
                  
                  <!-- #ifdef APP-PLUS -->
                  <view 
                    class="custom-checkbox" 
                    @tap="handleUserCheckboxTap(user)"
                  >
                    <view 
                      class="checkbox-inner" 
                      :class="{ 'checked': isUserSelected(user) }"
                    >
                      <text v-if="isUserSelected(user)" class="checkbox-icon">✓</text>
                    </view>
                  </view>
                  <!-- #endif -->
                  <text class="user-name">{{ user.nickName }}</text>
                </label>
              </checkbox-group>
            </scroll-view>
          </view>

          <!-- 第四层：已选用户 -->
          <view class="selected-users-section">
            <view class="section-title">已选择用户 ({{ selectUserList.length }})</view>
            <view class="selected-list">
              <view
                  v-for="user in selectUserList"
                  :key="user.userId"
                  class="selected-tag"
              >
                <text>{{ user.nickName }}</text>
                <text class="delete-icon" @tap="removeSelectedUser(user)">×</text>
              </view>
            </view>
          </view>
        </view>

        <view class="dialog-footer">
          <button class="btn primary" @tap="submitForward">确 定</button>
          <button class="btn" @tap="closeForwardDialog">取 消</button>
        </view>
      </view>
    </uni-popup>
	</view>
</template>

<script>
import { listbymark } from "@/api/messagecenter/serviceType";
import {
  MessageList,
  deleteMessageList,
  readMessageList,
  batchForwarding,
  readMessage,
  deleteMessage
} from "@/api/portal/messageCenter";
import { getDeptTree } from "@/api/commservice/comSysDept";
import { getListUser } from "@/api/commservice/comUser";
import TreeItem from '@/components/tree-item.vue'
// import { CommUtil } from "@/utils/comm";

	export default {
  components: {
    TreeItem
  },
		data() {
			return {
      isDingTalk: false, // 是否是钉钉小程序环境
      // 消息类型列表
      list1: [],
      // 当前选中的tab
      currentTab: 0,
      // 搜索和筛选
      searchKeyword: "",
      startDate: "",
      endDate: "",
      currentFilter: "unread",
      filters: [
        {key: "all", label: "全部"},
        {key: "read", label: "已读"},
        {key: "unread", label: "未读"}
      ],

      // 消息数据
      allMessages: [], // 所有消息
      filteredMessages: [], // 过滤后的消息
      paginatedMessages: [], // 当前页显示的消息
      selectedMessages: [], // 选中的消息

      // 分页
      currentPage: 1,
      pageSize: 10,
      outPageTotal: 0,
      jumpPage: 1,

      // 转发相关
      isForwardVisible: false,
      depInputValue: "",
      deptTreeList: [],
      userList: [],
      selectUserList: [],
      userTitle: "全部人员",

      // 加载状态
      loading: false,
      refreshing: false,
      selectedDeptId: null, // 当前选中的部门ID
      forwardMessages: [], // 要转发的消息列表
      searchUserKeyword: "",
      formattedDeptList: [],
      filteredUserList: [],
      showDeptSelect: false,
      selectedDeptName: '',
    };
  },

  computed: {
    totalPages() {
      return Math.ceil(this.outPageTotal / this.pageSize) || 1;
    }
  },

  created() {
    this.getmsgtypeList();
  },

  methods: {
    // 获取消息类型列表
    async getmsgtypeList() {
      try {
        const response = await listbymark({});
        console.log("消息类型列表原始数据:", response);
        if (!response || !Array.isArray(response.rows)) {
          console.error("Expected response.rows to be an array:", response);
          return;
        }

        // 将'全部消息'选项添加到数据列表里
        this.list1 = [
          { value: 0, text: '全部消息' },
          ...response.rows
            .filter(row => row.msgAlarmMark === 0)
            .map(row => ({
              value: row.messageServiceType,
              text: row.messageServiceName,
            }))
        ];

        console.log("处理后的消息类型列表:", this.list1);

        if (this.list1.length > 0) {
          // 使用第一个类型的 messageServiceType 作为初始值
          this.currentTab = this.list1[0].value;
          await this.getMessageList();
        }
      } catch (error) {
        console.error("获取消息类型失败:", error);
        uni.showToast({
          title: '获取消息类型失败',
          icon: 'none'
        });
      }
    },

    // 获取消息列表
    async getMessageList() {
      try {
        this.loading = true;
        const params = {
          messageServiceType: this.currentTab === 0 ? -1 : this.currentTab,
          readState: this.currentFilter === 'unread' ? 0 :
              this.currentFilter === 'read' ? 1 : -1,  // 未读:0, 已读:1, 全部:-1
          msgAlarmMark: 0,  // 表示这是普通消息
          startTime: this.startDate,
          endTime: this.endDate,
          title: this.searchKeyword,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        console.log('请求参数：', params);
        const response = await MessageList(params);
        console.log('响应数据：', response);

        if (response.code === 200) {
          this.allMessages = response.rows.map(msg => ({
            ...msg,
            createTime: msg.createTime || new Date().toLocaleString()
          }));
          this.outPageTotal = response.total;
          console.log('处理后的消息列表：', this.allMessages);
        } else {
          uni.showToast({
            title: '获取消息列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error("获取消息列表失败:", error);
        uni.showToast({
          title: '获取消息列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 标记已读
    async markAsRead(messageId) {
      try {

        // // 钉钉小程序环境
        const readJsonData = JSON.stringify([messageId]);
        console.log('发送的数据：', readJsonData);
        const response = await readMessageList(readJsonData);

        // console.log('其他平台发送的数据：', messageId);
        // response = await readMessage(messageId);


        if (response.code === 200) {
          uni.showToast({
            title: '已标记为已读',
            icon: 'success'
          });
          const message = this.allMessages.find(msg => msg.id === messageId);
          if (message) {
            message.isRead = 1;
          }
          // 重新获取消息列表
          await this.getMessageList();
        }
      } catch (error) {
        console.error("标记已读失败:", error);
        uni.showToast({
          title: '标记已读失败',
          icon: 'none'
        });
      }
    },

    // 删除消息
    async deleteRow(messageId) {
      try {
        const deleteJsonData = JSON.stringify([messageId]);
        console.log('发送的数据：', deleteJsonData);
        const response = await deleteMessageList(deleteJsonData);
        if (response.code === 200) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
          this.allMessages = this.allMessages.filter(msg => msg.id !== messageId);
          this.updateFilteredMessages();
        }
      } catch (error) {
        console.error("删除失败:", error);
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        });
      }
    },

    // 查看消息详情
    readMessage(row) {
        console.log("row", row,)
      if (row.appUrl) {
        uni.navigateTo({
          url: row.appUrl
        })
      } else {
        uni.showToast({
          title: '该消息暂无详细信息',
          icon: 'none',
          time: 1000
        })
      }
      // uni.navigateTo({
      //   url: '/pages/msgCenter/msg/viewHrpDivceInfo?cardNumber=00000006'
      // })
    },

    // 打开转发弹窗
    forwardMessage(row) {
      this.forwardMessages = [row];
      this.$refs.forwardPopup.open();
      this.getDeptTreeData();
    },
    // 切换消息选中状态
    toggleMessageSelection(messageId) {
      const index = this.selectedMessages.indexOf(messageId);
      if (index === -1) {
        this.selectedMessages.push(messageId);
      } else {
        this.selectedMessages.splice(index, 1);
      }
    },
    // 处理选择变化
    handleSelectionChange(e) {
      this.selectedMessages = e.detail.value;
    },
    // 批量转发
    forwardSelectedMessages() {
      if (this.selectedMessages.length === 0) {
        uni.showToast({
          title: '请先选择要转发的消息',
          icon: 'none'
        });
        return;
      }
      this.forwardMessages = this.allMessages.filter(msg =>
          this.selectedMessages.includes(msg.id)
      );
      this.$refs.forwardPopup.open();
      this.getDeptTreeData();
    },

    // 关闭转发弹窗
    closeForwardDialog() {
      this.$refs.forwardPopup.close();
      this.selectUserList = [];
      this.selectUserList = []; // 清空已选用户
      // this.searchDeptKeyword = ''; // 清空部门搜索关键词
      this.selectedDeptId = null; // 清空选中的部门ID
      this.selectedDeptName = ''; // 清空选中的部门名称
      this.showDeptSelect = false; // 关闭部门选择下拉框
      this.deptTreeList = []; // 清空部门树数据
      // this.filteredDeptList = []; // 清空过滤后的部门列表
      this.userList = []; // 清空用户列表
      this.filteredUserList = []; // 清空过滤后的用户列表
    },

    // 处理部门数据
    processDeptData(depts) {
      if (!depts) return [];

      return depts.map(dept => ({
        id: dept.id,
        label: dept.label,
        children: dept.children ? this.processDeptData(dept.children) : [],
        isOpen: false
      }));
    },

    // 获取部门树数据
    async getDeptTreeData() {
      try {
        const res = await getDeptTree();
        console.log("111111",res);
        if (res.code === 200 && res.data) {
          this.deptTreeList = this.processDeptData(res.data);
          console.log('Processed dept tree:', this.deptTreeList);
        } else {
          uni.showToast({
            title: '获取部门数据失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error("获取部门树失败:", error);
        uni.showToast({
          title: '获取部门数据失败',
          icon: 'none'
        });
      }
    },

    // 处理部门选择
    onDeptSelect(dept) {
      console.log('Selected dept:', dept);
      // 如果是叶子节点，则选中该部门
      if (!dept.children || dept.children.length === 0) {
        this.selectedDeptId = dept.id;
        this.selectedDeptName = dept.label;
        this.showDeptSelect = false;
        this.getUserList({id: dept.id});
      }
    },

    // 获取用户列表
    async getUserList(node) {
      try {
        const res = await getListUser({
          deptId: node.id,
          pageNum: 1,
          pageSize: 999
        });
        this.userList = res.rows;
        this.filteredUserList = res.rows;
      } catch (error) {
        console.error("获取用户列表失败:", error);
      }
    },

    // 处理用户选择
    handleUserSelect(e) {
      const selectedUserIds = e.detail.value;
      this.selectUserList = this.userList.filter(user =>
          selectedUserIds.includes(user.userId)
      );
    },

    // 检查用户是否已选择
    isUserSelected(user) {
      return this.selectUserList.some(selected => selected.userId === user.userId);
    },

    // 移除已选用户
    removeSelectedUser(user) {
      this.selectUserList = this.selectUserList.filter(item =>
          item.userId !== user.userId
      );
    },

    // 提交转发
    async submitForward() {
      if (!this.selectUserList.length) {
        uni.showToast({
          title: '请选择接收者',
          icon: 'none'
        });
        return;
      }

      try {
        const response = await batchForwarding({
          midList: this.forwardMessages.map(msg => msg.id),
          uidList: this.selectUserList.map(user => user.userId)
        });

        if (response.code === 200) {
          uni.showToast({
            title: '转发成功',
            icon: 'success'
          });
          this.closeForwardDialog();
        }
      } catch (error) {
        console.error("转发失败:", error);
        uni.showToast({
          title: '转发失败',
          icon: 'none'
        });
      }
    },

    handleTypeChange(value) {
      console.log("选中的消息类型值:", value);
      this.currentTab = value;
      this.currentPage = 1;
      // 重置选中的消息
      this.selectedMessages = [];
      this.getMessageList();
    },

    async loadMore() {
      if (this.loading) return; // 防止重复加载
      if (this.allMessages.length >= this.outPageTotal) {
        uni.showToast({
          title: '暂无更多数据',
          icon: 'none'
        });
        return;
      }
      this.currentPage++;
      await this.getMessageList(true); // 传入 true 表示是加载更多
    },

    onRefresh() {
      this.refreshing = true;
      this.currentPage = 1;
      // 重置选中的消息
      this.selectedMessages = [];
      this.getMessageList();
    },
    // 批量标记已读
    async markSelectedAsRead() {
      if (this.selectedMessages.length === 0) {
        uni.showToast({
          title: '请先选择要标记为已读的消息',
          icon: 'none'
        });
        return;
      }

      try {
        // 将数组转换为 JSON 字符串
        const readJsonData = JSON.stringify(this.selectedMessages);
        console.log('发送的数据：', readJsonData);

        const response = await readMessageList(readJsonData);
        if (response.code === 200) {
          uni.showToast({
            title: '标记已读成功',
            icon: 'success'
          });

          // 更新消息状态
          this.selectedMessages.forEach(id => {
            const message = this.allMessages.find(msg => msg.id === id);
            if (message) {
              message.isRead = 1;
            }
          });

          this.selectedMessages = []; // 清空选中
          
          // 重新获取消息列表
          await this.getMessageList();
        }
      } catch (error) {
        console.error("批量标记已读失败:", error);
        uni.showToast({
          title: '标记已读失败',
          icon: 'none'
        });
      }
    },

    // 批量删除
    async deleteSelectedMessages() {
      if (this.selectedMessages.length === 0) {
        uni.showToast({
          title: '请先选择要删除的消息',
          icon: 'none'
        });
        return;
      }

      try {
        const deleteJsonData = JSON.stringify(this.selectedMessages);
        console.log('发送的数据：', deleteJsonData);
        const response = await deleteMessageList(deleteJsonData);
        if (response.code === 200) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });

          // 更新消息列表
          this.allMessages = this.allMessages.filter(
              msg => !this.selectedMessages.includes(msg.id)
          );

          this.selectedMessages = []; // 清空选中
          this.updateFilteredMessages();
        }
      } catch (error) {
        console.error("批量删除失败:", error);
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        });
      }
    },

    changeFilter(key) {
      this.currentFilter = key;
      this.currentPage = 1;
      // 重置选中的消息
      this.selectedMessages = [];
      this.getMessageList();
    },

    filterUsers(value) {
      this.searchUserKeyword = value;
      this.filteredUserList = this.userList.filter(user =>
          user.userName.toLowerCase().includes(value.toLowerCase())
      );
    },

    toggleDeptSelect() {
      this.showDeptSelect = !this.showDeptSelect;
    },

    truncateContent(content) {
      if (!content) return '';
      // 如果内容超过100个字符，则截断并添加省略号
      return content.length > 100 ? content.substring(0, 100) + '...' : content;
    },

    handleCheckboxTap(id) {
      // #ifdef APP-PLUS
      // 阻止事件冒泡
      const index = this.selectedMessages.indexOf(id);
      if (index === -1) {
        this.selectedMessages.push(id);
      } else {
        this.selectedMessages.splice(index, 1);
      }
      
      // 手动触发change事件
      this.$emit('change', {
        detail: {
          value: this.selectedMessages
        }
      });
      // #endif
    },

    // 新的点击处理方法
    handleUserCheckboxTap(user) {
      // #ifdef APP-PLUS
      const selectedUsers = [...this.selectUserList];
      const index = selectedUsers.findIndex(u => u.userId === user.userId);
      
      if (index === -1) {
        selectedUsers.push(user);
      } else {
        selectedUsers.splice(index, 1);
      }
      
      // 触发原有的change事件
      this.handleUserSelect({
        detail: {
          value: selectedUsers.map(u => u.userId)
        }
      });
      // #endif
    },

    // 分页相关方法
    handlePrevPage() {
      if (this.currentPage > 1) {
        this.currentPage -= 1;
        this.getMessageList();
        uni.pageScrollTo({ scrollTop: 0, duration: 300 });
      }
    },

    handleNextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage += 1;
        this.getMessageList();
        uni.pageScrollTo({ scrollTop: 0, duration: 300 });
      }
    },

    handleJumpInput(value) {
      this.jumpPage = value;
    },

    handleJumpPage() {
      if (this.jumpPage > 0 && this.jumpPage <= this.totalPages) {
        this.currentPage = this.jumpPage;
        this.getMessageList();
      }
    },

    // 更新过滤后的消息
    updateFilteredMessages() {
      this.currentPage = 1; // 重置页码
      this.getMessageList();
    },
  }
};
</script>

<style lang="scss">
.msg-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 第一层：类型选择器 */
.type-selector {
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

/* 第二层：搜索栏 */
.search-bar {
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

/* 第三层：状态筛选 */
.filter-tabs {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;

  .filter-tab {
    flex: 1;
    text-align: center;
    padding: 15rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;

    &.active {
      color: #2979ff;
      font-weight: bold;

      &::after {
        content: '';
        position: absolute;
        bottom: -10rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #2979ff;
        border-radius: 2rpx;
      }
    }
  }
}

/* 第四层：消息列表 */
.message-scroll {
  flex: 1;
  background-color: #f5f5f5;
}

.message-list {
  // 添加底部内边距，值要大于等于底部操作按钮的高度
  padding: 20rpx 20rpx calc(240rpx + env(safe-area-inset-bottom));
}

.message-card {
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .card-header {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #eee;

    .header-left {
      display: flex;
      align-items: center;

      /* #ifdef APP-PLUS */
      .custom-checkbox {
        width: 40rpx;
        height: 40rpx;
        
        .checkbox-inner {
          width: 100%;
          height: 100%;
          border: 2rpx solid #999;
          border-radius: 4rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &.checked {
            background-color: #007AFF;
            border-color: #007AFF;
          }
          
          .checkbox-icon {
            color: #fff;
            font-size: 24rpx;
          }
        }
      }
      /* #endif */

      /* #ifndef APP-PLUS */
      checkbox {
        transform: scale(0.8);
      }
      /* #endif */
    }

    .status-tag {
      padding: 4rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;

      &.read {
        background-color: #e8f5e9;
        color: #4caf50;
      }

      &.unread {
        background-color: #fbe9e7;
        color: #f44336;
      }
    }
  }

  .card-content {
    padding: 20rpx;

    .title {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 10rpx;
    }

    .content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }

  .card-footer {
    padding: 20rpx;
    border-top: 1rpx solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .time {
      font-size: 24rpx;
      color: #999;
    }

    .action-buttons {
      display: flex;
      gap: 10rpx;
    }

    .action-btn {
      padding: 6rpx 20rpx;
      font-size: 24rpx;
      border-radius: 30rpx;
      background-color: #f5f5f5;
      border: none;

      &.danger {
        color: #f44336;
      }

      &.warn {
        color: #ff9800;
      }

      &:disabled {
        opacity: 0.5;
      }
    }
  }
}

/* 第五层：底部操作按钮 */
.bottom-actions {
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 120rpx; // 明确指定底部按钮区域的高度
  box-sizing: border-box;
  // 适配全面屏底部安全区域
  padding: 20rpx 20rpx env(safe-area-inset-bottom);

  .batch-btn {
    flex: 1;
    margin: 0 10rpx;
    font-size: 28rpx;
    padding: 20rpx 0;
    border-radius: 8rpx;

    &.primary {
      background-color: #2979ff;
      color: #fff;
    }

    &.warn {
      background-color: #ff9800;
      color: #fff;
    }

    &.danger {
      background-color: #f44336;
      color: #fff;
    }
  }
}

.loading-tip {
  text-align: center;
  padding: 20rpx;
}

.forward-dialog {
  width: 90vw;
  max-width: 600rpx;
  background: #fff;
  border-radius: 12rpx;

  .dialog-header {
    padding: 20rpx;
    border-bottom: 1rpx solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dialog-body {
    height: 800rpx;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    //.search-section {
    //  padding: 10rpx 0;
    //}

    .dept-section {
      position: relative;
      width: 100%;
      z-index: 999;

      .tree-select {
        width: 100%;
        height: 70rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx;
        background: #fff;
      }

      /* 下拉树状选择框 */
      .tree-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: #fff;
        border: 1rpx solid #ddd;
        border-radius: 4rpx;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        z-index: 1000;

        .tree-scroll {
          max-height: 400rpx;
          padding: 20rpx;
        }
      }
    }

    .user-list-section {
      flex: 1;
      min-height: 400rpx;
      border: 1rpx solid #eee;
      border-radius: 8rpx;

      .user-list {
        height: 100%;
      }

      .user-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        border-bottom: 1rpx solid #eee;

        .custom-checkbox {
          width: 40rpx;
          height: 40rpx;
          margin-right: 20rpx;
          
          .checkbox-inner {
            width: 100%;
            height: 100%;
            border: 2rpx solid #999;
            border-radius: 4rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &.checked {
              background-color: #007AFF;
              border-color: #007AFF;
            }
            
            .checkbox-icon {
              color: #fff;
              font-size: 24rpx;
            }
          }
        }
        
        .user-name {
          margin-left: 20rpx;
        }
      }
    }

    .selected-users-section {
      height: 120rpx;
      border-top: 1rpx solid #eee;
      padding-top: 10rpx;

      .section-title {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 10rpx;
      }

      .selected-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10rpx;
      }

      .selected-tag {
        display: inline-flex;
        align-items: center;
        background: #f5f5f5;
        padding: 4rpx 12rpx;
        border-radius: 30rpx;
        font-size: 24rpx;

        .delete-icon {
          margin-left: 8rpx;
          color: #999;
          padding: 0 4rpx;
        }
      }
    }
  }

  .dialog-footer {
    padding: 20rpx;
    border-top: 1rpx solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 20rpx;

    .btn {
      padding: 10rpx 30rpx;
      border-radius: 4rpx;
      font-size: 28rpx;

      &.primary {
        background: #2979ff;
        color: #fff;
      }
    }
  }
}

/* 添加树节点的样式 */
::v-deep .tree-item {
  .tree-node {
    padding: 10rpx 0;
    display: flex;
    align-items: center;
  }

  .tree-children {
    margin-left: 20rpx;
  }
}

/* APP端自定义checkbox样式 */
.custom-checkbox {
  width: 40rpx;
  height: 40rpx;
  margin: 20rpx;
  
  .checkbox-inner {
    width: 100%;
    height: 100%;
    border: 2rpx solid #999;
    border-radius: 4rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.checked {
      background-color: #007AFF;
      border-color: #007AFF;
    }
    
    .checkbox-icon {
      color: #fff;
      font-size: 24rpx;
    }
  }
}

/* 确保点击区域足够大 */
.header-left {
  padding: 10rpx;
  min-width: 80rpx;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分页栏样式 */
.pagination-container {
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 120rpx;
  left: 0;
  right: 0;
  z-index: 99;
  width: 100%;
  box-sizing: border-box;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;
  width: 100%;
  box-sizing: border-box;

  .page-btn {
    background: #2979ff;
    color: #fff;
    padding: 10rpx 30rpx;
    border-radius: 30rpx;
    font-size: 28rpx;
    min-width: 160rpx;
    text-align: center;

    &.disabled {
      background: #ccc;
      opacity: 0.7;
    }
  }

  .page-info {
    font-size: 28rpx;
    color: #666;
    flex: 1;
    text-align: center;
  }
}

.page-jump {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 0 40rpx;
  font-size: 28rpx;
  color: #666;

  .jump-input {
    width: 80rpx;
    height: 60rpx;
    text-align: center;
    background: #f5f5f5;
    border-radius: 6rpx;
    margin: 0 10rpx;
  }

  .jump-btn {
    background: #2979ff;
    color: #fff;
    padding: 6rpx 20rpx;
    border-radius: 6rpx;
    margin-left: 10rpx;
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .no-data-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
