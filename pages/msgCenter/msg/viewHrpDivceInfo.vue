<template>
  <view class="device-info-container">
    <!-- 主标题 -->
    <view class="page-title">设备档案详情</view>

    <!-- 基础信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <uni-icons type="info" size="16"></uni-icons>
        <text>基本信息</text>
      </view>
      <view class="card-content">
        <view
            v-for="(value, key) in baseInfo"
            :key="key"
            class="info-item"
        >
          <text class="info-label">{{ key | formatLabel }}：</text>
          <text class="info-value">{{ value || '-' }}</text>
        </view>
      </view>
    </view>

    <!-- 分类信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <uni-icons type="list" size="16"></uni-icons>
        <text>分类信息</text>
      </view>
      <view class="card-content">
        <view
            v-for="(value, key) in classInfo"
            :key="key"
            class="info-item"
        >
          <text class="info-label">{{ key | formatLabel }}：</text>
          <text class="info-value">{{ value || '-' }}</text>
        </view>
      </view>
    </view>

    <!-- 时间信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <uni-icons type="calendar" size="16"></uni-icons>
        <text>时间信息</text>
      </view>
      <view class="card-content">
        <view
            v-for="(value, key) in timeInfo"
            :key="key"
            class="info-item"
        >
          <text class="info-label">{{ key | formatLabel }}：</text>
          <text class="info-value">{{ value || '-' }}</text>
        </view>
      </view>
    </view>

    <!-- 其他信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <uni-icons type="more-filled" size="16"></uni-icons>
        <text>其他信息</text>
      </view>
      <view class="card-content">
        <view
            v-for="(value, key) in otherInfo"
            :key="key"
            class="info-item"
        >
          <text class="info-label">{{ key | formatLabel }}：</text>
          <text class="info-value">{{ value || '-' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCard } from "@/api/HRP/card";

export default {
  // filters 和 computed 部分保持不变
  filters: {
    formatLabel(key) {
      const labels = {
        cardId: '资产编号',
        cardName: '资产名称',
        class1Id: '一级分类',
        class1Name: '一级分类名称',
        class2Id: '二级分类',
        class2Name: '二级分类名称',
        class3Id: '三级分类',
        class3Name: '三级分类名称',
        cardSpec: '型号规格',
        unitName: '计量单位',
        deptCode: '科室编号',
        deptName: '科室名称',
        depreciation: '累计折旧',
        netValue: '账面净值',
        monthDepreciation: '月折旧额',
        depreciationStatus: '折旧状态',
        depreciationLife: '折旧年限',
        useDate: '使用日期',
        scrapDate: '报废时间',
        venderName: '供应商',
        factoryName: '厂家名称',
        remark: '备注',
        cardNumber: '卡片编号',
        monthDepreciationNum: '累计折旧月数',
        oldCardNumber: '旧资产编号',
        storagePlace: '存放地点',
        deviceRealStatus: '设备真实状态',
        expireDate: '到期日期',
        factoryNumber: '出厂编号',
        warehouse: '仓库',
        supplier: '供货人',
        supplierContact: '供货人联系方式',
        repairContact: '报修联系人',
        repairContactPhone: '报修联系电话',
        number: '数量',
        price: '金额',
        produceDate: '生产日期',
        serialNumber: '序列号',
        source: '来源'
      };
      return labels[key] || key;
    }
  },
  computed: {
    baseInfo() {
      return {
        cardId: this.divceInfo.cardId,
        cardName: this.divceInfo.cardName,
        cardNumber: this.divceInfo.cardNumber,
        cardSpec: this.divceInfo.cardSpec,
        unitName: this.divceInfo.unitName,
        deptCode: this.divceInfo.deptCode,
        deptName: this.divceInfo.deptName,
        number: this.divceInfo.number,
        price: this.divceInfo.price,
        serialNumber: this.divceInfo.serialNumber
      };
    },
    classInfo() {
      return {
        class1Id: this.divceInfo.class1Id,
        class1Name: this.divceInfo.class1Name,
        class2Id: this.divceInfo.class2Id,
        class2Name: this.divceInfo.class2Name,
        class3Id: this.divceInfo.class3Id,
        class3Name: this.divceInfo.class3Name
      };
    },
    timeInfo() {
      return {
        useDate: this.divceInfo.useDate,
        scrapDate: this.divceInfo.scrapDate,
        depreciationLife: this.divceInfo.depreciationLife,
        monthDepreciationNum: this.divceInfo.monthDepreciationNum,
        expireDate: this.divceInfo.expireDate,
        produceDate: this.divceInfo.produceDate
      };
    },
    otherInfo() {
      return {
        depreciation: this.divceInfo.depreciation,
        netValue: this.divceInfo.netValue,
        monthDepreciation: this.divceInfo.monthDepreciation,
        depreciationStatus: this.divceInfo.depreciationStatus,
        venderName: this.divceInfo.venderName,
        factoryName: this.divceInfo.factoryName,
        remark: this.divceInfo.remark,
        oldCardNumber: this.divceInfo.oldCardNumber,
        storagePlace: this.divceInfo.storagePlace,
        deviceRealStatus: this.divceInfo.deviceRealStatus,
        factoryNumber: this.divceInfo.factoryNumber,
        warehouse: this.divceInfo.warehouse,
        supplier: this.divceInfo.supplier,
        supplierContact: this.divceInfo.supplierContact,
        repairContact: this.divceInfo.repairContact,
        repairContactPhone: this.divceInfo.repairContactPhone,
        source: this.divceInfo.source
      };
    }
  },
  data() {
    return {
      cardNumber: '',
      divceInfo: {},
    }
  },
  // onLoad(options) {
  //   console.log('接收到的参数:', options);
  //   if (options.cardNumber) {
  //     this.cardNumber = options.cardNumber;
  //     this.getList(options.cardNumber);
  //   } else {
  //     uni.showToast({
  //       title: '参数错误',
  //       icon: 'none'
  //     });
  //     // 可以选择返回上一页
  //     setTimeout(() => {
  //       uni.navigateBack();
  //     }, 1500);
  //   }
  // },
  onLoad(options) {
    // 页面加载时获取数据
    this.getList();
  },
  methods: {
    getList() {
      const cardNumber = '00000006';
      getCard(cardNumber).then(res => {
        console.log("res", res);
        this.divceInfo = res.data;
      }).catch(error => {
        console.error('Error fetching card information:', error);
      });
    }
    // getList(cardNumber) {
    //   if (!cardNumber) {
    //     uni.showToast({
    //       title: '未获取到设备编号',
    //       icon: 'none'
    //     });
    //     return;
    //   }
    //
    //   getCard(cardNumber).then(res => {
    //     console.log("设备详情数据:", res);
    //     this.divceInfo = res.data;
    //   }).catch(error => {
    //     console.error('获取设备信息失败:', error);
    //     uni.showToast({
    //       title: '获取设备信息失败',
    //       icon: 'none'
    //     });
    //   });
    // }
  }
};
</script>

<style lang="scss">
.device-info-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    padding: 20rpx 0;
    color: #333;
  }

  .info-card {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #eee;
      margin-bottom: 20rpx;

      text {
        font-size: 32rpx;
        font-weight: bold;
        margin-left: 10rpx;
        color: #333;
      }
    }

    .card-content {
      .info-item {
        display: flex;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        .info-label {
          width: 200rpx;
          color: #666;
          font-size: 28rpx;
        }

        .info-value {
          flex: 1;
          color: #333;
          font-size: 28rpx;
        }
      }
    }
  }
}
</style>