<template>
	<view class="p20">
		<view class=" customer_list ">
			<view
			 v-for="(item,index) in inspectorList" :key="index">
		<view  class="customer_item bf" 	v-if="item.inspectorId==userInfo.userId||userInfo.userId==1">
			<view class="title just-sbet ">
								<view class="idCard">
									{{ getNum(item.assetId)}}
								</view>
								<view :class="item.status==0?'tips':'oldTips'">
									{{getInspectionStatus(item.status)}}
								</view>
							</view>
							<view class="customer_info just-sbet">
								<view class="company">
									地址
								</view>
								<view class="clientName">
									{{item.address}}
								</view>
							</view>
							<view class="customer_info just-sbet">
								<view class="company">
			巡检人
								</view>
								<view class="clientName">
									{{getUserName(item.inspectorId)}}
								</view>
							</view>
							<view class="customer_info just-sbet"  v-if="item.status != 0">
								<view class="company">
									盘点意见
								</view>
								<view class="clientName">
					{{item.dealOpinion}}
								</view>
							</view>
							<view class="customer_btn">
								<view class="toProperty"   v-if="item.status == 0" @click="topageAll('../../task/Inventory/property/property',item)">
									盘点资产
								</view>
							
								<view class="toWorkOrder" @click="topageAll('../../task/Inventory/workOrder/workOrder',item)">
									提交工单
								</view>
							</view>
		</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getAssetNum,
		topage
	} from '../../../common/common_methods';
	import {
		getAssetSuperInspectionInfo
	} from '../../../api/task';
	import {
		listUser,
		getUserProfile
	} from '../../../api/system/user';
	export default {
		data() {
			return {
				inspectorList:[],
				query:{
					inspectionId:''
				},
				inspectionStatusOptions: [{
						label: "未处理",
						value: 0
					},
					{
						label: "已完成",
						value: 1
					}
				],
				userList: [],
				userInfo:{}
			}
		},
	onLoad(option) {
		this.query.inspectionId =JSON.parse( option.info)
		this.getUser()
		this.getTaskList()
	},
		methods: {
			async getTaskList() {
			
				let res= await getAssetSuperInspectionInfo(this.query);
			this.inspectorList = res;
			
				
			},
			getInspectionStatus(status) {
				let str = '未知状态'
				this.inspectionStatusOptions.map(item => {
					if (item.value === status) {
						str = item.label
					}
				})
				return str
			},
			async getUser() {
				const param = {
					pageNum: 1,
					pageSize: 100,
				};
				getUserProfile().then((res)=>{
					console.log(res);
					this.userInfo = res.data
				})
				listUser(param).then((res) => {
					res.rows.map((u) => {
						const user = {
							id: u.userId,
							username: u.nickName ? u.nickName : u.userName,
						};
						this.userList.push(user);
			
					});
				});
			},
			getUserName(id) {
				let userName = "未知人员";
				this.userList.map((item) => {
					if (item.id === Number(id)) {
						userName = item.username;
					}
				});
				return userName;
			},
			getNum(id) {
				return getAssetNum(id)
			},
			topageAll(url, item) {
				topage(url, {
					id: item.id,
					assetId: item.assetId
				})
			},
		}
	}
</script>

<style lang="scss">
	.customer_list {

		.customer_item {
			box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
			padding: 30rpx;
			border-radius: 10rpx;
			margin-bottom: 20rpx;

			.title {
				margin: 10rpx 0;

				.idCard {
					color: #000;
				}

				.tips {
					border-radius: 20rpx;
					padding: 5rpx 20rpx;
					background-color: rgba(249, 0, 0, 0.8);
					color: #fff;
					font-weight: bold;

				}

				.oldTips {
					border-radius: 20rpx;
					font-weight: bold;
					padding: 5rpx 20rpx;
					background-color: rgba(91, 115, 249, 0.1);
					color: #5b73f9;
				}
			}

			.customer_info {
				border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
				padding-bottom: 20rpx;
				margin-top: 40rpx;
				color: #868686;

				.company {
					width: 140rpx;
				}

				.clientName {
					flex: 1;
					color: #333;
					font-weight: bold;
				}
			}

			.customer_btn {
				margin-top: 45rpx;
				// padding: 20rpx;
				// text-align: center;
				// background-color: #5b73f9;
				// color: #fff;
				// border-radius: 10rpx;
				display: grid;
				gap: 20rpx;
				grid-template-columns: repeat(2, 1fr);

				.toProperty {
					padding: 20rpx;
					text-align: center;
					background-color: #5b73f9;
					color: #fff;
					border-radius: 10rpx;
				}

				.toWorkOrder {
					padding: 20rpx;
					text-align: center;
					background-color: #5b73f9;
					color: #fff;
					border-radius: 10rpx;

				}
			}

		}
	}
</style>
