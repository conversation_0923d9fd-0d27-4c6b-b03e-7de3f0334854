<template>
	<view class="p20">
		<view class="task_list">
			<view class="task_item p20" v-for="(item,index) in taskList" :key="index">
				<view class="flex task_top  bf">

					<uni-icons type="calendar-filled" color="#5b73f9" size="40"></uni-icons>
					<view class="task_name">
						{{item.name}}
					</view>
				</view>
				<view class="just-sbet">
					<view class="">
						任务创建人： {{ getUserName(item.createUserId) }}
					</view>
					<view class="center inventory_to " @click="toTaskFastInfo(item)">
						<text>前往盘点</text>
						<uni-icons type="right"></uni-icons>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getAssetSuperInspectionList
	} from '../../api/task';
	import {
		topage
	} from '../../common/common_methods';
	import {
		listUser
	} from '../../api/system/user';
	export default {
		data() {
			return {
				taskList: [],
				query: {
					pageNum: 1,
					pageSize: 99999,
				},
				userList: []
			};
		},
		onLoad() {
			this.getUser()
			this.getTaskList()
		},
		methods: {
			async getTaskList() {

				let {
					list
				} = await getAssetSuperInspectionList(this.query);
				this.taskList = list;


			},
			async getUser() {
				const param = {
					pageNum: 1,
					pageSize: 100,
				};
				listUser(param).then((res) => {
					res.rows.map((u) => {
						const user = {
							id: u.userId,
							username: u.nickName ? u.nickName : u.userName,
						};
						this.userList.push(user);

					});
				});
			},
			getUserName(id) {
				let userName = "未知人员";
				this.userList.map((item) => {
					if (item.id === Number(id)) {
						userName = item.username;
					}
				});
				return userName;
			},
			toTaskFastInfo(item) {
				topage('./taskFastInfo/taskFastInfo', item.id)
			},
		}
	}
</script>

<style lang="scss">
	.task_list {
		// margin-top: 100rpx;
		width: 100%;
		height: 100%;

		.task_item {
			box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
			width: 100%;
			margin-bottom: 20rpx;
			border-radius: 10rpx;
			background-color: #fff;

			.task_name {
				align-self: center;
				font-size: 30rpx;
				font-weight: bold;
				color: #272727;
				align-self: center;
			}

			.inventory_to {
				border-radius: 10rpx;
				border: 2rpx solid #ccc;
				padding: 5rpx 10rpx;
			}
		}
	}
</style>