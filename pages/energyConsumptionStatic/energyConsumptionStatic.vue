<template>
	<view class="energy-consumption">
		<view class="data-card" v-for="type in energyTypes" :key="type.value">
			<view-energy :energy-type="type.value" :dept-id="deptId" />
		</view>
	</view>
</template>

<script>
import ViewEnergy from '@/components/MyFormComponents/viewEnergy.vue'
import { getInfo} from '@/api/login'
export default {
	components: {
		ViewEnergy
	},
	data() {
		return {
			deptId: 101, // 或者你自己的id
			energyTypes: [
				{ label: '用电', value: 1 },
				{ label: '用水', value: 2 },
				{ label: '用气', value: 3 }
			]
		}
	},
	created() {
    this.getDeptId();
  },
  methods:{
    getDeptId(){
      getInfo().then(res => {
        this.deptId = res.user.deptId;
      })
    }
  }
}
</script>

<style lang="scss">
.energy-consumption {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}
.data-card {
	background: #fff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	padding: 20rpx;
}
</style>
