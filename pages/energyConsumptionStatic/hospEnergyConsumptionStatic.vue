<template>
  <view class="energy-consumption">
    <!-- 部门选择区域 -->
    <view class="dept-section">
      <view class="tree-select" @tap="toggleDeptSelect">
        <text class="selected-text">{{ selectedDeptName || '请选择部门' }}</text>
        <uni-icons type="bottom" size="16" :color="showDeptSelect ? '#2A5EFF' : '#666'"></uni-icons>
      </view>

      <!-- 下拉树状选择框 -->
      <view class="tree-dropdown" v-if="showDeptSelect">
        <scroll-view scroll-y class="tree-scroll">
          <tree-item
            v-for="dept in deptTreeList"
            :key="dept.id"
            :node="dept"
            @clickNode="onDeptSelect"
          />
        </scroll-view>
      </view>
    </view>

    <view class="data-card" v-for="type in energyTypes" :key="type.value">
      <view-energy :energy-type="type.value" :dept-id="deptId" />
    </view>
  </view>
</template>

<script>
import ViewEnergy from '@/components/MyFormComponents/viewEnergy.vue'
import TreeItem from '@/components/tree-item.vue'
import { getDeptTree } from '@/api/commservice/comSysDept'

export default {
  components: {
    ViewEnergy,
    TreeItem
  },
  data() {
    return {
      deptId: -1, // 默认部门ID
      deptTreeList: [], // 部门树数据
      selectedDeptName: '', // 选中的部门名称
      showDeptSelect: false, // 是否显示部门选择框
      energyTypes: [
        { label: '用电', value: 1 },
        { label: '用水', value: 2 },
        { label: '用气', value: 3 }
      ]
    }
  },
  watch: {
    deptId: {
      handler(newVal) {
        console.log('当前选中的部门ID:', newVal)
      },
      immediate: true
    }
  },
  onLoad() {
    // if (options.deptId) {
    //   this.deptId = Number(options.deptId)
    // }
    // 获取部门树数据
    this.loadDepartmentTree()
  },
  methods: {
    // 加载部门树
    async loadDepartmentTree() {
      try {
        const res = await getDeptTree()
        if (res.code === 200 && res.data) {
          this.deptTreeList = this.processDeptData(res.data)
          console.log('部门树数据:', this.deptTreeList)
        } else {
          console.error('获取部门树数据失败:', res)
          uni.showToast({
            title: '获取部门树数据失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载部门树失败:', error)
        uni.showToast({
          title: '加载部门树失败',
          icon: 'none'
        })
      }
    },

    // 处理部门数据
    processDeptData(depts) {
      if (!depts) return []
      return depts.map(dept => ({
        id: dept.id,
        label: dept.label,
        children: dept.children ? this.processDeptData(dept.children) : [],
        isOpen: false
      }))
    },

    // 切换部门选择框的显示状态
    toggleDeptSelect() {
      this.showDeptSelect = !this.showDeptSelect
    },

    // 处理部门选择
    onDeptSelect(node) {
      console.log('选择部门:', node)
      this.deptId = node.id
      this.selectedDeptName = node.label
      this.showDeptSelect = false
    }
  }
}
</script>

<style lang="scss">
.energy-consumption {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.dept-section {
  position: relative;
  width: 100%;
  z-index: 999;
  margin-bottom: 20rpx;

  .tree-select {
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    background: #fff;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
  }

  .tree-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #fff;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;

    .tree-scroll {
      max-height: 400rpx;
      padding: 20rpx;
    }
  }
}

.data-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  padding: 20rpx;
}
</style>
