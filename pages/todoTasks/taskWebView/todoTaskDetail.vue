<template>
  <view class="app-container">

    <!-- 审批弹窗 -->
    <uni-popup ref="completePopup"  type="center">
      <view class="popup-content">
        <view class="popup-title">{{completeTitle}}</view>
        <uni-forms ref="taskForm" :model="taskForm" :rules="rules">
          <uni-forms-item v-if="checkSendUser || checkSendRole">
            <flow-user v-if="checkSendUser"
                       :check-type="checkType"
                       @handleUserSelect="handleUserSelect"/>
            <flow-role v-if="checkSendRole"
                       @handleRoleSelect="handleRoleSelect"/>
          </uni-forms-item>
          <uni-forms-item label="处理意见" required>
            <uni-easyinput
                type="textarea"
                v-model="taskForm.comment"
                placeholder="请输入处理意见"/>
          </uni-forms-item>
        </uni-forms>

        <view class="quick-tags">
          <view class="tag"
                v-for="tag in quickTags"
                :key="tag"
                @tap="selectInfo(tag)">
            {{tag}}
          </view>
        </view>

        <view class="btn-group">
          <button class="btn cancel" @click="close">取消</button>
          <button class="btn primary" @tap="taskComplete">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 主要内容卡片 -->
    <view class="main-card">
      <view class="card-header">
        <view class="header-left">
          <text class="icon-document">待办报修</text>
          <text class="tag">发起人: {{startUser}}</text>
          <text class="tag">任务节点: {{taskName}}</text>
        </view>
        <button  class="close-btn" @tap="goBack">关闭</button>
      </view>

      <!-- 标签页 -->
      <view class="tabs">
        <view class="tab-header">
          <view
              v-for="(tab, index) in tabs"
              :key="index"
              :class="['tab-item', activeName === tab.name ? 'active' : '']"
              @tap="switchTab(tab.name)"
          >
            {{tab.label}}
          </view>
        </view>

        <view class="tab-content">
          <!-- 表单信息 -->
          <view v-show="activeName === '1'"
                class="tab-pane"
                :style="{height: windowHeight ? 'calc('+ windowHeight + ' - 10rpx - 10rpx - 44px - 5px)' : ''}">
            <view class="form-container">
              <dynamic-form
                  :form-config="variablesData"
                  :initial-form-data="initialFormData"
                  :process-variables="param"
                  @submit="submitForm"
              />
              <view class="action-buttons fixed-bottom">
                <button v-if="!formKeyExist"
                        class="btn success"
                        @tap="handleComplete">审批</button>
                <button class="btn warning" @tap="handleReturn">退回</button>
                <button class="btn danger" @tap="handleReject">驳回</button>
              </view>
            </view>
          </view>

          <!-- 流转记录 -->
          <view v-show="activeName === '2'" class="tab-pane"
                :style="{height: windowHeight ? 'calc('+ windowHeight + ' - 10rpx - 10rpx - 44px - 5px)' : ''}">
            <flow-record :flowRecordList="flowRecordList"></flow-record>
          </view>

          <!-- 流程图 -->
          <view v-if="activeName === '3'"
                class="tab-pane"
                :style="{height: windowHeight ? 'calc('+ windowHeight + ' - 10rpx - 10rpx - 44px - 5px)' : ''}">
            <!--            <flow :flow-data="flowData"/>-->
            <py-bpmn-viewer :flowData="flowData" :imageUrl="processImageUrl"></py-bpmn-viewer>
          </view>
        </view>
      </view>
    </view>

    <!-- 退回流程弹窗 -->
    <uni-popup ref="returnPopup" type="center">
      <view class="popup-content">
        <view class="popup-title">{{returnTitle}}</view>
        <uni-forms ref="returnForm" :model="taskForm" :rules="returnRules">
          <uni-forms-item label="退回节点" required>
            <uni-data-checkbox
                v-model="taskForm.targetKey"
                :localdata="returnTaskList"
                :map="{text:'name',value:'id'}"
            />
            <view class="selected-node">
              {{ taskForm.targetKey }}
            </view>
          </uni-forms-item>
          <uni-forms-item label="退回意见" required>
            <uni-easyinput
                type="textarea"
                v-model="taskForm.comment"
                placeholder="请输入意见"
            />
          </uni-forms-item>
        </uni-forms>
        <view class="btn-group">
          <button class="btn cancel" @click="returnClose">取消</button>
          <button class="btn primary" @tap="taskReturn">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 驳回流程弹窗 -->
    <uni-popup ref="rejectPopup" type="center">
      <view class="popup-content">
        <view class="popup-title">{{rejectTitle}}</view>
        <uni-forms ref="rejectForm" :model="taskForm" :rules="rejectRules">
          <uni-forms-item label="驳回意见" required>
            <uni-easyinput
                type="textarea"
                v-model="taskForm.comment"
                placeholder="请输入意见"
            />
          </uni-forms-item>
        </uni-forms>
        <view class="btn-group">
          <button class="btn cancel" @tap="rejectClose">取消</button>
          <button class="btn primary" @tap="taskReject">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
import {flowRecord,getDiagram4Base64} from "@/api/flowable/finished";
//import Parser from '@/components/parser/Parser'
import {getProcessVariables, readXml, getFlowViewer, getHighlight, flowXmlAndNode} from "@/api/flowable/definition";
//import flow from '@/views/flowable/task/myProcess/detail/flow'
//import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {
  complete,
  rejectTask,
  returnList,
  returnTask,
  getNextFlowNode,
  flowTaskForm,
} from "@/api/flowable/todo";
import PyBpmnViewer from '@/components/py/py-bpmn-viewer/py-bpmn-viewer.vue';
import FlowUser from '@/components/flow/User/flow-user.vue';
import FlowRole from '@/components/flow/Role/flow-role.vue';
import FlowRecord from '@/components/flow/record/flow-record.vue';
import DynamicForm from '@/components/parser-nuoyi-form/DynamicForm.vue'
import {
  getWindowHeight,
  getVarType,
  getFormData
} from "@/utils/comUtil";

//import flow from '@/components/flow/Flow'

export default {
  components: {
    PyBpmnViewer,
    // Parser,
    //  flow,
    FlowUser,
    FlowRole,
    FlowRecord,
    DynamicForm
  },

  data() {
    return {
      windowHeight: undefined,
      // 快捷标签
      quickTags: ['已核实', '同意', '好的', '收到', 'OK', '通过'],
      // 标签页配置
      tabs: [
        { name: '1', label: '表单信息' },
        { name: '2', label: '流转记录' },
        { name: '3', label: '流程图' }
      ],
      // 提交退回任务rules
      returnRules: {
        targetKey: {
          rules: [{ required: true, errorMessage: '请选择退回节点' }]
        },
        comment: {
          rules: [{ required: true, errorMessage: '请输入退回意见' }]
        }
      },
      rejectRules:{
        comment: {
          rules: [{ required: true, errorMessage: '请输入驳回意见' }]
        }
      },
      deviceUseDeptId: 0,
      param: {},
      // 模型xml数据
      xmlData: "",
      flowData: {},
      activeName: '1',
      // 部门名称
      deptName: undefined,
      // 部门树选项
      // 用户表格数据
      userList: null,
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 查询参数
      queryParams: {
        deptId: undefined
      },
      // 遮罩层
      loading: true,
      flowRecordList: [], // 流程流转数据
      formConfCopy: {},
      src: null,
      rules: {}, // 表单校验
      variablesForm: {}, // 流程变量数据
      taskForm: {
        returnTaskShow: false, // 是否展示回退表单
        delegateTaskShow: false, // 是否展示回退表单
        defaultTaskShow: true, // 默认处理
        comment: "", // 意见内容
        procInsId: "", // 流程实例编号
        instanceId: "", // 流程实例编号
        deployId: "",  // 流程定义编号
        taskId: "",// 流程任务编号
        procDefId: "",  // 流程编号
        targetKey: "",
        variables: {
          variables: {}
        },
      },
      assignee: null,
      formConf: {}, // 默认表单数据
      variables: [], // 流程变量数据
      variablesData: {}, // 流程变量数据
      initialFormData:{},
      returnTaskList: [],  // 回退列表数据
      completeTitle: null,
      completeOpen: false,
      returnTitle: null,
      returnOpen: false,
      rejectOpen: false,
      rejectTitle: null,
      userData: [],
      checkSendUser: false, // 是否展示人员选择模块
      checkSendRole: false,// 是否展示角色选择模块
      checkType: 'single', // 选择类型
      taskName: null, // 任务节点
      startUser: null, // 发起人信息,
      multiInstanceVars: '', // 会签节点
      formKeyExist: false, // 当前节点是否存在表单
      // 流程图片的 Data URL
      processImageUrl: '',
    }
  },

  onLoad(options) {

    // 获取窗口高度
    this.getWindowHeight();

    if (options) {
      console.log("options",options);
      console.log("options",JSON.parse(options.url));
      const params = JSON.parse(options.url)
      this.taskName = params.taskName;
      console.log("taskName",this.taskName);
      this.startUser = params.startUser;
      this.taskForm.deployId = params.deployId;
      this.taskForm.taskId = params.taskId;
      this.taskForm.procInsId = params.procInsId;
      this.taskForm.executionId = params.executionId;
      this.taskForm.instanceId = params.procInsId;

      // this.taskForm.procInsId = '0bca75a6-f40b-11ef-a4a7-00ff1f9cda0f'
      // this.taskForm.executionId = '0bcb1248-f40b-11ef-a4a7-00ff1f9cda0f'
      // this.taskForm.deployId = '570046'
      // this.taskForm.taskId = '755167'




      if (this.taskForm.taskId) {
        this.processVariables(this.taskForm.taskId)
        this.getFlowTaskForm(this.taskForm.taskId)
      }
      this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);
    }else {

      if (this.taskForm.taskId) {
        this.processVariables(this.taskForm.taskId)
        this.getFlowTaskForm(this.taskForm.taskId)
      }
      this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);
    }
  },

  methods: {
    // 获取窗口高度
    getWindowHeight() {
      getWindowHeight().then(response => {
        this.windowHeight = response;
      })
    },
    // 选择快捷标签
    selectInfo(tag) {
      this.taskForm.comment = tag
    },
    // selectInfo(e){
    //   console.log(e.target.innerText)
    //   this.taskForm.comment=e.target.innerText
    // },
    // 切换标签页
    switchTab(name) {
      this.activeName = name
      if (name === '3') {
        this.loadFlowData()
      }
    },

    // 加载流程图数据
    async loadFlowData() {
      try {
        // 1. 加载XML数据
        const xmlRes = await flowXmlAndNode({
          procInsId: this.taskForm.procInsId,
          deployId: this.taskForm.deployId
        });
        this.flowData = xmlRes.data;

        // 2. 加载流程图片
        const imageRes = await getDiagram4Base64(this.taskForm.procInsId);
        if (imageRes.data) {
          // 如果后端返回的是纯base64字符串，需要添加前缀
          this.processImageUrl = `data:image/png;base64,${imageRes.data}`;
        }
      } catch (error) {
        console.error('加载流程数据失败:', error);
        // 错误时清空图片URL
        this.processImageUrl = '';
      }
    },
    // 取消审批流程弹窗
    close() {
      this.$refs.completePopup.close()
    },
    // 设置图标
    setIcon(val) {
      return {
        'icon-check': !!val,
        'success': !!val,
        'icon-time': !val,
        'waiting': !val
      };
    },
    // setIcon(val) {
    //   if (val) {
    //     return "el-icon-check";
    //   } else {
    //     return "el-icon-time";
    //   }
    // },
    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      });
      // //uni.navigateBack()
      //
      //
      //
      // this.checkSendRole = true;
      // //this.checkType = "single";
      // this.completeOpen = true;
      // this.completeTitle = "流程审批";

    },
    // /** 返回页面 */
    // goBack() {
    //   // 关闭当前标签页并返回上个页面
    //   const obj = { path: "/task/todo", query: { t: Date.now()} };
    //   this.$tab.closeOpenPage(obj);
    // },
    // 显示错误提示
    showError(message) {
      uni.showToast({
        title: message,
        icon: 'none'
      })
    },
    // 显示成功提示
    showSuccess(message) {
      uni.showToast({
        title: message,
        icon: 'success'
      })
    },


    handleClick(tab, event) {
      if (tab.name === '3') {
        flowXmlAndNode({procInsId: this.taskForm.procInsId, deployId: this.taskForm.deployId}).then(res => {
          this.flowData = res.data;
        })
      }
    },

    setColor(val) {
      if (val) {
        return "#2bc418";
      } else {
        return "#b3bdbb";
      }
    },
    // 用户信息选中数据
    handleUserSelect(selection) {
      if (selection) {
        if (selection instanceof Array) {
          const selectVal = selection.map(item => item.userId);
          if (this.multiInstanceVars) {
            this.$set(this.taskForm.variables, this.multiInstanceVars, selectVal);
          } else {
            this.$set(this.taskForm.variables, "approval", selectVal.join(','));
          }
        } else {
          this.$set(this.taskForm.variables, "approval", selection.userId.toString());
        }
      }
      // 确保 taskForm.comment 不为 null 或 undefined
      if (!this.taskForm.comment) {
        this.taskForm.comment = '';
      }
    },
    // 角色信息选中数据
    handleRoleSelect(selection) {
      if (selection) {
        if (selection instanceof Array) {
          const selectVal = selection.map(item => item.roleId);
          this.$set(this.taskForm.variables, "approval", selectVal.join(','));
        } else {
          this.$set(this.taskForm.variables, "approval", selection);
        }
      }
    },
    /** 流程流转记录 */
    getFlowRecordList(procInsId, deployId) {
      const that = this
      const params = {procInsId: procInsId, deployId: deployId}
      flowRecord(params).then(res => {
        that.flowRecordList = res.data.flowList;
      }).catch(res => {
        this.goBack();
      })
    },
    /** 获取流程变量内容 */
    processVariables(taskId) {
      if (taskId) {
        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示
        getProcessVariables(taskId).then(res => {
          // this.variablesData = res.data.variables;
          this.deviceUseDeptId=(res.data.deviceUseDeptId != undefined)?res.data.deviceUseDeptId:this.deviceUseDeptId;//设备使用id
          this.param=res.data;
        });
      }
    },
    /** 流程节点表单 */
    getFlowTaskForm(taskId) {
      if (taskId) {
        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示
        flowTaskForm({taskId: taskId}).then(res => {
          this.variablesData = res.data.formData;
          //this.$set(this, 'variablesData', res.data.formData);
          this.taskForm.variables = res.data.formData;
          this.formKeyExist = res.data.formKeyExist;
        });
      }
    },
    /** 加载审批任务弹框 */
    handleComplete() {
      // this.completeOpen = true;
      // this.completeTitle = "流程审批";
      this.submitForm(null);
    },
    /** 用户审批任务 */
    taskComplete() {
      if (!this.taskForm.variables && this.checkSendUser) {
        this.$modal.msgError("请选择流程接收人员!");
        return;
      }
      if (!this.taskForm.variables && this.checkSendRole) {
        this.$modal.msgError("请选择流程接收角色组!");
        return;
      }
      if (!this.taskForm.comment) {
        this.$modal.msgError("请输入审批意见!");
        return;
      }
      if (this.taskForm && this.formKeyExist) {
        // 表单是否禁用
        this.taskForm.formData.formData.disabled = true;
        // 是否显示按钮
        this.taskForm.formData.formData.formBtns = false;
        this.taskForm.variables = Object.assign({}, this.taskForm.variables, this.taskForm.formData.valData);
        this.taskForm.variables.variables = this.taskForm.formData.formData;
		console.log("this.taskForm",this.taskForm)
        complete(this.taskForm).then(response => {
          this.$modal.msgSuccess(response.msg);
          this.$refs.completePopup.close();
		  uni.navigateBack({
			  delta:1,
		  })
        });
      } else {
        // 流程设计人员类型配置为固定人员接收任务时,直接提交任务到下一步
		    console.log("this.taskForm1",this.taskForm)
        complete(this.taskForm).then(response => {
          this.$modal.msgSuccess(response.msg);
          this.$refs.completePopup.close();
          uni.navigateBack({
            delta:1,
          })
        });
      }
    },
    /** 委派任务 */
    handleDelegate() {
      this.taskForm.delegateTaskShow = true;
      this.taskForm.defaultTaskShow = false;
    },
    handleAssign() {

    },

    /** 驳回任务 */
    handleReject() {
      this.$refs.rejectPopup.open()
      this.rejectTitle = "驳回流程";
    },
    /** 取消驳回任务 */
    rejectClose(){
      this.$refs.rejectPopup.close();
    },
    /** 提交驳回任务 */
    // taskReject() {
    //   this.$refs["rejectForm"].validate(valid => {
    //     if (valid) {
    //       rejectTask(this.taskForm).then(res => {
    //         this.$modal.msgSuccess(res.msg);
    //         this.goBack();
    //       });
    //     }
    //   });
    // },
    async taskReject() {
        const valid = await this.$refs.rejectForm.validate();
        if (valid) {
          const res = await rejectTask(this.taskForm);
          this.$modal.msgSuccess(res.msg);
          uni.showToast({
            title: res.msg,
            // duration: 1000,
            icon: 'success',
          })
          this.$refs.rejectPopup.close();
          uni.navigateBack({ delta: 1 });
        }
    },
    /** 可退回任务列表 */
    handleReturn() {
      console.log("退回按钮被触发");
      this.$refs.returnPopup.open();
      this.returnTitle = "退回流程";
      returnList(this.taskForm).then(res => {
        console.log("回退列表数据",res);
        this.returnTaskList = res.data;
        this.taskForm.variables = null;
      })
    },
    /** 取消退回任务 */
    returnClose(){
      this.$refs.returnPopup.close();
    },
    /** 提交退回任务 */
    async taskReturn() {
        const valid = await this.$refs.returnForm.validate();
        if (valid) {
          const res = await returnTask(this.taskForm);
          this.$modal.msgSuccess(res.msg);
          uni.showToast({
            title: res.msg,
            // duration: 1000,
            icon: 'success',
          })
          this.$refs.returnPopup.close();
          uni.navigateBack({ delta: 1 });
        }
    },

    /** 委派任务 */
    submitDeleteTask() {
      this.$refs["taskForm"].validate(valid => {
        if (valid) {
          delegate(this.taskForm).then(response => {
            this.$modal.msgSuccess(response.msg);
            this.goBack();
          });
        }
      });
    },
    // /** 取消回退任务按钮 */
    // cancelDelegateTask() {
    //   this.taskForm.delegateTaskShow = false;
    //   this.taskForm.defaultTaskShow = true;
    //   this.returnTaskList = [];
    // },
    /** 申请流程表单数据提交 */
    submitForm(formData) {
      if(formData){
        let value = formData.formData
        value.formModel=formData.valData
        this.variablesData = value

      }


      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况
      const params = {taskId: this.taskForm.taskId}
      getNextFlowNode(params).then(res => {
        const data = res.data;
        this.taskForm.formData = formData;
        if (data) {
          if (data.dataType === 'dynamic') {
            if (data.type === 'assignee') { // 指定人员
              this.checkSendUser = true;
              this.checkType = "single";
            } else if (data.type === 'candidateUsers') {  // 候选人员(多个)
              this.checkSendUser = true;
              this.checkType = "multiple";
            } else if (data.type === 'candidateGroups') { // 指定组(所属角色接收任务)
              this.checkSendRole = true;
            } else { // 会签
              // 流程设计指定的 elementVariable 作为会签人员列表
              let priceDeviceDeptUserId = 0;
              let priceDeviceUseDeptUserId = 0;
              let priceFinanceDeptUserId = 0;
              let priceAuditDeptUserId = 0;
              let bExist = false;

              //当前表单里面是否存在选择科室人员，把当前选择的科室人员作为 会签
              if (formData != undefined){
                if (formData.valData != undefined){

                  if (formData.valData.priceDeviceDeptUserId != undefined ){
                    if (formData.valData.priceDeviceDeptUserId > 0){
                      priceDeviceDeptUserId = formData.valData.priceDeviceDeptUserId;
                      bExist = true;
                    }
                  }

                  if (formData.valData.priceDeviceUseDeptUserId != undefined ){
                    if (formData.valData.priceDeviceUseDeptUserId > 0){
                      priceDeviceUseDeptUserId = formData.valData.priceDeviceUseDeptUserId;
                      bExist = true;
                    }
                  }

                  if (formData.valData.priceFinanceDeptUserId != undefined ){
                    if (formData.valData.priceFinanceDeptUserId > 0){
                      priceFinanceDeptUserId = formData.valData.priceFinanceDeptUserId;
                      bExist = true;
                    }
                  }

                  if (formData.valData.priceAuditDeptUserId != undefined ){
                    if (formData.valData.priceAuditDeptUserId > 0){
                      priceAuditDeptUserId = formData.valData.priceAuditDeptUserId;
                      bExist = true;
                    }
                  }
                }
              }
              //当前表单里面不存在选择科室人员，在当前工作流变量里科室人员作为会签
              if (bExist == false){
                if (this.param != undefined){

                  if (this.param.priceDeviceDeptUserId != undefined ){
                    if (this.param.priceDeviceDeptUserId > 0){
                      priceDeviceDeptUserId = this.param.priceDeviceDeptUserId;
                      bExist = true;
                    }
                  }

                  if (this.param.priceDeviceUseDeptUserId != undefined ){
                    if (this.param.priceDeviceUseDeptUserId > 0){
                      priceDeviceUseDeptUserId = this.param.priceDeviceUseDeptUserId;
                      bExist = true;
                    }
                  }

                  if (this.param.priceFinanceDeptUserId != undefined ){
                    if (this.param.priceFinanceDeptUserId > 0){
                      priceFinanceDeptUserId = this.param.priceFinanceDeptUserId;
                      bExist = true;
                    }
                  }

                  if (this.param.priceAuditDeptUserId != undefined ){
                    if (this.param.priceAuditDeptUserId > 0){
                      priceAuditDeptUserId = this.param.priceAuditDeptUserId;
                      bExist = true;
                    }
                  }
                }
              }

              // 存在选择科室人员，把科室人员作为 会签 节点人员
              if (bExist == true){
                let aryUsers = [];

                if (priceDeviceDeptUserId > 0){
                  aryUsers.push(priceDeviceDeptUserId);
                }
                if (priceDeviceUseDeptUserId > 0){
                  aryUsers.push(priceDeviceUseDeptUserId);
                }
                if (priceFinanceDeptUserId > 0){
                  aryUsers.push(priceFinanceDeptUserId);
                }
                if (priceAuditDeptUserId > 0){
                  aryUsers.push(priceAuditDeptUserId);
                }

                this.multiInstanceVars = data.vars;
                this.checkSendUser = false;
                this.$set(this.taskForm.variables, this.multiInstanceVars, aryUsers);
              }else {
                // 不存在选择科室人员，选择人员作为会签人员
                this.multiInstanceVars = data.vars;
                this.checkSendUser = true;
                this.checkType = "multiple";
              }

            }
          }
        }
        this.taskForm.comment = 'ok'
        this.$refs.completePopup.open();
        this.completeOpen = true;
        this.completeTitle = "流程审批";
      })
    },
  }
}
</script>
<style lang="scss">
.app-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20rpx;
}

.popup-content {
  width: 700rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.popup-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.quick-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin: 20rpx 0;

  .tag {
    background-color: #67c23a;
    color: #fff;
    padding: 10rpx 20rpx;
    border-radius: 6rpx;
    font-size: 24rpx;
  }
}

.main-card {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;

  .card-header {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #ebeef5;

    .header-left {
      display: flex;
      align-items: center;
      gap: 20rpx;
    }

    .tag {
      background-color: #ecf5ff;
      color: #409eff;
      padding: 4rpx 16rpx;
      border-radius: 4rpx;
      font-size: 24rpx;
    }

    .close-btn {
      padding: 10rpx 30rpx;
      font-size: 24rpx;
      color: #fff;
      background-color: #f56c6c;
      border-radius: 4rpx;
    }
  }
}

.tabs {
  .tab-header {
    display: flex;
    border-bottom: 1rpx solid #ebeef5;

    .tab-item {
      flex: 1;
      text-align: center;
      padding: 20rpx 0;
      font-size: 28rpx;
      color: #606266;
      position: relative;

      &.active {
        color: #409eff;
        font-weight: 500;

        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40%;
          height: 4rpx;
          background-color: #409eff;
        }
      }
    }
  }
}

.btn-group {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;

  .btn {
    width: 45%;
    font-size: 28rpx;
    padding: 16rpx 0;
    border-radius: 8rpx;

    &.cancel {
      background-color: #f4f4f5;
      color: #909399;
    }

    &.primary {
      background-color: #409eff;
      color: #fff;
    }

    &.success {
      background-color: #67c23a;
      color: #fff;
    }

    &.warning {
      background-color: #e6a23c;
      color: #fff;
    }

    &.danger {
      background-color: #f56c6c;
      color: #fff;
    }
  }
}
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 30rpx;

  .btn {
    min-width: 160rpx;
    font-size: 28rpx;
    padding: 16rpx 30rpx;
    border-radius: 8rpx;

    &.success {
      background-color: #67C23A;
      color: #fff;
    }

    &.warning {
      background-color: #E6A23C;
      color: #fff;
    }

    &.danger {
      background-color: #F56C6C;
      color: #fff;
    }
  }
}
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  display: flex;
  justify-content: space-around;
  
  .btn {
    margin: 0 10rpx;
    flex: 1;
  }
}
</style>