<template>
	<view class="container">
		<view class="nav bf">
			<view class="flex  align-center p20">
				<uni-easyinput prefixIcon="search" @focus="flag=true" v-model="queryParams.name" 
					placeholder="请输入流程名称">
				</uni-easyinput>
			</view>
			<view class="p20" v-if="flag">
				<view class="btn_big" @click="search">
					搜索
				</view>
			</view>
		</view>
		<view class="asset_list p20">
			<view class="p20 bf card asset_info" v-for="item in todoList" :key="item.id">
				<view class="just-sbet">
					<view class="key">
						任务编号
					</view>
					<view class="">
						{{item.taskId}}
					</view>
				</view>
				<view class="just-sbet">
					<view class="key">
						流程名称
					</view>
					<view class="">
						{{item.procDefName}}
					</view>
				</view>
				<view class="just-sbet">
					<view class="key">
						当前节点
					</view>
					<view class="">
						{{item.taskName}}
					</view>
				</view>
				<view class="just-sbet">
					<view class="key">
						流程版本
					</view>
					<view class="">
						v{{item.procDefVersion}}
					</view>
				</view>
				<view class="just-sbet">
					<view class="key">
						流程发起人
					</view>
					<view class="">
						{{item.startUserName}} - {{item.startDeptName}}
					</view>
				</view>
        <view class="just-sbet">
          <view class="key">
            接收时间
          </view>
          <view class="">
            {{item.createTime}}
          </view>
				</view>
				<view class="just-sbet">
					<view class="key">
					</view>
					<view class="tips center" @click="toHandle" :data-index="item">
						去处理<uni-icons type="forward" color="#fff"></uni-icons>
					</view>
				</view>
			</view>
		</view>
		<!-- 分页栏 -->
		<view class="pagination-container" v-if="total > 0">
			<view class="pagination">
				<view class="page-btn" :class="{ disabled: queryParams.pageNum <= 1 }" @click="handlePrevPage">
					上一页
				</view>
				<view class="page-info">
					第 {{queryParams.pageNum}} 页 / 共 {{totalPages}} 页
				</view>
				<view class="page-btn" :class="{ disabled: queryParams.pageNum >= totalPages }" @click="handleNextPage">
					下一页
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		todoList,
		complete,
		returnList,
		returnTask,
		rejectTask,
		getDeployment,
		delDeployment,
		exportDeployment
	} from "@/api/flowable/todo";
	export default {
		data() {
			return {
				// 流程待办任务表格数据
				todoList: [],
				flag: false, // 搜索按钮是否显示
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					name: null,
					category: null
				},
				// 总条数
				total: 0,
				loading: false
			};
		},
		onLoad() {
			this.getList();
		},
		onShow(){
			this.getList();
		},
		methods: {
			search() {
				this.queryParams.pageNum = 1;
				this.getList();
			},
			/** 查询流程定义列表 */
			getList() {
				this.loading = true;
				todoList(this.queryParams).then(response => {
					this.todoList = response.data.records;
					this.total = response.data.total;
					this.loading = false;
				});
			},
			handlePrevPage() {
				if (this.queryParams.pageNum > 1) {
					this.queryParams.pageNum -= 1;
					this.getList();
					uni.pageScrollTo({ scrollTop: 0, duration: 300 });
				}
			},
			handleNextPage() {
				if (this.queryParams.pageNum < this.totalPages) {
					this.queryParams.pageNum += 1;
					this.getList();
					uni.pageScrollTo({ scrollTop: 0, duration: 300 });
				}
			},
			toHandle(item) {
				var row = item.currentTarget.dataset.index
				let params = {
					procInsId: row.procInsId,
					executionId: row.executionId,
					deployId: row.deployId,
					taskId: row.taskId,
					taskName: row.taskName,
					startUser: row.startUserName + "-" + row.startDeptName,
				}
				console.log(params, 'aaa');

				// uni.navigateTo({
				// 	url: `/pages/todoTasks/taskWebView/taskWebView?url=${JSON.stringify(params)}`
				// })
        uni.navigateTo({
          url: `/pages/todoTasks/taskWebView/todoTaskDetail?url=${JSON.stringify(params)}`
        })
			}
		},
		computed: {
			totalPages() {
				return Math.ceil(this.total / this.queryParams.pageSize) || 1;
			}
		}
	}
</script>

<style lang="scss">
	.annex {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
	}

	.nav {
		position: fixed;
		width: 100%;
		top: 0;
		z-index: 1024;
		box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
	}
	.asset_list{
		margin-top: 135rpx;
		display: grid;
		gap: 20rpx;
		.asset_info{
			display: grid;
			gap:25rpx;
			.tips{
				color: #fff;
				background: #3296fa;
				padding: 5rpx 10rpx;
				border-radius: 20rpx;
			}
			.key {
				flex-basis: 150rpx;
				margin-right: 30rpx;
			}
		}
	}
	.pagination-container {
		padding: 20rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.pagination {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 40rpx;

		.page-btn {
			background: #4095E5;
			color: #fff;
			padding: 10rpx 30rpx;
			border-radius: 30rpx;
			font-size: 28rpx;

			&.disabled {
				background: #ccc;
				opacity: 0.7;
			}
		}

		.page-info {
			font-size: 28rpx;
			color: #666;
		}
	}

	.container {
		padding-bottom: 120rpx;
	}
</style>