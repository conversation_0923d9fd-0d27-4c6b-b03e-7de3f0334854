<template>
	<view>
		
	</view>
</template>

<script>
	import {
		listHrpRepairRecord
	} from '@/api/repair';
	export default {
		data() {
			return {
				hrpInfo: {
					pageNum: 1,
					pageSize: 10,
					cardNumber: null,
					cardName: null
				}
				
			}
		},
		
		onShow() {
			this.scan()
		},
		methods: {
			scan() {
				
			uni.scanCode({
				scanType: ['barCode', 'qrCode'],
				success: async (res) => {
					console.log(res)
					let arr = res.result(',')
					console.log(arr)
					arr=arr[0].split(':')
					console.log(arr)
					this.hrpInfo.cardNumber=arr[1]
					
				    this.getHrpRepairList()
				}
			})
			},
			hrpRepairReport(item){
				var objStr = JSON.stringify(item)
				uni.navigateTo({
					url: '/pages/assetDetails/assetDetails?repairInfo='+encodeURIComponent(objStr)
				})
			},
			async getHrpRepairList() {
				let res = await listHrpRepairRecord(this.hrpInfo);
				if(res ){
					console.log(res.rows)
					this.hrpRepairReport(res.rows[0])
				}
			
			},
			
		}
	}
</script>

<style>

</style>
