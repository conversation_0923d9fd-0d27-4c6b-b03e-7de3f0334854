<template>
	<view class="p20"> 
		<view class=" customer_list grid gap">
				<view class="customer_item bf " v-for="(item,index) in contractList" :key="index" >
					<view class="title just-sbet ">
						<view class="idCard">
							{{ item.contractNumber}}

						</view>
						<view class="tips">
							{{item.period==null?'':item.period}}
						</view>
					</view>
					<view class="customer_info just-sbet">
						<view class="company">
							客户姓名
						</view>
						<view class="clientName">
							{{item.clientName}}
						</view>
					</view>
					<view class="customer_info just-sbet">
						<view class="company">
							联系方式
						</view>
						<view class="clientName" @click.stop="makePhoneCall(item.phoneNumber)">
							{{item.phoneNumber}}
						</view>
					</view>
					<view class="customer_btn" @click="customerBtnClick(item)">
						了解详情
					</view>
				</view>
			</view>
	</view>
</template>

<script>
	import {
		contractInfoEnding
	} from '../../api/contract';
import { getAssetNum, topage } from '../../common/common_methods';
	export default {
		data() {
			return {
				contractList: []
			};
		},
		onLoad() {
			this.getContractList()
			console.log(1);
		},
		methods: {
			customerBtnClick(item) {
				topage('./contractInfo/contractInfo', item)
			},
			async getContractList() {
				let res = await contractInfoEnding()
				console.log(res);
				this.contractList = res
			},
			
			makePhoneCall(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber: phoneNumber,
					success: (res) => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.log('拨打电话失败');
					}
				})
			},
			getNum(id) {
				return getAssetNum(id)
			}
		}
	}
</script>

<style lang="scss">
	.customer_list {

			.customer_item {
				box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
				padding: 30rpx;
				border-radius: 10rpx;


				.title {
					margin: 10rpx 0;

					.idCard {
						color: #000;
					}

					.tips {
						border-radius: 20rpx;
						padding: 5rpx 20rpx;
						background-color: rgba(91, 115, 249, 0.1);
						color: #5b73f9;
					}
				}

				.customer_info {
					border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
					padding-bottom: 20rpx;
					margin-top: 40rpx;
					color: #868686;

					.company {}

					.clientName {
						color: #333;
						font-weight: bold;
					}
				}

				.customer_btn {
					margin-top: 45rpx;
					padding: 20rpx;
					text-align: center;
					background-color: #5b73f9;
					color: #fff;
					border-radius: 10rpx;
				}
			}
		}
</style>