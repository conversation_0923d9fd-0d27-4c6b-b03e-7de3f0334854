<template>
	<view class="p20">

		<view class="customer_info p20 bf card">
			<view class="customer_item just-sbet bf">
				<view class="content">
					资源
				</view>
				<view class="key">
					{{getNum(customerInfo.resourceId)}}
				</view>
			</view>
			<view class="customer_item just-sbet bf" v-for="(item,key,index) in customerInfoKey" :key="key">
				<view class="content" v-if="customerInfoKey[key]!=undefined || customerInfoKey[key]!=null ">
					{{item}}:
				</view>
				<view class="key" @click="makePhoneCall(key)"
					v-if="customerInfo[key] != null && customerInfo[key]!=undefined ">
					{{customerInfo[key]}}
				</view>

			</view>
		</view>
	</view>
</template>

<script>
	import {
		getAssetNum
	} from '../../../common/common_methods';
	export default {
		data() {
			return {
				customerInfo: {},
				customerInfoKey: {
					"contractNumber": "合同编号",
					"clientName": "客户姓名",
					"phoneNumber": "联系方式",
					// "idCard": null,
					// "resourceId": "资源",
					// "resourceAddress": "韶山东路34号02号门面",
					// "landlord": "",
					"status": "状态",
					"rentalStartDate": "出租开始时间",
					"rentalEndDate": "出租结束时间",
					"usage": "租赁用途",
					"period": "缴费周期",
					// "rentfee": null,
					"deposit": "押金",
					"roomRentFee": "房屋面积",
					"storefrontRentFee": "商铺月租金（元/平米）",
					"storefrontSquare": "商铺面积",
					// "roomSquare": 0,
					// "createUserId": null,
					// "createTime": "2023-08-08 15:09:46",
					// "updateUserId": 1,
					// "updateTime": "2023-10-05 02:18:43",
					// "checkInInfo": "",
					// "checkOutInfo": null,
					"achiveNumber": "档案号",
					// "totalFee": 23986,
					"paidFee": null
				}
			};
		},
		onLoad(option) {
			this.customerInfo = JSON.parse(option.info)
			console.log(this.customerInfo);
		},
		methods: {
			makePhoneCall(key) {
				if (key != 'phoneNumber') return
				uni.makePhoneCall({
					phoneNumber: this.customerInfo.phoneNumber,
					success: (res) => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.log('拨打电话失败');
					}
				})
			},
			getNum(id) {
				return getAssetNum(id)
			}

		}
	}
</script>

<style lang="scss">
	.customer_info {
		// display: grid;
		// gap: 45rpx;

		.customer_item {
			padding: 30rpx 0;

			.content {
				flex-basis: 130rpx;
				margin-right: 30rpx;
			}

			.key {
				flex: 1;
				text-align: right;

			}
		}


	}
</style>