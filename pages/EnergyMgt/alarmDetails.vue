<template>
	<view>
		<uni-section title="报警详情" type="line">
			<uni-card>
				<uni-list>
					<uni-list-item title="报警时间" :rightText="item.alarmDate"></uni-list-item>
					<uni-list-item title="设备名称" :rightText="item.alarmPosition"></uni-list-item>
					<uni-list-item title="所属组织" :rightText="item.orgName"></uni-list-item>
					<uni-list-item title="报警级别" v-if="item.alarmGrade===1" rightText="严重"></uni-list-item>
					<uni-list-item title="报警级别" v-if="item.alarmGrade===2" rightText="一般"></uni-list-item>
					<uni-list-item title="报警级别" v-if="item.alarmGrade===3" rightText="轻微"></uni-list-item>
					<uni-list-item title="报警状态" v-if="item.alarmStat===1" rightText="正在报警"></uni-list-item>
					<uni-list-item title="报警状态" v-if="item.alarmStat===2" rightText="报警消失"></uni-list-item>
					<uni-list-item title="报警类型" :rightText="item.alarmTypeName"></uni-list-item>

				</uni-list>
			</uni-card>
		</uni-section>
		<uni-section title="处理结果" type="line">
			<uni-data-checkbox v-model="item.handleStat" :localdata="disposalResults"
				@change="change"></uni-data-checkbox>
			<textarea v-model="params.handleMessage" placeholder="请输入处理内容" class="custom-textarea"></textarea>

			<view style="">
				<button @click="submit" type="primary"
					style="width: 80%; margin: auto; margin-top: 20rpx; margin-bottom: 20rpx; border-radius: 50rpx;">提交</button>

			</view>
		</uni-section>




	</view>
</template>

<script>
	import {

		electricWarnHandle
	} from '@/api/energy.js'

	export default {
		data() {
			return {
				item: {},
				disposalResults: [{
						text: '未处理',
						value: 0
					}, {
						text: '处理中',
						value: 1
					}, {
						text: '已处理',
						value: 2
					},
					{
						text: '误报',
						value: 3
					},
					{
						text: '忽略',
						value: 4
					}
				],
				params: {
					alarmDate: '',
					alarmCode: '',
					handleStat: 0,
					handleMessage: '',
					dbType: 0


				}


			}
		},
		onLoad(e) {
			this.item = JSON.parse(e.item)
			this.params.alarmDate = this.item.alarmDate
			this.params.alarmCode = this.item.alarmCode

		},
		methods: {
			change(e) {
				console.log(e);
				this.params.handleStat = e.value
			},

			async submit() {
				try {
					if (this.params.handleMessage === '') {
						uni.showToast({
							title: '请输入处理内容',
							icon: 'none'
						})
						return
					}
					console.log(this.params);
					//const res = await electricWarnHandle(this.params)

				} catch (e) {
					//TODO handle the exception
				}

			}
		}
	}
</script>

<style>
	.custom-textarea {
		border: 1px solid #ccc;
		padding: 10px;
		height: 100px;
		width: 90%;
		margin-top: 20rpx;
	}
</style>