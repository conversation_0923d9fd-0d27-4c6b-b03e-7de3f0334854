<template>
	<view style=" 	background-image: linear-gradient(45deg, #3b83ef, #eaeaec);height: 96vh;">
		<swiper :autoplay="true" :interval="3000" :duration="1000" circular style="height: 360rpx;">
			<swiper-item>
				<image style="width: 100%;height:100%;" src="../../static/images/banner/banner03.jpg"
					mode="scaleToFill">
				</image>
			</swiper-item>
			<swiper-item>
				<image style="width: 100%;height:100%;" src="../../static/images/banner/banner03.jpg"
					mode="scaleToFill">
				</image>
			</swiper-item>
		</swiper>
		<view class="grid">
			<view @click="go(1)">
				<view class="workIcon workIcon-anquanjiance"></view>
				<view class="text-md padding-top-sm">实时检测</view>
			</view>
			<view @click="go(2)">
				<view class="workIcon workIcon-diandeng"></view>
				<view class="text-md padding-top-sm">电能历史</view>
			</view>
			<view @click="go(3)">
				<view class="workIcon workIcon-baojingzhenggai"></view>
				<view class="text-md padding-top-sm">用电报警</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "energyMgt",


		data() {
			return {



			};
		},
		methods: {
			go(id) {
				if (id === 1) {
					uni.navigateTo({
						url: '/pages/EnergyMgt/realtimeDetect'
					})
				} else if (id === 2) {
					uni.navigateTo({
						url: '/pages/EnergyMgt/electricityHistory'
					})
				} else {
					uni.navigateTo({
						url: '/pages/EnergyMgt/electricityAlarm'
					})
				}
			}


		}
	};
</script>

<style lang="scss" scoped>
	@import url('../../static/workServerIcon/workServerIcon.css');


	.grid {
		display: flex;
		justify-content: space-around;
		/* 水平居中 */
		align-items: center;
		/* 垂直居中 */
		flex-wrap: wrap;
		/* 如果需要换行 */
		margin-top: 120rpx;
		font-weight: bold;

	}

	.grid>view {
		display: flex;

		flex-direction: column;
		align-items: center;
		/* 子元素内部垂直居中 */
		justify-content: center;
		/* 子元素内部水平居中 */
		margin: 5rpx;
		/* 子元素之间的间距 */
	}
</style>