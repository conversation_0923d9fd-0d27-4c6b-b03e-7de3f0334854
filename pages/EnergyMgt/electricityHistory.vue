<template>
	<view style="font-weight:bold;">
		<view
			style="display: flex; justify-content: space-around;font-size: 30rpx;  background-color: #fff;color: #8c8c8c; line-height: 30rpx; padding: 30rpx;">
			<view @click="toggle('top')">
				<text>选择部门</text>
				<uni-icons type="bottom" size="16" color="#8c8c8c"></uni-icons>
			</view>
			<view>
				<uni-datetime-picker type="date" v-model="dateValue" @change="maskClick">
					<!-- 插槽内容，可以放置自定义的视图 -->
					<view class="custom-picker">
						<text>选择日期</text>
						<uni-icons type="bottom" size="16" color="#8c8c8c"></uni-icons>
					</view>
				</uni-datetime-picker>
			</view>

		</view>
		<uni-section :title="value?value:'请选择部门'" style="font-size: 116px;margin: 20rpx 10rpx;"
			type="line"></uni-section>
		<uni-popup ref="popup" background-color="#fff">
			<view class="img">
				<view style="width: 100%;height: 60vh; border-radius: 30rpx;">
					<view style="display: flex; justify-content: space-between; margin: 20rpx 10rpx;">
						<text style="font-size: 16px;">选择部门</text>
						<uni-icons type='closeempty' @click="close" size="18"></uni-icons>
					</view>

					<view style="padding: 10px;">
						<scroll-view style="height: 400px;" scroll-y="true">
							<TreeItem v-for="(item,index) in treeData" :key="item.id" :node="item"
								@clickNode="clickNode">
							</TreeItem>
						</scroll-view>
					</view>
				</view>
			</view>

		</uni-popup>

		<view style="margin-bottom: 100rpx;">
			<view class="card">
				<view>电压</view>
				<qiun-data-charts type="area" :chartData="voltageOption" :opts="opts"
					:ontouch="true"></qiun-data-charts>
			</view>

			<view class="card">
				<view>电流图表</view>
				<qiun-data-charts type="area" :chartData="currentOption" :opts="opts"
					:ontouch="true"></qiun-data-charts>
			</view>

			<view class="card">
				<view>功率图表</view>
				<qiun-data-charts type="area" :chartData="powerOption" :opts="opts" :ontouch="true"></qiun-data-charts>
			</view>

			<view class="card">
				<view>温度表</view>
				<qiun-data-charts type="area" :chartData="tempOption" :opts="opts" :ontouch="true"></qiun-data-charts>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getDeptTree,
		getElectricHistory
	} from '../../api/energy.js';
	import TreeItem from '../../components/tree-item.vue'

	import * as echarts from 'echarts';
	export default {
		name: 'electricityHistory',
		components: {
			TreeItem
		},

		data() {
			return {
				treeData: [],
				value: '',
				depId: 103,
				// 时间选择值
				dateValue: '2024-09-11',
				voltageOption: {},
				currentOption: {},
				powerOption: {},
				tempOption: {},
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],

					enableScroll: true,
					ontouch: true,
					legend: {
						show: true,
						position: 'top',
					},
					xAxis: {
						disableGrid: true,
						itemCount: 4,
						scrollShow: true,
						boundaryGap: false,
					},
					yAxis: {
						gridType: "dash",
						dashLength: 2
					},
					extra: {
						type: "curve",
						opacity: 0.2,
						addLine: true,
						width: 2,
						gradient: true,
						activeType: "hollow"
					}

				},
				loading: true,
				error: false,
				myChart: null


			};
		},
		created() {
			this.getUserTree()
		},
		onReady() {
			if (this.depId === null) {
				uni.showToast({
					title: '请选择部门',
					icon: 'none',
					duration: 2000
				})
			} else {
				this.initChart()
			}


		},
		methods: {
			async getUserTree() {
				const res = await getDeptTree();
				const data = this.addParentId(res.data, null); // 设置初始状态
				this.treeData = data
			},
			addParentId(nodes, parentId) {
				for (let i = 0; i < nodes.length; i++) {
					const node = nodes[i];
					if (node.children && Array.isArray(node.children)) {
						this.addParentId(node.children, node.id); // 递归处理子节点
					}
					node.parentId = parentId; // 设置当前节点的 parentId
					node.isOpen = false


				}
				return nodes
			},
			// 初始化树形结构数据
			initializeTreeData(data) {
				data.forEach(node => {
					node.isOpen = false
					if (node.children) {
						this.initializeTreeData(node.children); // 递归设置子节点的 isOpen 状态
					}
				});

				return data;
			},
			toggle(type) {
				this.type = type
				// open 方法传入参数 等同在 uni-popup 组件上绑定 type属性
				this.$refs.popup.open(type)
			},
			close() {
				this.$refs.popup.close('bottom')
			},

			clickNode(item) {
				this.$emit('clickNode', item)
				this.value = item.label
				this.depId = item.id
				this.$refs.popup.close('bottom')
				if (this.dateValue === '') {
					uni.showToast({
						title: '请选择时间',
						icon: 'none',
						duration: 2000
					})
				} else {
					this.initChart()
				}

			},

			initChart() {
				this.initVoltageChart()
			},

			async initVoltageChart() {
				try {
					const res = await getElectricHistory(this.depId, this.dateValue);
					console.log(res);
					this.voltageOption.categories = res.data.timeList;
					this.voltageOption.series = []
					this.voltageOption.series.push({
						name: 'A相',
						data: res.data.aVoltageList
					}, {
						name: 'B相',
						data: res.data.bVoltageList
					}, {
						name: 'C相',
						data: res.data.cVoltageList
					})

					this.voltageOption = JSON.parse(JSON.stringify(this.voltageOption))
					this.currentOption.categories = res.data.timeList
					this.currentOption.series = []
					this.currentOption.series.push({
						name: 'A相',
						data: res.data.aElectricityList
					}, {
						name: 'B相',
						data: res.data.bElectricityList
					}, {
						name: 'C相',
						data: res.data.cElectricityList
					})
					this.currentOption = JSON.parse(JSON.stringify(this.currentOption))

					this.powerOption.categories = res.data.timeList
					this.powerOption.series = []
					this.powerOption.series.push({
						name: 'A相',
						data: res.data.aPowerList
					}, {
						name: 'B相',
						data: res.data.bPowerList
					}, {
						name: 'C相',
						data: res.data.cPowerList
					})
					this.powerOption = JSON.parse(JSON.stringify(this.powerOption))

					this.tempOption.categories = res.data.timeList
					this.tempOption.series = []
					this.tempOption.series.push({
						name: 'A相',
						data: res.data.aTemperatureList
					}, {
						name: 'B相',
						data: res.data.bTemperatureList
					}, {
						name: 'C相',
						data: res.data.cTemperatureList
					})
					this.tempOption = JSON.parse(JSON.stringify(this.tempOption))


					this.error = true;
					this.loading = false;




				} catch (e) {
					this.error = true;
					this.loading = false;
					console.log(e);
				} finally {
					this.loading = false;
				}

			},
			maskClick(e) {
				console.log('maskClick事件:', this.dateValue);
				this.initChart()
			}
		},
	}
</script>

<style lang="scss">
	.card {
		width: 95%;
		height: 600rpx;
		box-shadow: 5px 5px 10px 2px rgba(0, 0, 0, 0.3);
		border-radius: 10px;
		background-color: #fff;
		margin: auto;
		margin-top: 30rpx;

		margin-bottom: 20rpx;

	}

	.img {
		width: 100%;
		height: 100%;
		background-image: url('../../static/images/banner/nav2.jpg');
		filter: opacity(80%);
		background-size: cover;
		background-position: center;
		border-radius: 30rpx;
	}
</style>