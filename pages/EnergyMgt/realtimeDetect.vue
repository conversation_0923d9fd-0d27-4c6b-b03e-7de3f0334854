<template>
	<div style="height: 95vh; font-weight: bold;">
		<div
			style="display: flex; justify-content: space-around;font-size: 30rpx;  background-color: #fff; padding: 30rpx; color: #8c8c8c; line-height: 20rpx; margin-top: 15rpx;">
			<div @click="toggle('top')">
				<text>选择部门</text>
				<uni-icons type="bottom" size="16" color="#8c8c8c"></uni-icons>
			</div>
			<div @click="toggle1('top')"><text>选择状态</text>
				<uni-icons type="bottom" size="16" color="#8c8c8c"></uni-icons>
			</div>

		</div>
		<uni-section :title="value?value:'请选择部门'" style="font-size: 116px;margin: 20rpx 10rpx;"
			type="line"></uni-section>
		<uni-popup ref="popup" background-color="#fff">
			<view class="img">
				<view style="width: 100%;height: 60vh; border-radius: 30rpx;">
					<view style="display: flex; justify-content: space-between; margin: 20rpx 10rpx;">
						<text style="font-size: 16px; padding-top: 10rpx;">选择部门</text>
						<uni-icons type='closeempty' @click="close()" size="18"></uni-icons>
					</view>
					<view style="padding: 10px;">
						<scroll-view style="height: 400px;" scroll-y="true">
							<TreeItem v-for="(item,index) in treeData" :key="item.id" :node="item"
								@clickNode="clickNode">
							</TreeItem>
						</scroll-view>
					</view>
				</view>

			</view>

		</uni-popup>

		<uni-popup ref="popup2" background-color="#fff">
			<view class="img">
				<view style="width: 100%;height: 30vh; border-radius: 30rpx;">
					<view style="display: flex; justify-content: space-between; margin: 20rpx 10rpx;">
						<text style="font-size: 16px;">选择状态</text>
						<uni-icons type='closeempty' @click="close2()" size="18"></uni-icons>
					</view>

					<view>
						<uni-data-checkbox v-model="selectValue" :localdata="range" @change="selectionStatus"
							mode="button"></uni-data-checkbox>
					</view>
				</view>
			</view>

		</uni-popup>

		<view class="" v-if="serviceList.length===0">
			<view style="display: flex; justify-content: center; align-items: center; height: 70vh">当前没有数据,请更换部门</view>
		</view>
		<view v-else>
			<view>
				<uni-card v-for="(item ,index) in serviceList" :key="item.id " style="margin-top: 10rpx;" class="card">
					<template v-slot:title>
						<view style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
							<view
								style="margin-top:5rpx;width: 40%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
								{{item.name}}
							</view>
							<view style="margin-top:15rpx; ">
								<button size="mini" type="default" @click="open(item)">合闸</button>
								<span style="margin-right: 10rpx;"></span>
								<button size="mini" type="default" @click="Closing(item)">分闸</button>
							</view>
						</view>
					</template>

					<view>{{item.ownerName}}</view>
					<view style="margin-top: 10px" v-if="item.state === 0 ">
						<text>状态 :</text>
						<text style="padding-left: 10rpx; color: #214283; font-weight: bold;">在线</text>
					</view>
					<view style="margin-top: 20px" v-if="item.state === 1 ">
						<text>状态 :</text>
						<text style="padding-left: 10rpx; font-weight: bold;">离线</text>
					</view>
					<view style="margin-top: 20px" v-if="item.state === 2">
						<text>状态 :</text>
						<text style="padding-left: 10rpx; font-weight: bold; color: red;">异常</text>
					</view>
					<view style="margin-top: 10px">{{item.time}}</view>

					<view class="table-bottom">
						<view>零线</view>
						<view>{{item.zeroTemperature}}°C</view>
						<view>-</view>
						<view>-</view>
						<view>-</view>
						<view>A项</view>
						<view>{{item.atemperature}}°C</view>
						<view>{{item.avoltage}}V</view>
						<view>{{item.aelectricCurrent}}A</view>
						<view>{{item.apower}}KW</view>
					</view>


				</uni-card>
			</view>
			<view style="margin: 10rpx; padding: 50rpx;">
				<uni-pagination :total="total?total:0" title="标题文字" prev-text="前一页" next-text="后一页"
					@change="changePageNum" />
			</view>
		</view>

		</view>

	</div>
</template>

<script>
	import {
		getDeptTree,
		getDahuaOrganizationLinkById
	} from '../../api/energy.js';
	import TreeItem from '../../components/tree-item.vue'

	export default {
		name: "energyMgt",
		components: {
			TreeItem
		},

		data() {
			return {
				dept: '',
				treeData: [],
				type: 'center',
				value: '',
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					deptId: undefined
				},
				// 设备参数
				serviceList: [],
				select: false,
				selectValue: 0,
				range: [{
					"value": 0,
					"text": "全部"
				}, {
					"value": 1,
					"text": "在线"
				}, {
					"value": 2,
					"text": "离线"
				}, {
					"value": 3,
					"text": "异常"
				}],
				total: 0


			};
		},
		methods: {
			async getUserTree() {
				const res = await getDeptTree();
				const data = this.addParentId(res.data, null); // 设置初始状态
				this.treeData = data
			},
			async getDeviceList() {
				try {
					// 显示加载提示
					uni.showLoading({
						title: '数据加载中...'
					});

					// 等待异步请求完成
					const res = await getDahuaOrganizationLinkById(this.queryParams);
					uni.hideLoading();
					// 处理响应数据
					this.serviceList = res.data.pageData;
					this.total = res.data.totalPage

				} catch (error) {
					// 处理请求错误
					console.error('请求失败:', error);
					uni.showToast({
						title: '数据加载失败',
						icon: 'none'
					});
				} finally {
					// 无论请求成功还是失败，都隐藏加载提示
					uni.hideLoading();

				}
			},
			addParentId(nodes, parentId) {
				for (let i = 0; i < nodes.length; i++) {
					const node = nodes[i];
					if (node.children && Array.isArray(node.children)) {
						this.addParentId(node.children, node.id); // 递归处理子节点
					}
					node.parentId = parentId; // 设置当前节点的 parentId
					node.isOpen = false


				}
				return nodes
			},
			// 初始化树形结构数据
			initializeTreeData(data) {
				data.forEach(node => {
					node.isOpen = false
					if (node.children) {
						this.initializeTreeData(node.children); // 递归设置子节点的 isOpen 状态
					}
				});

				return data;
			},
			toggle(type) {
				this.type = type
				// open 方法传入参数 等同在 uni-popup 组件上绑定 type属性
				this.$refs.popup.open(type)
			},
			toggle1(type) {
				if (this.serviceList.length > 0) {
					this.type = type
					// open 方法传入参数 等同在 uni-popup 组件上绑定 type属性
					this.$refs.popup2.open(type)
				} else {
					uni.showToast({
						title: '请先选择部门',
						icon: 'none',
						duration: 2000
					})
				}
			},
			close() {
				this.$refs.popup.close('bottom')
			},
			close2() {
				this.$refs.popup2.close('bottom')
			},
			clickNode(item) {
				this.$emit('clickNode', item)
				this.value = item.label
				this.queryParams.deptId = item.id
				this.serviceList = []
				this.getDeviceList()
				this.$refs.popup.close('bottom')
			},
			changeCheck(e) {
				console.log(e);

			},
			//合闸
			Closing(item) {

			},
			//分闸
			open(item) {

			},
			//筛选状态
			selectionStatus(e) {
				console.log(e);
				this.$refs.popup2.close('bottom')
			},
			//更换页数
			changePageNum(e) {
				this.queryParams.pageNum = e.current
				console.log(this.queryParams);
				this.getDeviceList()
			}


		},
		created() {
			this.getUserTree();
		},

	};
</script>

<style lang="scss" scoped>
	.table-bottom {
		margin-top: 10px;
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		align-items: center;
		gap: 12px;
		justify-items: center;
	}

	.card {
		width: 95%;
		height: 400rpx;
		box-shadow: 5px 5px 10px 2px rgba(0, 0, 0, 0.3);
		border-radius: 10px;
		background-color: #fff;
		margin: auto;
		margin-top: 30rpx;

	}

	.img {
		width: 100%;
		height: 100%;
		background-image: url('../../static/images/banner/nav2.jpg');
		filter: opacity(80%);
		background-size: cover;
		background-position: center;
		border-radius: 30rpx;
	}
</style>