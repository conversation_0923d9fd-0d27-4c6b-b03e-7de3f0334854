<template>
	<view>
		<view class="example-body">
			<uni-row class="demo-uni-row">
				<uni-col :span="9" :offset="2">
					<view class="demo-uni-col dark_deep">
						<view
							style="display: flex;  justify-content: center; align-items: center; margin-bottom: 10px;">
							<view class="workIcon workIcon-baojingzhenggai"></view>
							<view>
								<view style="font-weight: bold; font-size: 20px;">今日</view>
								<view style="font-size: 10px;">{{currentTime}}</view>
							</view>
						</view>
						<view
							style="display: flex; flex-direction: column; justify-content: center; align-items: center; margin-bottom: 10px;">
							<view>报警未处理数:<span style="color: red;">{{unprocessedToday}}</span></view>
							<view>报警发生数:{{processedToday}}</view>
						</view>

					</view>
				</uni-col>

				<uni-col :span="9" :offset="2">
					<view class="demo-uni-col light">
						<view
							style="display: flex;  justify-content: center; align-items: center; margin-bottom: 10px; margin-top: 5px;">
							<view class="workIcon workIcon-baojingzhenggai"></view>
							<view>
								<view style="font-weight: bold; font-size: 20px;">近30天</view>
								<view style="font-size: 10px;">{{formattedDate}}至今日</view>
							</view>
						</view>
						<view
							style="display: flex; flex-direction: column; justify-content: center; align-items: center; margin-bottom: 10px;margin-top: 5px;">
							<view>报警未处理数:<span style="color: red;">{{unprocessed30Days}}</span></view>
							<view>报警发生数:{{processedToday30Days}}</view>
						</view>

					</view>
				</uni-col>
			</uni-row>
		</view>
		<view v-if="alarms.length!==0">

			<view>
				<uni-card v-for="(item,index) in alarms" :key="index" @click="to(item)">
					<view>所属组织:{{item.orgName}}</view>
					<view>设备名称:{{item.alarmPosition}}</view>
					<view>报警类型:{{item.alarmTypeName}}</view>
					<view v-if="item.alarmStat===1">报警状态:正在报警</view>
					<view v-if="item.alarmStat===2">报警状态:报警消失</view>

					<view style="display: flex; justify-content: space-between;">
						<view>
							<view v-if="item.handleStat===0">处理状态:<span style="color: red;">未处理</span></view>
							<view v-if="item.handleStat===1">处理状态:<span style="color: #3978f6;">处理中</span></view>
							<view v-if="item.handleStat===2">处理状态:<span style="color: #98c379;">已处理</span></view>
							<view v-if="item.handleStat===3">处理状态:<span style="color: #d19a66;">误报</span></view>
							<view v-if="item.handleStat===4">处理状态:忽略</view>
						</view>
						<view>
							<view v-if="item.isOverdue===1"><button :disabled="true" type="warn" size="mini">超期</button>
							</view>
							<view v-else><button :disabled="true" type="default" size="mini">未超期</button></view>
						</view>
					</view>
					<view>报警时间:{{item.alarmDate}}</view>
				</uni-card>
			</view>


			<view style="margin: 10rpx; padding: 50rpx;">
				<uni-pagination :total="total" title="标题文字" prev-text="前一页" next-text="后一页" @change="changePageNum" />
			</view>
		</view>



	</view>
</template>

<script>
	import {
		electricWarn,
		electricWarnList,
		electricWarnHandle
	} from '@/api/energy.js'

	export default {
		data() {
			return {
				currentTime: '',
				unprocessedToday: 0,
				processedToday: 0,
				unprocessed30Days: 0,
				processedToday30Days: 0,
				formattedDate: '',
				alarms: [],
				total: 0,
				params: {
					alarmStartDateString: '',
					alarmEndDateString: '',
					pageNum: 1,
					pageSize: 10,
					orgCodeList: ["103", "-1"], // 根据需要调整组织代码
				}


			}
		},
		onLoad() {
			const data = new Date()
			const year = data.getFullYear();
			const month = String(data.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，所以需要加 1，并确保为两位数
			const day = String(data.getDate()).padStart(2, '0');
			this.currentTime = year + '-' + month + '-' + day
			const currentDate = new Date();
			const thirtyDaysAgo = new Date(currentDate.getTime() - (30 * 24 * 60 * 60 * 1000)); // 减去30天
			// 格式化日期为YYYY-MM-DD
			this.formattedDate = thirtyDaysAgo.getFullYear() + '-' + String(thirtyDaysAgo.getMonth() + 1).padStart(2,
				'0') + '-' + String(thirtyDaysAgo.getDate()).padStart(2, '0')
			this.getElectricWarnList()
			this.fetchWarningData()

		},
		methods: {
			async fetchWarningData() {
				try {
					const response = await electricWarn();
					const data = response.data;
					this.unprocessedToday = data.warnUntreatedToday;
					this.processedToday = data.warnNumToday;
					this.unprocessed30Days = data.warnUntreatedMonth;
					this.processedToday30Days = data.warnNumMonth;
				} catch (error) {
					console.error("获取报警数据失败:", error);
				}
			},
			async getElectricWarnList() {

				try {
					uni.showLoading({
						title: '数据加载中...'
					});
					this.params.alarmStartDateString = this.formattedDate + " " + "00:00:00"
					this.params.alarmEndDateString = this.currentTime + " " + "00:00:00"
					const res = await electricWarnList(this.params)
					uni.hideLoading();

					this.alarms = res.data.pageData
					this.total = res.data.pageData.length
					console.log(this.total);
				} catch (e) {
					console.error("获取报警数据失败:", error);
					uni.showToast({
						title: '数据加载失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();

				}

			},
			changePageNum(e) {
				this.params.pageNum = e
				this.getElectricWarnList()

			},
			to(item) {
				uni.navigateTo({
					url: '/pages/EnergyMgt/alarmDetails?item=' + JSON.stringify(item)
				})

			}
		}
	}
</script>

<style>
	@import url('../../static/workServerIcon/workServerIcon.css');

	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		overflow: hidden;
		padding-top: 40rpx;
	}

	.demo-uni-col {
		height: 90px;
		border-radius: 5px;

	}

	.dark_deep {
		background-color: #fff1f0;
	}


	.light {
		background-color: #fff7e6;
	}
</style>