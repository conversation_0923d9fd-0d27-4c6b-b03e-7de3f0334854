<template>
	<view class="p20">
		<view class="bf p20">

			<view class="flex order_item just-sbet">
				<view class="lable">
					工单类型：{{typeName }}

				</view>
				<view class="history" @click="toHistoryOrder">
					查看历史记录
					
				</view>
			</view>
			<view class="flex order_item">
				<view class="lable">
					下一步处理人：

				</view>
				<view class="">
					<picker @change="inspectorPickerChange" range-key="username" :range="userList">
						<view class="uni-input">{{selectUserName}} <uni-icons type="bottom" style="margin-left: 10rpx;"></uni-icons>
						</view>
					</picker>
				</view>
			</view>
			<view class="flex order_item">
				<view class="lable">
					下一步状态：

				</view>
				<view class="">
					<picker @change="statusPickerChange" :range="statusOptions">
						<view class="uni-input">{{formParams.status}} <uni-icons type="bottom"
								style="margin-left: 10rpx;"></uni-icons>
						</view>
					</picker>
				</view>
			</view>
		</view>
		<view class="p20 bf opportunityInfo">
			<uni-section title="处理意见" type="line">
				<view class="text_contatiner">
					<view class="oppo_info">
						<textarea :style="{'borderColor':isfocus? '#5b73f9':'#CCC'}" class="update_input w-full"
							@focus="isfocus=true" @blur="isfocus=false" maxlength="150" @input="inputBusiness" type="textarea"
							v-model="formParams.dealOpinion" placeholder="请输入处理意见"></textarea>

						<view class="length" :style="{'color':isfocus? '#5b73f9':'#000'}">
							{{businessInfoLength}}/150
						</view>
					</view>
				</view>
			</uni-section>
			<uni-section title="上传图片" type="line">
				<view class="example-body">
					<uni-file-picker v-model="imageValue" limit="9" title="最多选择9张图片" fileMediatype="image" mode="grid"
						@select="handleSelect" @delete="handleDelete"></uni-file-picker>
				</view>
			</uni-section>


		</view>
		<view class="btn" @click="btnPropetyPull">
			提交
		</view>
	</view>
</template>

<script>
	import {
		listUser,
		getUserProfile,
		uploadImage
	} from '@/api/system/user'
	import {
		workOrderUpdate,
		getworkOrderInfo
	} from '../../../api/order';
	import {
		getAssetNum,
		show,
		topage
	} from '../../../common/common_methods';
	export default {
		data() {
			return {
				formParams: {
					"id": '',
					"status": "",
					"processorUserId": '', // 下一步处理人id
					"dealOpinion": "",
					"attachment": "[]"
				},
				selectTypeName: "盘点问题",
				typeOptions: [{
						label: "盘点问题",
						value: 1
					},
					{
						label: "消防治安问题",
						value: 2
					},
					{
						label: "生产安全问题",
						value: 3
					},
					{
						label: "经营合规问题",
						value: 4
					}
				],
				businessInfoLength: 0,
				isfocus: false,
				imageValue: [], // 图片地址
				filePathsList: [],
				userList: [], // 下一步处理人list
				selectUserName: "",
				userInfo: {},
				statusOptions: ["进行中", "审核中", "已完成"],
				workOrderInfo: {}, // 工单详情
				typeName: '',



			}
		},
		onLoad(option) {
			let id = JSON.parse(option.info)
			this.formParams.id = id

			this.initUser()
			this.getUser()
			this.getOneworkOrderInfo()
		},
		methods: {
			async handleSelect(res) {
				// 选择图片
				this.uploadImg(res.tempFiles)
			
			},
			async uploadImg(tempFiles) {
				// 图片上传
				if (!tempFiles.length) return
				const path = tempFiles.pop()
			
				let data = {
					name: 'file',
					filePath: path.path
				}
				console.log(path, 'path');
				let {
					fileName,
					url,
			
				} = await uploadImage(data)
				this.filePathsList.push({
					name: fileName,
					url,
					uid: path.uuid // uid和后台中的uid参数对应上
				})
				this.uploadImg(tempFiles)
			},
			handleDelete(e) {
				// 图片删除
				let {
					uuid
				} = e.tempFile
				const num = this.filePathsList.findIndex((e) => e.uid == uuid)
				this.filePathsList.splice(num, 1)
			},
			toHistoryOrder(){
				topage('./historyOrder/historyOrder',this.workOrderInfo.id)
			},
			statusPickerChange({
				detail
			}) {
				console.log(detail);
				this.formParams.status = this.statusOptions[detail.value]
			},
			async getOneworkOrderInfo() {
				let res = await getworkOrderInfo(this.formParams.id)
				this.workOrderInfo = res
				this.formParams.status = res.status
				this.getTypeName(res.type)
			},
			getTypeName(type) {
				let res = this.typeOptions.find((item) => item.value == type)
				this.typeName = res.label
			},
			async getUser() {
				let {
					data
				} = await getUserProfile()
				this.userInfo = data
				this.formParams.processorUserId = data.userId
			},
			async btnPropetyPull() {
				this.formParams.attachment = JSON.stringify(this.filePathsList)
				let res = await workOrderUpdate(this.formParams)
				if (res == 'OK') {
					show("提交成功", 200)
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				}
			},
			processorPickerChange({
				detail
			}) {
				console.log(detail);
				this.formParams.type = this.typeOptions[detail.value].value
				this.selectTypeName = this.typeOptions[detail.value].label
			},
			inspectorPickerChange({
				detail
			}) {
				console.log(detail.value);
				this.formParams.inspectorId = this.userList[detail.value].id
				this.selectUserName = this.userList[detail.value].username

			},
			async initUser() {
				const param = {
					pageNum: 1,
					pageSize: 10
				}
				this.userList = []
				let {
					rows
				} = await listUser(param)
				rows.map(u => {
					const user = {
						id: u.userId,
						username: u.nickName ? u.nickName : u.userName
					}
					this.userList.push(user)
				})
				this.selectUserName = this.userList[0].username
				this.formParams.inspectorId = this.userList[0].id

			},

			inputBusiness({
				detail
			}) {
				this.businessInfoLength = detail.value.length;
				if (this.businessInfoLength >= 150) {
					show('最多不能超过150个字', 1)
				}
			},
			getNum(id) {
				return getAssetNum(id)
			}
		}

	}
</script>

<style lang="scss">
	.order_item {
		padding: 20rpx 0;
		align-items: center;
		.history{
			padding: 10rpx 20rpx ;
			text-align: center;
			background-color: #5b73f9;
			color: #fff;
			border-radius: 10rpx;
		}
	}

	.btn {
		margin-top: 30rpx;
		padding: 20rpx;
		text-align: center;
		background-color: #5b73f9;
		color: #fff;
		border-radius: 10rpx;
	}

	.text_contatiner {
		.title {}

		.oppo_info {
			margin-top: 20rpx;
			position: relative;

			.update_input {
				border-radius: 10rpx;
				border: 2rpx solid #ccc;
				padding: 10rpx;
				height: 300rpx;
			}

			.length {
				position: absolute;
				z-index: 12312;
				right: 11rpx;
				bottom: 10rpx;
			}
		}
	}

	.opportunityInfo {
		margin-top: 20rpx;
		border-radius: 10rpx;

	}
</style>