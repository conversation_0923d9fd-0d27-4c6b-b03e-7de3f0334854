<template>
	<view class="p20">
		<view class="history_list">
			<view class="bf p20 history_item " v-for="(item,index) in historyList" :key="item.id">
				<view class="flex just-sbet history_key">
					<view class="left">
						处理人
					</view>
					<view class="left">
						{{getUserName(item.operatorId)}}
					</view>
				</view>
				<view class="flex just-sbet history_key" v-for="(jtem,key,index) in historyKey" :key="index">
					<view class="left">
						{{jtem}}
					</view>
					<view class="right">
						<text v-if="key=='alterInfo'">{{item['alterType']}}</text> {{item[key]}}
					</view>
				</view>
				<view class="flex just-sbet history_key">
					<view class="left">
						附件
					</view>
					<view class="right grid-3">

						<view class="" v-for="(jtem,jndex) in JSON.parse(item.attachment)" :key="jtem.uid"
							@click="previewImage(jtem)">
							<!-- <image style="width: 100rpx;height: 100rpx;" :src="jtem.url" mode=""></image> -->
							<view class="" style="color: #007BFF;text-decoration: underline;">
								{{'附件'+Number(jndex+1)}}
							</view>
						</view>

					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import config from '@/config'
	import {
		getworkOrderInfo
	} from '../../../../api/order';
	import {
		listUser
	} from '../../../../api/system/user';
	export default {
		data() {
			return {
				historyList: {}, // 工单详情
				historyKey: {
					"alterInfo": "处理内容",
					"dealOpinion": "处理意见",
					// operatorId: '处理人',
					"updateTime": "处理时间",

				},
				userList: []
			};
		},
		onLoad(option) {
			let id = JSON.parse(option.info)
			this.getOneworkOrderInfo(id)
			this.initUser()
		},
		methods: {
			previewImage(attachment) {
				// 预览图片
				// attachment = JSON.parse(attachment).map((item)=>{
				// 	return item.url
				// })
				// uni.previewImage({
				// 	urls: attachment,
				// 	current: index
				// })
				console.log(attachment, 'attachment');
				this.openOther(attachment.name, attachment.url)
			},
			//typeNname为文件名称，包含文件后缀.doc, val值为文件链接
			openOther(typeName, val) {
				const imgFormat = /\.(jpg|jpeg|png|gif)$/; //图片格式匹配
				const documentFormatRegex = /\.(doc|docx|xls|xlsx|ppt|txt|pdf)$/i; //文件格式
				if (imgFormat.test(typeName)) {
					// 预览图片
					uni.previewImage({
						urls: [val],
						current: 1
					})
				} else if (documentFormatRegex.test(typeName)) {
					//文件预览功能微信预览需要先下载
					uni.downloadFile({
						url: val,
						success: function(res) {
							uni.hideLoading();
							var filePath = res.tempFilePath;
							//文件保存到本地
							uni.saveFile({
								tempFilePath: filePath, //临时路径
								success: function(res) {
									uni.openDocument({
										filePath: res.savedFilePath,
										success: function(res) {
											console.log('打开文档成功');
										}
									});
								},
								fail: (err) => {
									console.log(err);
								}
							});
						},
						fail: function(err) {
							uni.hideLoading();
						}
					});
				} else {
					console.log("该格式无法打开");
				}

			},
			async initUser() {
				const param = {
					pageNum: 1,
					pageSize: 10
				}
				this.userList = []
				let {
					rows
				} = await listUser(param)
				rows.map(u => {
					const user = {
						id: u.userId,
						username: u.nickName ? u.nickName : u.userName
					}
					this.userList.push(user)
				})


			},
			getUserName(id) {
				let userName = '未知人员'
				this.userList.map(item => {
					if (item.id === Number(id)) {
						userName = item.username
					}
				})
				return userName
			},
			async getOneworkOrderInfo(id) {
				let {
					history
				} = await getworkOrderInfo(id)
				this.historyList = history

			},
		}
	}
</script>

<style lang="scss">
	.history_list {

		.history_item {
			border-radius: 20rpx;
			margin-bottom: 20rpx;

			.history_key {
				padding: 30rpx;

				.left {}

				.right {}
			}
		}
	}
</style>