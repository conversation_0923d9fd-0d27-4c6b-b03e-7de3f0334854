<template>
	<view class="w-full h-full">

		<scroll-view class="nav bf" scroll-x="true" scroll-left="120">
			<view class="nav_item" :class="item.type==selectType?'active':''" v-for="(item,index) in navSelectList"
				:key="index" @click="selectNavBtn(item,index)">
				{{getType(item.type)}}
			</view>
		</scroll-view>
		<swiper class="task_list "  :current="current" duration="500"  @change="swiperChange">
			<swiper-item v-for="item in navSelectList.length" :key="item">
				<view class="p20">
					<view class=" task_item" v-for="(item,index) in taskList" :key="index">
						<view class="flex task_top p20 bf"
							v-if="item.inspectorId==userInfo.userId||item.createUserId==userInfo.userId">
							<uni-icons type="calendar-filled" color="#5b73f9" size="40"></uni-icons>
							<view class="w-full" style="margin-top: 10rpx;">
								<view class="just-sbet as">
									<!--  问题描述 -->
									<view class="quota">
										{{item.description}}
									</view>
									<view class="tips">
										{{item.status}}
									</view>
								</view>

								<view class="flex just-sbet  w-full task_buttom">
									<view class=" inventory">
										处理人：{{getUserName(item.processorUserId)}}
									</view>
									<view class="center inventory_to " @click="toInventory(item)">
										<text>去处理</text>
										<uni-icons type="right"></uni-icons>
									</view>
								</view>
							</view>
						</view>

					</view>
				</view>
			</swiper-item>

		</swiper>


	</view>
</template>

<script>
	import {
		workOrderQuery
	} from '../../api/order';
	import {
		getUserProfile,
		listUser
	} from '../../api/system/user';

	import {
		topage
	} from '../../common/common_methods';
	export default {
		data() {
			return {
				navSelectList: [], // nav
				selectType: "", // 默认选中
				taskList: [], // 任务列表
				taskAllList: [], // 全部任务
				periodOptions: [{
						label: "临时性",
						value: 1
					},
					{
						label: "按月",
						value: 12
					},
					{
						label: "按季",
						value: 4
					},
					{
						label: "按年",
						value: 5
					},
				],
				typeOptions: [{
						label: "盘点问题",
						value: 1
					},
					{
						label: "消防治安问题",
						value: 2
					},
					{
						label: "生产安全问题",
						value: 3
					},
					{
						label: "经营合规问题",
						value: 4
					}
				],
				query: {
					pageNum: 1,
					pageSize: 9999,
				},
				userInfo: {},
				userList: [],
				current: 0

			};
		},
		onLoad() {
			this.getUser()

			this.initUser()
			this.getTaskList()
		},

		// onReachBottom() {
		// 	this.query.pageNum++
		// 	this.getTaskList()
		// },
		methods: {
			initUser() {
				const param = {
					pageNum: 1,
					pageSize: 10
				}
				this.userList = []
				listUser(param).then(res => {
					res.rows.map(u => {
						const user = {
							id: u.userId,
							username: u.nickName ? u.nickName : u.userName
						}
						this.userList.push(user)
					})
				})
			},
			getUserName(id) {
				let userName = '未知人员'
				this.userList.map(item => {
					if (item.id === Number(id)) {
						userName = item.username
					}
				})
				return userName
			},
			async getUser() {
				let {
					data
				} = await getUserProfile()
				this.userInfo = data
			},
			getType(type) {
				let result = '未知类型'
				this.typeOptions.map(item => {
					if (item.value === type) {
						result = item.label
					}
				})
				return result
			},
			toInventory(item) {
				topage('./handleOrder/handleOrder', item.id)
			},
			async getTaskList() {

				let {
					list
				} = await workOrderQuery(this.query);

				this.taskAllList = [...this.taskAllList, ...list]
				let age = 'type'
				this.navSelectList = this.taskAllList.reduce((all, next) => all.some(atom => atom[age] == next[age]) ? all : [
					...all,
					next
				], [])
				this.selectNavBtn(this.navSelectList[0],0)
			},
			selectNavBtn(item,index) {
				this.current = index;
				this.taskList = []
				this.selectType = item.type
				this.taskAllList.forEach((jtem) => {
					if (item.type == jtem.type) {

						this.taskList.push(jtem)
					}
				})
				console.log(this.taskList);
			},
			getPeriod(period) {
				let periodStr = '未知周期'
				this.periodOptions.map(item => {
					if (item.value === period) {
						periodStr = item.label
					}
				})
				return periodStr
			},
			swiperChange({
				detail
			}) {
				console.log(detail.current);
				let current = detail.current;
				this.selectNavBtn(this.navSelectList[current],current);
			}


		}
	}
</script>

<style lang="scss">
	page {
		width: 100%;
		height: 100%;
	}

	.nav {
		position: fixed;
		width: 100%;
		top: 0;
		z-index: 1024;
		box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);

		.nav_item {
			display: inline-block;
			padding: 20rpx;
			text-align: center;
		}

		.active {
			color: #5b73f9;
			font-weight: bold;
			border-bottom: 4rpx solid #5b73f9;
		}
	}

	.task_list {
		margin-top: 100rpx;
		width: 100%;
		height: 100%;

		.task_item {
			margin-bottom: 20rpx;
			border-radius: 10rpx;
			width: 100%;
			box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);

			.task_top {
				border-radius: 20rpx;

				.quota {
					
					width: 70%;
					font-size: 30rpx;
					font-weight: bold;
					color: #272727;
				}

				.tips {
					border-radius: 20rpx;
					padding: 5rpx 20rpx;
					background-color: rgba(91, 115, 249, 0.1);
					color: #5b73f9;
				}

				.bill_user {
					font-size: 30rpx;
					color: #272727;
					margin-top: 20rpx;
				}

				.inventory {}
			}

			.task_buttom {
				margin-top: 20rpx;

				.inventory {
					font-size: 30rpx;
				}

				.inventory_to {
					border-radius: 10rpx;
					border: 2rpx solid #ccc;
					padding: 5rpx 10rpx;
				}
			}

		}
	}
</style>