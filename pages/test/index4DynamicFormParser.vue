<template>
  <view class="">
    <DynamicFormParser
        :formModel="formConf4DynamicFormParser"
        :formContent="formContent"
        ></DynamicFormParser>
  </view>
</template>
<script>
import DynamicFormParser from '@/components/parser/DynamicFormParser';
import { formConf4DynamicFormParser } from './formConfigDynamicFormParser';
export default {
  name: "index4DynamicFormParser" ,
  data() {
    return {
      //
      formConf4DynamicFormParser,
      formContent:{}
    };
  },
  components: {DynamicFormParser},
  watch: {
	  formConf4DynamicFormParser: function (newValue, oldValue) {
	    console.log("newValue", newValue)
	    console.log("oldValue", oldValue)
		//this.localFormContent = Object.assign({},newValue)
	   // console.log("this.localFormContent", this.localFormContent)
	  },
  },
  created() {
	  console.log("aaa",this.formConf4DynamicFormParser)
  }
}
</script>
<style scoped lang="scss">

</style>