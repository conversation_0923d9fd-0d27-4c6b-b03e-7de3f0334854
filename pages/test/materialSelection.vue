<template>
	<view class="page">
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<text class="title">选择耗材</text>
		</view>
		
		<!-- 仓库和分类信息展示 -->
		<view class="warehouse-info">
			<view class="info-item">
				<text class="label">仓库：</text>
				<text class="value">{{warehouseName}}</text>
			</view>
			<view class="info-item" v-if="firstLevelName">
				<text class="label">分类：</text>
				<text class="value">
					{{firstLevelName}}
					<text v-if="secondLevelName"> > {{secondLevelName}}</text>
					<text v-if="thirdLevelName"> > {{thirdLevelName}}</text>
				</text>
			</view>
		</view>
		
		<!-- 搜索框 -->
		<view class="search-box">
			<input 
				type="text" 
				v-model="searchText" 
				placeholder="搜索耗材名称" 
				@input="searchMaterials"
				class="search-input"
			/>
			<text class="search-icon">🔍</text>
		</view>
		
		<!-- 耗材列表 -->
		<view class="materials-list">
			<view class="section-title">
				<text>耗材列表</text>
			</view>
			<view class="list-header">
				<view class="column name">名称</view>
				<view class="column spec">规格</view>
				<view class="column price">单价</view>
				<view class="column stock">库存</view>
				<view class="column action">操作</view>
			</view>
			
			<scroll-view scroll-y="true" class="list-body">
				<view 
					v-for="(item, index) in paginatedMaterials" 
					:key="index" 
					class="list-item"
				>
					<view class="column name">{{item.productName}}</view>
					<view class="column spec">{{item.productSpec}}</view>
					<view class="column price">{{item.productPrice}}</view>
					<view class="column stock">{{item.originalQuantity}}</view>
					<view class="column action">
						<text 
							class="add-btn" 
							@click="selectByIndex(index)"
							v-if="!isItemSelected(item)"
						>添加</text>
						<text 
							class="added-btn" 
							v-else
						>已添加</text>
					</view>
				</view>
			</scroll-view>
			
			<!-- 无数据提示 -->
			<view class="empty-tip" v-if="filteredMaterials.length === 0 && !loading">
				<text>暂无耗材数据</text>
			</view>
			
			<!-- 加载提示 -->
			<view class="loading-tip" v-if="loading">
				<text>正在加载耗材数据...</text>
			</view>
			
			<!-- 分页控制区 -->
			<view class="pagination-control" v-if="filteredMaterials.length > 0">
				<view class="pagination-buttons">
					<text 
						class="pagination-btn prev" 
						:class="{ disabled: materialCurrentPage <= 1 }"
						@click="changeMaterialPage(materialCurrentPage - 1)"
					>&lt;</text>
					
					<view class="pagination-pages">
						<text 
							v-if="materialCurrentPage > 3" 
							class="pagination-page"
							@click="changeMaterialPage(1)"
						>1</text>
						
						<text v-if="materialCurrentPage > 3" class="pagination-ellipsis">...</text>
						
						<text 
							v-for="page in 5" 
							:key="page"
							v-if="materialCurrentPage - 3 + page > 0 && materialCurrentPage - 3 + page <= totalMaterialPages"
							:class="['pagination-page', { active: materialCurrentPage === materialCurrentPage - 3 + page }]"
							@click="changeMaterialPage(materialCurrentPage - 3 + page)"
						>{{materialCurrentPage - 3 + page}}</text>
						
						<text v-if="materialCurrentPage < totalMaterialPages - 2" class="pagination-ellipsis">...</text>
						
						<text 
							v-if="materialCurrentPage < totalMaterialPages - 2" 
							class="pagination-page"
							@click="changeMaterialPage(totalMaterialPages)"
						>{{totalMaterialPages}}</text>
					</view>
					
					<text 
						class="pagination-btn next" 
						:class="{ disabled: materialCurrentPage >= totalMaterialPages }"
						@click="changeMaterialPage(materialCurrentPage + 1)"
					>&gt;</text>
				</view>
				
				<view class="pagination-info">
					<text>{{materialCurrentPage}}/{{totalMaterialPages}}页，共{{filteredMaterials.length}}条</text>
				</view>
			</view>
		</view>
		
		<!-- 已选耗材区域 -->
		<view class="selected-materials-area" v-if="selectedMaterialsList.length > 0">
			<view class="section-title">
				<text>已选耗材</text>
				<text class="clear-all" @click="clearAllSelected">清空</text>
			</view>
			<scroll-view scroll-y="true" class="selected-scroll-view">
				<view 
					v-for="(item, index) in paginatedSelectedMaterials" 
					:key="index" 
					class="selected-item"
				>
					<view class="selected-info">
						<text class="selected-name">{{item.productName}}</text>
						<text class="selected-spec">{{item.productSpec}}</text>
					</view>
					<view class="quantity-control">
						<text 
							class="quantity-btn minus" 
							@click="decreaseQuantity(selectedMaterialsList[index])"
						>-</text>
						<input 
							type="number" 
							:value="item.selectedQuantity"
							@input="updateQuantity(index, $event)" 
							class="quantity-input"
							@blur="validateQuantity(selectedMaterialsList[index])"
						/>
						<text 
							class="quantity-btn plus" 
							@click="increaseQuantity(selectedMaterialsList[index])"
						>+</text>
					</view>
					<text class="delete-btn" @click="removeSelected(index)">×</text>
				</view>
			</scroll-view>
			<view class="selected-summary">
				<text class="selected-total">总价: ¥{{totalPrice.toFixed(2)}}</text>
			</view>
			
			<!-- 已选耗材分页控制区 -->
			<view class="pagination-control" v-if="selectedMaterialsList.length > 0">
				<view class="pagination-buttons">
					<text 
						class="pagination-btn prev" 
						:class="{ disabled: selectedCurrentPage <= 1 }"
						@click="changeSelectedPage(selectedCurrentPage - 1)"
					>&lt;</text>
					
					<view class="pagination-pages">
						<text 
							v-if="selectedCurrentPage > 3" 
							class="pagination-page"
							@click="changeSelectedPage(1)"
						>1</text>
						
						<text v-if="selectedCurrentPage > 3" class="pagination-ellipsis">...</text>
						
						<text 
							v-for="page in 5" 
							:key="page"
							v-if="selectedCurrentPage - 3 + page > 0 && selectedCurrentPage - 3 + page <= totalSelectedPages"
							:class="['pagination-page', { active: selectedCurrentPage === selectedCurrentPage - 3 + page }]"
							@click="changeSelectedPage(selectedCurrentPage - 3 + page)"
						>{{selectedCurrentPage - 3 + page}}</text>
						
						<text v-if="selectedCurrentPage < totalSelectedPages - 2" class="pagination-ellipsis">...</text>
						
						<text 
							v-if="selectedCurrentPage < totalSelectedPages - 2" 
							class="pagination-page"
							@click="changeSelectedPage(totalSelectedPages)"
						>{{totalSelectedPages}}</text>
					</view>
					
					<text 
						class="pagination-btn next" 
						:class="{ disabled: selectedCurrentPage >= totalSelectedPages }"
						@click="changeSelectedPage(selectedCurrentPage + 1)"
					>&gt;</text>
				</view>
				
				<view class="pagination-info">
					<text>{{selectedCurrentPage}}/{{totalSelectedPages}}页，共{{selectedMaterialsList.length}}条</text>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-bar">
			<view class="selected-info">
				<text class="selected-count">已选择：{{selectedCount}}种</text>
			</view>
			<view class="action-btns">
				<text class="cancel-btn" @click="goBack">取消</text>
				<text class="confirm-btn" @click="confirmSelection">确认选择</text>
			</view>
		</view>
	</view>
</template>

<script>
import { listHrpMat } from '@/api/HRP/hrpmat.js';

export default {
	data() {
		return {
			// 页面参数
			dwbh: '', // 仓库编号
			sortCode: '', // 分类编码
			warehouseName: '', // 仓库名称
			firstLevelName: '', // 一级分类名称
			secondLevelName: '', // 二级分类名称
			thirdLevelName: '', // 三级分类名称
			
			// 耗材数据
			materials: [], // 原始耗材列表
			filteredMaterials: [], // 过滤后的耗材列表
			selectedMaterialsList: [], // 已选择的耗材列表
			
			// 搜索相关
			searchText: '',
			
			// 状态控制
			loading: false,
			
			// 分页相关
			materialPageSize: 10, // 每页显示数量，固定10条
			materialCurrentPage: 1, // 当前页码
			
			selectedPageSize: 10, // 已选耗材每页显示数量
			selectedCurrentPage: 1 // 已选耗材当前页码
		}
	},
	computed: {
		// 已选耗材数量
		selectedCount() {
			return this.selectedMaterialsList.length;
		},
		// 总价
		totalPrice() {
			return this.selectedMaterialsList.reduce((total, item) => total + item.productPrice * item.selectedQuantity, 0);
		},
		// 计算当前页的耗材列表
		paginatedMaterials() {
			// 如果总数据量小于等于页面大小，直接返回所有数据
			if (this.filteredMaterials.length <= this.materialPageSize) {
				return this.filteredMaterials;
			}
			
			const startIndex = (this.materialCurrentPage - 1) * this.materialPageSize;
			const endIndex = startIndex + this.materialPageSize;
			return this.filteredMaterials.slice(startIndex, endIndex);
		},
		// 计算耗材列表的总页数
		totalMaterialPages() {
			return Math.ceil(this.filteredMaterials.length / this.materialPageSize);
		},
		// 计算当前页的已选耗材列表
		paginatedSelectedMaterials() {
			// 如果总数据量小于等于页面大小，直接返回所有数据
			if (this.selectedMaterialsList.length <= this.selectedPageSize) {
				return this.selectedMaterialsList;
			}
			
			const startIndex = (this.selectedCurrentPage - 1) * this.selectedPageSize;
			const endIndex = startIndex + this.selectedPageSize;
			return this.selectedMaterialsList.slice(startIndex, endIndex);
		},
		// 计算已选耗材列表的总页数
		totalSelectedPages() {
			return Math.ceil(this.selectedMaterialsList.length / this.selectedPageSize);
		}
	},
	onLoad(options) {
		// 获取页面参数
		this.dwbh = options.dwbh || '';
		this.sortCode = options.sortCode || '';
		this.warehouseName = decodeURIComponent(options.warehouseName || '');
		this.firstLevelName = decodeURIComponent(options.firstLevelName || '');
		this.secondLevelName = decodeURIComponent(options.secondLevelName || '');
		this.thirdLevelName = decodeURIComponent(options.thirdLevelName || '');
		
		// 加载耗材数据
		this.loadMaterials();
	},
	onReady() {
		// 检查数据是否正常加载
		console.log('页面准备就绪，当前数据：', {
			materials: this.materials.length,
			filtered: this.filteredMaterials.length,
			firstItem: this.filteredMaterials[0]
		});
	},
	methods: {
		// 加载耗材数据
		async loadMaterials() {
			if (!this.dwbh) {
				uni.showToast({
					title: '仓库编号不能为空',
					icon: 'none'
				});
				return;
			}
			
			this.loading = true;
			
			try {
				const params = {
					dwbh: this.dwbh,
					sortCode: this.sortCode
				};
				
				const res = await listHrpMat(params);
				
				console.log('原始API响应数据:', res);
				
				if (res.code === 200) {
					// 处理获取的数据
					this.materials = (res.data || []).map(item => ({
						productId: item.productId || `temp_${Date.now()}`,
						productName: item.productName || '未知名称',
						productSpec: item.productSpec || '',
						productPrice: item.productPrice || 0,
						unit: item.unit || '个',
						quantity: item.quantity || 0,
						originalQuantity: item.quantity || 0
					}));
					
					// 初始化过滤后的数据
					this.filteredMaterials = [...this.materials];
					
					console.log('加载耗材数据成功:', this.materials);
				} else {
					throw new Error(res.msg || '获取耗材数据失败');
				}
			} catch (error) {
				console.error('加载耗材数据失败:', error);
				uni.showToast({
					title: error.message || '加载耗材数据失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 搜索耗材
		searchMaterials() {
			if (!this.searchText.trim()) {
				// 如果搜索框为空，显示所有数据
				this.filteredMaterials = [...this.materials];
			} else {
				// 根据搜索文本过滤耗材
				const keyword = this.searchText.toLowerCase().trim();
				this.filteredMaterials = this.materials.filter(item => {
					return (
						(item.productName && item.productName.toLowerCase().includes(keyword)) ||
						(item.productSpec && item.productSpec.toLowerCase().includes(keyword))
					);
				});
			}
			
			// 重置分页状态
			this.materialCurrentPage = 1;
		},
		
		// 检查物品是否已被选择
		isItemSelected(item) {
			return this.selectedMaterialsList.some(selectedItem => 
				selectedItem.productId === item.productId
			);
		},
		
		// 选择耗材
		selectMaterial(item) {
			console.log("item", item);
			console.log("filteredMaterials 当前状态:", this.filteredMaterials);
			console.log("当前选择的索引位置:", this.filteredMaterials.findIndex(i => i === item));
			
			// 确保item不为undefined
			if (!item || typeof item !== 'object') {
				console.error('选择的耗材项为空或无效', item);
				uni.showToast({
					title: '选择的耗材数据无效',
					icon: 'none'
				});
				return;
			}
			
			// 检查originalQuantity属性是否存在
			if (typeof item.originalQuantity === 'undefined' || item.originalQuantity === null) {
				console.error('耗材库存数据无效', item);
				
				// 尝试从quantity字段获取
				const quantity = item.quantity || 0;
				console.log('尝试从quantity字段获取值:', quantity);
				
				// 更新item对象
				item.originalQuantity = quantity;
				
				if (quantity <= 0) {
					uni.showToast({
						title: '该耗材库存不足',
						icon: 'none'
					});
					return;
				}
			} else if (item.originalQuantity <= 0) {
				uni.showToast({
					title: '该耗材库存不足',
					icon: 'none'
				});
				return;
			}
			
			// 检查是否已经存在于已选列表
			if (this.isItemSelected(item)) {
				uni.showToast({
					title: '该耗材已添加',
					icon: 'none'
				});
				return;
			}
			
			// 为了安全起见，创建一个新对象添加到已选列表
			const selectedItem = {
				productId: item.productId || `temp_${Date.now()}`,
				productName: item.productName || '未知名称',
				productSpec: item.productSpec || '',
				productPrice: item.productPrice || 0,
				unit: item.unit || '个',
				originalQuantity: item.originalQuantity || 0,
				selectedQuantity: 1
			};
			
			this.selectedMaterialsList.push(selectedItem);
			
			uni.showToast({
				title: '添加到已选列表',
				icon: 'success'
			});
			
			console.log('添加成功', selectedItem);
		},
		
		// 减少选择数量
		decreaseQuantity(item) {
			if (!item) {
				console.error('减少数量时item为空');
				return;
			}
			
			// 检查item是否还在selectedMaterialsList中
			const index = this.selectedMaterialsList.findIndex(i => i.productId === item.productId);
			if (index === -1) {
				console.error('该耗材已不在已选列表中');
				return;
			}
			
			// 使用列表中实际的item引用，避免引用断开问题
			const currentItem = this.selectedMaterialsList[index];
			
			if (typeof currentItem.selectedQuantity !== 'number') {
				currentItem.selectedQuantity = 1;
			}
			
			if (currentItem.selectedQuantity > 1) {
				currentItem.selectedQuantity--;
			} else {
				// 提示用户
				uni.showModal({
					title: '提示',
					content: '数量已是最小值，是否移除该耗材？',
					success: (res) => {
						if (res.confirm) {
							// 移除该物品
							this.selectedMaterialsList.splice(index, 1);
						}
					}
				});
			}
		},
		
		// 增加选择数量
		increaseQuantity(item) {
			if (!item) {
				console.error('增加数量时item为空');
				return;
			}
			
			// 检查item是否还在selectedMaterialsList中
			const index = this.selectedMaterialsList.findIndex(i => i.productId === item.productId);
			if (index === -1) {
				console.error('该耗材已不在已选列表中');
				return;
			}
			
			// 使用列表中实际的item引用，避免引用断开问题
			const currentItem = this.selectedMaterialsList[index];
			
			if (typeof currentItem.selectedQuantity !== 'number') {
				currentItem.selectedQuantity = 1;
			}
			
			if (typeof currentItem.originalQuantity !== 'number') {
				currentItem.originalQuantity = 1;
			}
			
			if (currentItem.selectedQuantity < currentItem.originalQuantity) {
				currentItem.selectedQuantity++;
			} else {
				uni.showToast({
					title: '不能超过库存数量',
					icon: 'none'
				});
			}
		},
		
		// 验证输入的数量
		validateQuantity(item) {
			if (!item) {
				console.error('验证数量时item为空');
				return;
			}
			
			// 检查item是否还在selectedMaterialsList中
			const index = this.selectedMaterialsList.findIndex(i => i.productId === item.productId);
			if (index === -1) {
				console.error('该耗材已不在已选列表中');
				return;
			}
			
			// 使用列表中实际的item引用，避免引用断开问题
			const currentItem = this.selectedMaterialsList[index];
			
			if (typeof currentItem.selectedQuantity === 'undefined') {
				currentItem.selectedQuantity = 1;
				return;
			}
			
			let quantity = parseInt(currentItem.selectedQuantity);
			
			if (isNaN(quantity) || quantity < 1) {
				// 如果输入无效或小于1，设为1
				currentItem.selectedQuantity = 1;
			} else if (quantity > currentItem.originalQuantity) {
				// 如果超过库存，设为最大库存
				currentItem.selectedQuantity = currentItem.originalQuantity;
				uni.showToast({
					title: '已调整为最大库存数量',
					icon: 'none'
				});
			} else {
				// 有效数量，四舍五入为整数
				currentItem.selectedQuantity = Math.round(quantity);
			}
		},
		
		// 移除已选物品
		removeSelected(index) {
			// 计算在全部数据中的实际索引
			const startIndex = (this.selectedCurrentPage - 1) * this.selectedPageSize;
			const realIndex = startIndex + index;
			
			if (realIndex < 0 || realIndex >= this.selectedMaterialsList.length) {
				console.error('无效的索引：', realIndex);
				return;
			}
			this.selectedMaterialsList.splice(realIndex, 1);
			
			// 如果当前页没有数据了且不是第一页，则回到上一页
			if (this.paginatedSelectedMaterials.length === 0 && this.selectedCurrentPage > 1) {
				this.selectedCurrentPage--;
			}
		},
		
		// 清空所有已选物品
		clearAllSelected() {
			if (this.selectedMaterialsList.length === 0) return;
			
			uni.showModal({
				title: '提示',
				content: '确定要清空所有已选耗材吗？',
				success: (res) => {
					if (res.confirm) {
						this.selectedMaterialsList = [];
					}
				}
			});
		},
		
		// 确认选择
		confirmSelection() {
			if (this.selectedMaterialsList.length === 0) {
				uni.showToast({
					title: '请至少选择一种耗材',
					icon: 'none'
				});
				return;
			}
			
			// 复制一份数据，避免引用问题
			const selectedMaterialsToReturn = this.selectedMaterialsList.map(item => ({
				productId: item.productId,
				productName: item.productName,
				productSpec: item.productSpec,
				productPrice: item.productPrice,
				unit: item.unit,
				originalQuantity: item.originalQuantity,
				quantity: item.selectedQuantity
			}));
			
			// 返回上一页并传递数据
			const pages = getCurrentPages();
			const prevPage = pages[pages.length - 2];
			
			// 使用事件通道传递数据
			uni.navigateBack({
				delta: 1,
				success: function() {
					// 成功返回上一页后，通过事件通道发送数据
					if (prevPage.$vm.getOpenerEventChannel) {
						prevPage.$vm.getOpenerEventChannel().emit(
							'updateSelectedMaterials', 
							{ selectedMaterials: selectedMaterialsToReturn }
						);
					}
				}
			});
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 选择耗材
		selectByIndex(index) {
			// 计算在全部数据中的实际索引
			const startIndex = (this.materialCurrentPage - 1) * this.materialPageSize;
			const realIndex = startIndex + index;
			const item = this.filteredMaterials[realIndex];
			this.selectMaterial(item);
		},
		
		// 更新数量
		updateQuantity(index, event) {
			if (!event || !this.selectedMaterialsList[index]) {
				console.error('无效的索引或已选耗材');
				return;
			}
			
			const quantity = parseInt(event.target.value);
			
			if (isNaN(quantity) || quantity < 1) {
				// 如果输入无效或小于1，设为1
				this.selectedMaterialsList[index].selectedQuantity = 1;
			} else if (quantity > this.selectedMaterialsList[index].originalQuantity) {
				// 如果超过库存，设为最大库存
				this.selectedMaterialsList[index].selectedQuantity = this.selectedMaterialsList[index].originalQuantity;
				uni.showToast({
					title: '已调整为最大库存数量',
					icon: 'none'
				});
			} else {
				// 有效数量，四舍五入为整数
				this.selectedMaterialsList[index].selectedQuantity = Math.round(quantity);
			}
		},
		
		// 分页相关方法
		// 切换耗材列表页码
		changeMaterialPage(page) {
			if (page < 1 || page > this.totalMaterialPages) return;
			this.materialCurrentPage = page;
		},
		
		// 切换已选耗材列表页码
		changeSelectedPage(page) {
			if (page < 1 || page > this.totalSelectedPages) return;
			this.selectedCurrentPage = page;
		}
	}
}
</script>

<style lang="scss">
.page {
	padding: 0;
	background-color: #f5f5f5;
	min-height: 100vh;
	position: relative;
	padding-bottom: 100rpx; /* 为底部操作栏预留空间 */
}

.header {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #fff;
	border-bottom: 1rpx solid #eee;
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		
		.back-icon {
			font-size: 40rpx;
			color: #333;
		}
	}
	
	.title {
		font-size: 32rpx;
		font-weight: bold;
		flex: 1;
	}
}

.warehouse-info {
	background-color: #fff;
	padding: 20rpx;
	margin-bottom: 20rpx;
	
	.info-item {
		display: flex;
		margin-bottom: 10rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.label {
			font-size: 28rpx;
			color: #606266;
			min-width: 100rpx;
		}
		
		.value {
			font-size: 28rpx;
			color: #303133;
			flex: 1;
		}
	}
}

.search-box {
	background-color: #fff;
	padding: 20rpx;
	margin-bottom: 20rpx;
	position: relative;
	
	.search-input {
		height: 80rpx;
		background-color: #f5f5f5;
		border-radius: 40rpx;
		padding: 0 80rpx 0 30rpx;
		font-size: 28rpx;
	}
	
	.search-icon {
		position: absolute;
		right: 40rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 32rpx;
		color: #999;
	}
}

/* 耗材列表样式 */
.materials-list {
	background-color: #fff;
	padding: 20rpx;
	margin-bottom: 20rpx;
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.list-header {
		display: flex;
		padding: 20rpx 0;
		background-color: #f8f8f8;
		border-bottom: 1rpx solid #eee;
		
		.column {
			font-size: 28rpx;
			font-weight: bold;
			color: #606266;
			text-align: center;
		}
	}
	
	.list-body {
		height: 300rpx; /* 固定高度，限制显示3条左右数据 */
		
		.list-item {
			display: flex;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #eee;
			
			.column {
				font-size: 28rpx;
				color: #303133;
				text-align: center;
			}
		}
	}
	
	.column {
		&.name { flex: 2; }
		&.spec { flex: 2; }
		&.price { flex: 1; }
		&.stock { flex: 1; }
		&.action { flex: 1.5; }
	}
	
	.empty-tip, .loading-tip {
		padding: 40rpx 0;
		text-align: center;
		color: #909399;
		font-size: 28rpx;
	}
}

.quantity-control {
	display: flex;
	align-items: center;
	justify-content: center;
	
	.quantity-btn {
		width: 50rpx;
		height: 50rpx;
		line-height: 50rpx;
		text-align: center;
		background-color: #f5f5f5;
		border: 1rpx solid #dcdfe6;
		
		&.minus {
			border-radius: 6rpx 0 0 6rpx;
		}
		
		&.plus {
			border-radius: 0 6rpx 6rpx 0;
		}
	}
	
	.quantity-input {
		width: 80rpx;
		height: 50rpx;
		text-align: center;
		border: 1rpx solid #dcdfe6;
		border-left: none;
		border-right: none;
	}
}

.add-btn {
	display: inline-block;
	padding: 8rpx 20rpx;
	background-color: #409EFF;
	color: #fff;
	border-radius: 6rpx;
	font-size: 24rpx;
}

.added-btn {
	display: inline-block;
	padding: 8rpx 20rpx;
	background-color: #909399;
	color: #fff;
	border-radius: 6rpx;
	font-size: 24rpx;
}

.selected-materials-area {
	background-color: #fff;
	padding: 20rpx;
	margin-bottom: 20rpx;
	
	.section-title {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
		font-size: 30rpx;
		font-weight: bold;
		
		.clear-all {
			font-size: 26rpx;
			color: #409EFF;
			font-weight: normal;
		}
	}
	
	.selected-scroll-view {
		max-height: 300rpx;
	}
	
	.selected-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;
		
		&:last-child {
			border-bottom: none;
		}
		
		.selected-info {
			flex: 1;
			margin-right: 20rpx;
			
			.selected-name {
				font-size: 28rpx;
				color: #303133;
				margin-bottom: 10rpx;
				display: block;
			}
			
			.selected-spec {
				font-size: 24rpx;
				color: #909399;
			}
		}
		
		.delete-btn {
			width: 50rpx;
			height: 50rpx;
			line-height: 46rpx;
			text-align: center;
			font-size: 36rpx;
			color: #F56C6C;
			margin-left: 20rpx;
		}
	}
	
	.selected-summary {
		padding: 20rpx 0;
		border-top: 1rpx solid #eee;
		
		.selected-total {
			font-size: 28rpx;
			color: #606266;
		}
	}
}

.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background-color: #fff;
	border-top: 1rpx solid #eee;
	z-index: 100;
	
	.selected-info {
		.selected-count {
			font-size: 28rpx;
			color: #606266;
		}
	}
	
	.action-btns {
		display: flex;
		
		.cancel-btn, .confirm-btn {
			padding: 15rpx 30rpx;
			margin-left: 20rpx;
			font-size: 28rpx;
			border-radius: 6rpx;
		}
		
		.cancel-btn {
			background-color: #f5f5f5;
			color: #606266;
		}
		
		.confirm-btn {
			background-color: #409EFF;
			color: #fff;
		}
	}
}

/* 分页控制区样式 */
.pagination-control {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-top: 1rpx solid #eee;
	
	.pagination-buttons {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		
		.pagination-btn {
			padding: 8rpx 20rpx;
			margin: 0 10rpx;
			border: 1rpx solid #dcdfe6;
			border-radius: 6rpx;
			font-size: 24rpx;
			
			&.disabled {
				background-color: #f5f5f5;
				color: #909399;
			}
		}
		
		.pagination-pages {
			display: flex;
			align-items: center;
			
			.pagination-page {
				padding: 8rpx 20rpx;
				margin: 0 5rpx;
				border: 1rpx solid #dcdfe6;
				border-radius: 6rpx;
				font-size: 24rpx;
				
				&.active {
					background-color: #409EFF;
					color: #fff;
					border-color: #409EFF;
				}
			}
			
			.pagination-ellipsis {
				padding: 0 10rpx;
				color: #606266;
			}
		}
	}
	
	.pagination-info {
		font-size: 24rpx;
		color: #606266;
	}
}
</style> 