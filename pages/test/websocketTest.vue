<template>
  <view class="container">
    <view class="status">
      <text>连接状态: {{connectStatus}}</text>
    </view>
    <view class="message-input">
      <input type="text" v-model="messageText" placeholder="请输入消息"/>
      <button @tap="sendMessage" :disabled="!isConnected">发送</button>
    </view>
    <view class="message-list">
      <view v-for="(msg, index) in messages" :key="index" class="message-item">
        {{msg}}
      </view>
    </view>
  </view>
</template>

<script>
import { getAllMessageWs } from '@/api/websocket'

export default {
  data() {
    return {
      connectStatus: '未连接',
      isConnected: false,
      messageText: '',
      messages: [],
      ws: null
    }
  },
  onLoad() {
    this.initWebSocket()
  },
  onUnload() {
    this.closeWebSocket()
  },
  methods: {
    initWebSocket() {
      this.ws = getAllMessageWs(
        // 可以传入参数
        // {
        // dpetId:103
        // }
      )

      // 逻辑几乎与web端一致，详情请查看web端中的deptDrivingDashBoard页面

      // 设置回调函数
      this.ws.options.onOpen = () => {
        this.connectStatus = '已连接'
        this.isConnected = true
        this.addMessage('系统: 连接成功')
      }

      this.ws.options.onClose = () => {
        this.connectStatus = '已断开'
        this.isConnected = false
        this.addMessage('系统: 连接已断开')
      }

      this.ws.options.onError = (error) => {
        this.connectStatus = '连接错误'
        this.isConnected = false
        this.addMessage('系统: 连接发生错误')
        console.error('WebSocket错误:', error)
      }

      this.ws.options.onMessage = (data) => {
        console.log('收到消息:' + data)
        this.addMessage('收到: ' + (typeof data === 'string' ? data : JSON.stringify(data)))
      }

      // 建立连接
      this.ws.connect()
    },

    sendMessage() {
      if (!this.messageText.trim() || !this.isConnected) return
      
      this.ws.send(this.messageText)
      this.addMessage('发送: ' + this.messageText)
      this.messageText = ''
    },

    closeWebSocket() {
      if (this.ws) {
        this.ws.close()
        this.ws = null
      }
    },

    addMessage(msg) {
      this.messages.push(msg)
    }
  }
}
</script>

<style lang="scss">
.container {
  padding: 20rpx;
  
  .status {
    padding: 20rpx;
    text-align: center;
    background-color: #f8f8f8;
    margin-bottom: 20rpx;
  }
  
  .message-input {
    display: flex;
    margin-bottom: 20rpx;
    
    input {
      flex: 1;
      border: 1px solid #ddd;
      padding: 10rpx;
      margin-right: 20rpx;
    }
    
    button {
      width: 160rpx;
    }
  }
  
  .message-list {
    .message-item {
      padding: 20rpx;
      border-bottom: 1px solid #eee;
    }
  }
}
</style> 