<template>
  <view class="hour-picker-example">
    <view class="example-section">
      <text class="section-title">小时时间选择器示例</text>

      <!-- 单选模式示例 -->
      <view class="example-item">
        <text class="label">单选模式：</text>
        <HourPicker
            :value="singleHour"
            @change="handleSingleHourChange"
            :singleMode="true"
            :currentDate="currentDate"
            :disableFutureHours="true"
        />
        <text class="result">选择结果: {{ singleHour[0] || '未选择' }}</text>
      </view>

      <!-- 范围选择模式示例 -->
      <view class="example-item">
        <text class="label">范围选择模式（全天24小时）：</text>
        <HourPicker
            :value="hourRange"
            @change="handleHourRangeChange"
            :singleMode="false"
            :currentDate="currentDate"
            :disableFutureHours="true"
        />
        <text class="result">选择结果: {{ formatRangeResult() }}</text>
      </view>

      <!-- 限制时间范围示例 -->
      <view class="example-item">
        <text class="label">限制时间范围（8:00-22:00）：</text>
        <HourPicker
            :value="limitedHours"
            @change="handleLimitedHoursChange"
            :singleMode="false"
            :currentDate="currentDate"
            minHour="08:00"
            maxHour="22:00"
        />
        <text class="result">选择结果: {{ formatLimitedResult() }}</text>
      </view>

      <!-- 工作时间选择示例 -->
      <view class="example-item">
        <text class="label">工作时间选择（8:00-18:00）：</text>
        <HourPicker
            :value="workHours"
            @change="handleWorkHoursChange"
            :singleMode="false"
            :currentDate="currentDate"
            minHour="08:00"
            maxHour="18:00"
        />
        <text class="result">工作时间: {{ formatWorkHoursResult() }}</text>
      </view>

      <!-- 日期选择 -->
      <view class="example-item">
        <text class="label">选择日期：</text>
        <DayPicker
            :value="[currentDate]"
            @change="handleDateChange"
            :singleMode="true"
        />
      </view>

      <!-- 禁用未来时间示例 -->
      <view class="example-item">
        <text class="label">禁用未来时间：</text>
        <HourPicker
            :value="pastHour"
            @change="handlePastHourChange"
            :singleMode="true"
            :currentDate="currentDate"
            :disableFutureHours="true"
        />
        <text class="result">选择结果: {{ pastHour[0] || '未选择' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import HourPicker from '@/components/MyFormComponents/time-picker/HourPicker.vue'
import DayPicker from '@/components/MyFormComponents/time-picker/DayPicker.vue'

export default {
  name: 'HourPickerExample',
  components: {
    HourPicker,
    DayPicker
  },
  data() {
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

    return {
      currentDate: todayStr,
      singleHour: [],
      hourRange: [],
      limitedHours: [],
      workHours: [],
      pastHour: []
    }
  },
  methods: {
    handleSingleHourChange(value) {
      this.singleHour = value;
      console.log('单选时间变化:', value);
    },

    handleHourRangeChange(value) {
      this.hourRange = value;
      console.log('时间范围变化:', value);
    },

    handleLimitedHoursChange(value) {
      this.limitedHours = value;
      console.log('限制时间范围变化:', value);
    },

    handleWorkHoursChange(value) {
      this.workHours = value;
      console.log('工作时间变化:', value);
    },

    handlePastHourChange(value) {
      this.pastHour = value;
      console.log('过去时间变化:', value);
    },

    handleDateChange(value) {
      this.currentDate = value[0];
      console.log('日期变化:', value[0]);
      // 当日期变化时，清空所有时间选择
      this.singleHour = [];
      this.hourRange = [];
      this.limitedHours = [];
      this.workHours = [];
      this.pastHour = [];
    },

    formatRangeResult() {
      if (this.hourRange.length === 2 && this.hourRange[0] && this.hourRange[1]) {
        return `${this.hourRange[0]} 至 ${this.hourRange[1]}`;
      }
      return '未选择';
    },

    formatLimitedResult() {
      if (this.limitedHours.length === 2 && this.limitedHours[0] && this.limitedHours[1]) {
        return `${this.limitedHours[0]} 至 ${this.limitedHours[1]}`;
      }
      return '未选择';
    },

    formatWorkHoursResult() {
      if (this.workHours.length === 2 && this.workHours[0] && this.workHours[1]) {
        const startHour = parseInt(this.workHours[0].split(':')[0]);
        const endHour = parseInt(this.workHours[1].split(':')[0]);
        const duration = endHour - startHour;
        return `${this.workHours[0]} 至 ${this.workHours[1]} (共${duration}小时)`;
      }
      return '未选择';
    }
  }
}
</script>

<style lang="scss">
.hour-picker-example {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;

  .example-section {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .section-title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 50rpx;
      text-align: center;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -15rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #07C160;
        border-radius: 2rpx;
      }
    }

    .example-item {
      margin-bottom: 50rpx;
      padding-bottom: 40rpx;
      border-bottom: 1px solid #eee;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .label {
        display: block;
        font-size: 30rpx;
        color: #333;
        margin-bottom: 25rpx;
        font-weight: 600;
      }

      .result {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-top: 25rpx;
        padding: 20rpx;
        background-color: #f8f9fa;
        border-radius: 12rpx;
        border-left: 6rpx solid #07C160;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }
    }
  }
}
</style>
