<template>
	<view class="page">
		<view>
			<text class="haocai">耗材</text>
			<button class="addhao" @click="openhandpo">添加耗材</button>
		</view>
		<uni-popup ref="handMaterialPopup" type="center">
			<view class="popup-content">
				<text class="popup-title">请选择耗材编号/名称等</text>
				<view class="search-bar">
					<input type="text" v-model="searchTerm" placeholder="请输入耗材编号/名称等" />
				</view>

				<!-- 材料选择区域 -->
				<view class="materials-container">
					<!-- 常用耗材区域 -->
					<view class="common-materials">
						<view class="type-selector">
							<radio-group @change="handleTypeChange">
								<label v-for="type in materialTypes" :key="type.id" class="type-item">
									<radio :value="type.id.toString()" :checked="type.id === selectedType" />
									<text>{{type.name}}</text>
								</label>
							</radio-group>
						</view>

						<view class="material-list">
							<view v-for="(item, index) in filteredMaterials" :key="index" class="material-item">
								<checkbox :checked="item.selected" @change="(e) => handleMaterialSelect(item, e)" />
								<text>{{ item.code }} - {{ item.name }}</text>
							</view>
						</view>
					</view>

					<!-- 已选耗材区域 -->
					<view class="selected-materials">
						<text class="section-title">已选耗材</text>
						<view class="selected-table">
							<!-- 表头 -->
							<view class="table-header">
								<view class="th name">名称</view>
								<view class="th unit">单位</view>
								<view class="th price">单价（元）</view>
								<view class="th quantity">数量</view>
							</view>
							<!-- 表格内容 -->
							<view class="table-body">
								<view v-for="(item, index) in selectedMaterials" :key="index" class="table-row">
									<view class="td name">{{item.name}}</view>
									<view class="td unit">{{item.unit}}</view>
									<view class="td price">{{item.unitPrice}}</view>
									<view class="td quantity">
										<view class="quantity-control">
											<text class="quantity-btn minus" @click="decreaseQuantity(item)">-</text>
											<input type="number" v-model="item.quantity" class="quantity-input" />
											<text class="quantity-btn plus" @click="increaseQuantity(item)">+</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="popup-footer">
					<view class="total-price">
						总价：¥{{ totalPrice }}
					</view>
					<view class="button-group">
						<button class="confirm-btn" @click="confirmSelection">确定</button>
						<button class="close-btn" @click="closePopup">关闭</button>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import {
		listRepairMaterialTypes,
		listRepairMaterials
	} from '@/api/repair.js'
	export default {
		data() {
			return {
				searchTerm: '',
				selectedType: 1,
				materialTypes: [],
				materials: [],
				selectedMaterials: [],
				selectedMaterialTypeId: null,

			};
		},
		methods: {
			openhandpo() {
				this.$refs.handMaterialPopup.open();
				this.loadMaterialTypes();
			},
			closePopup() {
				// 清除所有选中状态
				this.materials.forEach(material => {
					material.selected = false;
					material.quantity = 1; // 重置数量
				});
				// 清空已选材料列表
				this.selectedMaterials = [];
				// 重置搜索条件
				this.searchTerm = '';
				// 重置耗材类型为默认值（第一个选项）
				this.selectedType = 1;
				this.$refs.handMaterialPopup.close();
			},
			// 加载耗材类型
			async loadMaterialTypes() {
				try {
					const res = await listRepairMaterialTypes({
						pageNum: 1,
						pageSize: 10
					});
					console.log("res", res)
					this.materialTypes = res.rows;
					// 处理默认选中时的列表数据
					if (this.materialTypes.length > 0) {
						this.selectedMaterialTypeId = this.materialTypes[0].id;
						this.loadMaterials();
					}
				} catch (error) {
					console.error("加载耗材类型失败:", error);
				}
			},
			// 加载耗材列表
			async loadMaterials() {
				if (!this.selectedMaterialTypeId) return;
				       try {
				           const res = await listRepairMaterials({
				               typeId: this.selectedMaterialTypeId
				           });
				           console.log("Loaded materials:", res.rows);
				           this.materials = res.rows.map(material => ({
				               ...material,
				               selected: false // 确保每个材料的 selected 属性初始化为 false
				           }));
				           this.$nextTick(() => {
				               this.syncSelectedState();
				           });
				       } catch (error) {
				           console.error("加载耗材失败:", error);
				       }
			},

			confirmSelection() {
				this.selectedMaterials = this.materials.filter(item => item.selected);
				console.log("this.selectedMaterials",this.selectedMaterials);
				this.closePopup();
			},
			removeMaterial(item) {
				item.selected = false;
				this.selectedMaterials = this.selectedMaterials.filter(m => m.code !== item.code);
			},
			handleTypeChange(e) {
				this.selectedType = Number(e.detail.value);
				// 更新 selectedMaterialTypeId 为当前选中的类型 ID
				this.selectedMaterialTypeId = this.selectedType;
				// 加载新选中类型的材料列表
				this.loadMaterials();
				// 同步选中状态
				this.syncSelectedState();
			},
			syncSelectedState() {
				// 遍历所有材料，根据是否在 selectedMaterials 中来设置 selected 状态
				this.materials.forEach(material => {
					material.selected = this.selectedMaterials.some(selected => selected.code === material.code);
				});
			},
			handleMaterialSelect(item, e) {
				item.selected = e.detail.value;
				if (item.selected) {
					if (!this.selectedMaterials.find(m => m.code === item.code)) {
						// 添加数量属性
						item.quantity = 1;
						this.selectedMaterials.push(item);
					}
				} else {
					this.selectedMaterials = this.selectedMaterials.filter(m => m.code !== item.code);
				}
				console.log("this.selectedMaterials",this.selectedMaterials);
				this.syncSelectedState();
			},

			increaseQuantity(item) {
				if (typeof item.quantity !== 'number') {
					item.quantity = 1;
				}
				item.quantity++;
			},

			decreaseQuantity(item) {
				if (typeof item.quantity !== 'number') {
					item.quantity = 1;
				}
				if (item.quantity > 1) {
					item.quantity--;
				}
			}
		},
		computed: {
			filteredMaterials() {
				return this.materials.filter(item =>
					(item.typeId === this.selectedType) &&
					(this.searchTerm === '' ||
						item.name.includes(this.searchTerm) ||
						item.code.includes(this.searchTerm))
				);
			},
			totalPrice() {
				return this.selectedMaterials.reduce((total, item) => {
					const quantity = Number(item.quantity) || 0;
					const price = Number(item.unitPrice) || 0;
					return total + (quantity * price);
				}, 0).toFixed(2);
			}
		},
		mounted() {
			this.syncSelectedState();
		}
	};
</script>

<style lang="scss">
	.page {
		display: flex;
		flex-direction: column;
	}

	.haocai {
		text-align: left;
		margin: 20rpx;
	}

	.addhao {
		margin-left: 100rpx;
		padding: 0 20rpx;
		border: 1rpx solid #fec938;
		//border-radius: 30%;
		color: #a7ca91;
	}

	.materials-container {
	  height: 60vh;
	  display: flex;
	  flex-direction: column;
	  margin: 20rpx 0;
	  overflow: hidden;
	}
	
	.common-materials {
	  height: 45%; // 设置固定高度比例
	  border: 1px solid #eee;
	  border-radius: 8rpx;
	  margin-bottom: 20rpx;
	  display: flex;
	  flex-direction: column;
	  overflow: hidden; // 防止内容溢出
	}
	
	.type-selector {
	  padding: 20rpx;
	  border-bottom: 1px solid #eee;
	  overflow-x: auto; // 允许水平滚动
	  white-space: nowrap; // 防止换行
	}
	
	.material-list {
	  flex: 1;
	  overflow-y: auto;
	  padding: 20rpx;
	}
	
	.selected-materials {
	  height: 45%; // 设置固定高度比例
	  border: 1px solid #eee;
	  border-radius: 8rpx;
	  display: flex;
	  flex-direction: column;
	  overflow: hidden;
	}
	
	.selected-table {
	  flex: 1;
	  display: flex;
	  flex-direction: column;
	  overflow: hidden;
	}
	
	.table-header {
	  display: flex;
	  background-color: #f5f5f5;
	  padding: 20rpx 0;
	  position: sticky;
	  top: 0;
	  z-index: 1;
	}
	
	.table-body {
	  flex: 1;
	  overflow-y: auto;
	  
	  .table-row {
	    display: flex;
	    padding: 20rpx 0;
	    border-bottom: 1rpx solid #eee;
	  }
	}




	.selected-list {
		height: calc(100% - 80rpx);
		overflow-y: auto;
		padding: 20rpx;
		flex-shrink: 0; // 防止标题被压缩
	}

	.material-item,
	.selected-item {
		display: flex;
		align-items: center;
		padding: 10rpx 0;

		text {
			margin-left: 10rpx;
		}
	}

	.remove-btn {
		color: red;
		padding: 0 10rpx;
		cursor: pointer;
	}

	.popup-content {
		width: 80vw;
		max-width: 800rpx;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
	}


	.th,
	.td {
		text-align: center;

		&.name {
			flex: 2;
		}

		&.unit {
			flex: 1;
		}

		&.price {
			flex: 1;
		}

		&.quantity {
			flex: 2;
		}
	}

	.quantity-control {
		display: flex;
		align-items: center;
		justify-content: center;

		.quantity-btn {
			width: 50rpx;
			height: 50rpx;
			line-height: 50rpx;
			text-align: center;
			border: 1rpx solid #ddd;
			background: #f5f5f5;

			&.minus {
				border-radius: 6rpx 0 0 6rpx;
			}

			&.plus {
				border-radius: 0 6rpx 6rpx 0;
			}
		}

		.quantity-input {
			width: 80rpx;
			height: 50rpx;
			text-align: center;
			border-top: 1rpx solid #ddd;
			border-bottom: 1rpx solid #ddd;
			margin: 0;
		}
	}

	.popup-footer {
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 1rpx solid #eee;
	}

	.total-price {
		text-align: right;
		padding: 20rpx;
		font-weight: bold;
		color: #f56c6c;
	}

	.button-group {
		display: flex;
		justify-content: flex-end;
		gap: 20rpx; // 按钮之间的间距

		button {
			margin: 0; // 清除默认的按钮margin
			min-width: 160rpx; // 设置最小宽度
		}

		.confirm-btn {
			background-color: #409eff;
			color: #fff;
		}

		.close-btn {
			background-color: #909399;
			color: #fff;
		}
	}
</style>