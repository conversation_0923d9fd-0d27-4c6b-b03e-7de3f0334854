<template>
	<view class="p20">
		<view class="bf p20">
			<view class="lable">
				<text>资产编号：</text>
				{{getNum(assetId)}}
			</view>
		</view>
		<view class="p20 bf opportunityInfo">
			<uni-section title="盘点意见" type="line">
				<view class="text_contatiner">
					<view class="oppo_info">
						<textarea :style="{'borderColor':isfocus? '#5b73f9':'#CCC'}" class="update_input w-full"
							@focus="isfocus=true" @blur="isfocus=false" maxlength="150" @input="inputBusiness" type="textarea"
							v-model="formParams.dealOpinion" placeholder="请输入盘点意见"></textarea>

						<view class="length" :style="{'color':isfocus? '#5b73f9':'#000'}">
							{{businessInfoLength}}/150
						</view>
					</view>
				</view>
			</uni-section>
			<uni-section title="上传图片" type="line">
				<view class="example-body">
					<uni-file-picker v-model="imageValue" limit="9" title="最多选择9张图片" fileMediatype="image" mode="grid"
						@select="handleSelect" @delete="handleDelete"></uni-file-picker>
				</view>
			</uni-section>


		</view>
		<view class="btn" @click="btnPropetyPull">
			提交
		</view>
	</view>
</template>

<script>
	import {
		uploadImage
	} from '../../../../api/system/user'
	import {
		assetInspectionDeal
	} from '../../../../api/task'
	import {
		getAssetNum,
		show
	} from '../../../../common/common_methods'

	export default {
		data() {
			return {
				id: "",
				assetId: "",
				formParams: {
					id: "",
					status: 1,
					attachment: '[]',
					dealOpinion: "" // 意见
				},
				businessInfoLength: 0,
				isfocus: false,
				imageValue: [], // 图片地址
				filePathsList: [],
			}
		},
		onLoad(option) {
			let {
				id,
				assetId
			} = JSON.parse(option.info)
			this.formParams.id = id
			this.assetId = assetId
		},
		methods: {
			async handleSelect(res) {
				// 选择图片
				this.uploadImg(res.tempFiles)

			},
			async uploadImg(tempFiles) {
				// 图片上传
				if (!tempFiles.length) return
				const path = tempFiles.pop()

				let data = {
					name: 'file',
					filePath: path.path
				}
				console.log(path, 'path');
				let {
					fileName,
					url,

				} = await uploadImage(data)
				this.filePathsList.push({
					name: fileName,
					url,
					uid: path.uuid // uid和后台中的uid参数对应上
				})
				this.uploadImg(tempFiles)
			},
			handleDelete(e) {
				// 图片删除
				let {
					uuid
				} = e.tempFile
				const num = this.filePathsList.findIndex((e) => e.uid == uuid)
				this.filePathsList.splice(num, 1)
			},
			async btnPropetyPull() {

				console.log(this.formParams);
				this.formParams.attachment = JSON.stringify(this.filePathsList)
				let res = await assetInspectionDeal(this.formParams)
				console.log(res);
				if (res == 'OK') {
					show("提交成功", 200)
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				}
			},
			inputBusiness({
				detail
			}) {
				this.businessInfoLength = detail.value.length;
				if (this.businessInfoLength >= 150) {
					show('最多不能超过150个字', 1)
				}
			},
			getNum(id) {
				return getAssetNum(id)
			}
		}

	}
</script>

<style lang="scss">
	.btn {
		margin-top: 30rpx;
		padding: 20rpx;
		text-align: center;
		background-color: #5b73f9;
		color: #fff;
		border-radius: 10rpx;
	}

	.text_contatiner {
		.title {}

		.oppo_info {
			margin-top: 20rpx;
			position: relative;

			.update_input {
				border-radius: 10rpx;
				border: 2rpx solid #ccc;
				padding: 10rpx;
				height: 300rpx;
			}

			.length {
				position: absolute;
				z-index: 12312;
				right: 11rpx;
				bottom: 10rpx;
			}
		}
	}

	.opportunityInfo {
		margin-top: 20rpx;
		border-radius: 10rpx;

	}
</style>