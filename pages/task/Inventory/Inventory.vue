<template>
	<view class="p20">
		<view class=" customer_list ">
			<view class="customer_item bf " v-for="(item,index) in inspectorList" :key="index">
				<view class="title just-sbet ">
					<view class="idCard">
						{{ getNum(item.assetInfo.id)}}
					</view>
					<view :class="item.assetInspection.status==0?'tips':'oldTips'">
						{{getInspectionStatus(item.assetInspection.status)}}
					</view>
				</view>
				<view class="customer_info just-sbet">
					<view class="company">
						地址
					</view>
					<view class="clientName">
						{{item.assetInfo.newAddress}}
					</view>
				</view>
				<view class="customer_info just-sbet" v-if="item.assetInspection.status!=0">
					<view class="company">
						盘点意见
					</view>
					<view class="clientName">

						{{item.assetInspection.dealOpinion==null?'':item.assetInspection.dealOpinion}}
					</view>
				</view>
				<view class="customer_btn">
					<view class="toProperty" v-if="item.assetInspection.status==0" @click="topageAll('./property/property',item)">
						盘点资产
					</view>
				
					<view class="toWorkOrder" @click="topageAll('./workOrder/workOrder',item)">
						提交工单
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getAssetInspection
	} from '../../../api/task';
	import {
		getAssetNum,
		topage
	} from '../../../common/common_methods';
	export default {
		data() {
			return {
				inspectorId: "",
				inspectorList: [], // 盘点资产列表
				inspectionStatusOptions: [{
						label: "未处理",
						value: 0
					},
					{
						label: "已完成",
						value: 1
					}
				],
			};
		},
		onLoad(option) {
			this.inspectorId = JSON.parse(option.info)
			// console.log(this.inspectorId);

		},
		onShow() {
			this.getInspector()
		},
		methods: {
			topageAll(url, item) {
				topage(url, {
					id: item.assetInspection.id,
					assetId: item.assetInspection.assetId
				})
			},

			async getInspector() {
				uni.showLoading({
					title: '加载中',
					mask: true
				})

				let res = await getAssetInspection(this.inspectorId)
				// console.log(res);
				this.inspectorList = res
				setTimeout(() => {
					uni.hideLoading();
				}, 1000)
			},
			getNum(id) {
				return getAssetNum(id)
			},
			getInspectionStatus(status) {
				let str = '未知状态'
				this.inspectionStatusOptions.map(item => {
					if (item.value === status) {
						str = item.label
					}
				})
				return str
			}
		}

	}
</script>

<style lang="scss">
	.customer_list {

		.customer_item {
			box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
			padding: 30rpx;
			border-radius: 10rpx;
			margin-bottom: 20rpx;

			.title {
				margin: 10rpx 0;

				.idCard {
					color: #000;
				}

				.tips {
					border-radius: 20rpx;
					padding: 5rpx 20rpx;
					background-color: rgba(249, 0, 0, 0.8);
					color: #fff;
					font-weight: bold;

				}

				.oldTips {
					border-radius: 20rpx;
					font-weight: bold;
					padding: 5rpx 20rpx;
					background-color: rgba(91, 115, 249, 0.1);
					color: #5b73f9;
				}
			}

			.customer_info {
				border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
				padding-bottom: 20rpx;
				margin-top: 40rpx;
				color: #868686;

				.company {
					width: 140rpx;
				}

				.clientName {
					color: #333;
					font-weight: bold;
					flex: 1;
				}
			}

			.customer_btn {
				margin-top: 45rpx;
				// padding: 20rpx;
				// text-align: center;
				// background-color: #5b73f9;
				// color: #fff;
				// border-radius: 10rpx;
				display: grid;
				gap: 20rpx;
				grid-template-columns: repeat(2, 1fr);

				.toProperty {
					padding: 20rpx;
					text-align: center;
					background-color: #5b73f9;
					color: #fff;
					border-radius: 10rpx;
				}

				.toWorkOrder {
					padding: 20rpx;
					text-align: center;
					background-color: #5b73f9;
					color: #fff;
					border-radius: 10rpx;

				}
			}

		}
	}
</style>