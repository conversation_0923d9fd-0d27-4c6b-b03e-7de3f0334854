<template>
	<view class="work_container p20">
		<view class="grid grid-2 serice_list">
			<view class="service_item   b20  just-sbet fl"
				v-for="(item,index) in serviceList" :key="index"  :style="[{'backgroundColor': item.bgcolor}]" @click="sericeBtn(item)" >
				<view class="service_title w-full just-sbet as">
					<view class="name">
						{{item.name}}
					</view>
					<uni-icons color="#fff" custom-prefix="iconfont" type="icon-shenglvehao"></uni-icons>
				</view>
				<view class="serice_info w-full just-sbet as ">
					<uni-icons color="#fff" size="40" custom-prefix="iconfont" :type="item.icon"></uni-icons>
					<view class="enter">
						查看
						<uni-icons color="#fff" type="right"></uni-icons>
					</view>
				</view>

			</view>

		</view>
	</view>
	</view>
	
</template>

<script>
	import {
		show,
		topage
	} from '../../common/common_methods'
	
	import config from '../../config'
    
	export default {
		data() {
			return {
				
				serviceList: [
          {
            name: "待办报修",
            icon: "icon-task",
            link: "../todoTasks/todoTasks",
            bgcolor: "#4c93ed",
            tips: "",
          },
          {
            name: "已办报修",
            icon: "icon-hetong",
            link: "../myDoneTask/myDoneTask",
            bgcolor: "#f15b6c",
            tips: "",
          },
			    {
					  name: "我的流程",
					  icon: "icon-home",
					  link: "../myTasks/index",
					  bgcolor: "#e8482e",
					  tips: "",
				  }
			]
			}
		},
		methods: {
			sericeBtn(item) {
					uni.navigateTo({
						url:item.link
					})
				
			}
		},
		onLoad() {
			console.log(config.baseUrl);
		}
	}
</script>

<style lang="scss">
	.work_container {
		.grid {
			.service_item {
				padding: 30rpx;
				height: 280rpx;
				color: #fff;

				.service_title {
					.name {
						font-size: 34rpx;
					}
				}

				.serice_info {
					align-items: flex-end;
					.enter {
						display: flex;
						justify-content: center;
					}
				}
			}

		}

		.grid-2 {}

		.serice_list {}
	}
</style>