<template>
	<view class="container">
		<!-- <view class="nav bf"> -->
		<view class="flex  align-center p20">

			<uni-easyinput prefixIcon="search" @focus="flag=true" v-model="query.inspectPlanName"
				placeholder="请输入设备编号或者名称">
			</uni-easyinput>

		</view>
		<view class="p20" v-if="flag">
			<view class="btn_big" @click="search">
				搜索
			</view>
		</view>

		<!-- 分段器 -->
		<uni-segmented-control :current="current" :values="items" @clickItem="onClickItem"></uni-segmented-control>
		<view class="">
			<view v-show="current === 0">
				<!-- 选项卡1的内容 -->
			</view>
			<view v-show="current === 1">
				<!-- 选项卡2的内容 -->
			</view>
			<view v-show="current === 2">
				<!-- 选项卡3的内容 -->
			</view>
			<view v-show="current === 3">
				<!-- 选项卡4的内容 -->
			</view>
		</view>


		<!-- </view> -->
		<view class="asset_list p20">
			<view class="p20 bf card asset_info" v-for="item in currentTabList" :key="item.id">
				<view class="just-sbet ">
					<view class="key">
						巡检计划
					</view>
					<view class="">
						{{item.inspectPlanName}}
					</view>
				</view>
				<view class="just-sbet ">
					<view class="key">
						巡检路线
					</view>
					<view class="">
						{{item.inspectRouteName}}
					</view>
				</view>
				<view class="just-sbet ">
					<view class="key">
						开始时间
					</view>
					<view class="">
						{{item.inspectStartTime}}
					</view>
				</view>
				<view class="just-sbet ">
					<view class="key">
						结束时间
					</view>
					<view class="">
						{{item.inspectEndTime}}
					</view>
				</view>
				<view class="just-sbet ">
					<view class="key">
						巡检类型
					</view>
					<view class="">
						{{item.inspectTypeName}}
					</view>
				</view>
				<view class="just-sbet ">
					<view class="key">
					</view>


					<!-- <view class="tips center" @click="hrpRepairReport(item)" >
							报修<uni-icons type="forward" color="#fff"></uni-icons>
						</view> -->


					<view class="tips center" @click="inspectReport" :data-index="item">
						开始巡检<uni-icons type="forward" color="#fff"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 分页栏 -->
		<view class="pagination-container" v-if="tabData[current].list.length > 0">
			<view class="pagination">
				<view class="page-btn" :class="{ disabled: tabData[current].pageNum <= 1 }" @click="handlePrevPage">
					上一页
				</view>
				<view class="page-info">
					第 {{tabData[current].pageNum}} 页 / 共 {{totalPages}} 页
				</view>
				<view class="page-btn" :class="{ disabled: tabData[current].pageNum >= totalPages }" @click="handleNextPage">
					下一页
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		listInspectRecord
	} from '@/api/inspect.js';

	export default {
		data() {
			return {
				allList: [], // 全部数据
				tabData: [
					{ list: [], pageNum: 1 }, // 今天
					{ list: [], pageNum: 1 }, // 明天
					{ list: [], pageNum: 1 }, // 本周
					{ list: [], pageNum: 1 }, // 本月
				],
				pageSize: 10,
				items: ['今天', '明天', '本周', '本月'],
				current: 0,
				flag: false,
				query: {
					inspectPlanName: '',
					// 其它查询条件
				}
			}
		},
		computed: {
			currentTabList() {
				const { list, pageNum } = this.tabData[this.current];
				const start = (pageNum - 1) * this.pageSize;
				return list.slice(start, start + this.pageSize);
			},
			totalPages() {
				const total = this.tabData[this.current].list.length;
				return Math.ceil(total / this.pageSize) || 1;
			}
		},
		onLoad() {
			this.getAllInspectList();
		},
		onShow() {

			//test
			if (this.current == 0) {
				this.getData()
			}


		},

		methods: {
			async getAllInspectList() {
				let res = await listInspectRecord({ ...this.query, pageSize: 99999, pageNum: 1 });
        console.log("res",res);
				if (res && res.rows) {
					// 过滤掉status为1的数据
					this.allList = res.rows.filter(item => item.status !== 1);
					this.splitTabs();
				}
			},
			splitTabs() {
				// 计算今天、明天、本周、本月的时间范围
				const today = new Date();
				today.setHours(0, 0, 0, 0);
				const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
				const startOfWeek = new Date(today);
				startOfWeek.setDate(today.getDate() - (today.getDay() === 0 ? 6 : today.getDay() - 1));
				const endOfWeek = new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000);
				const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
				const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
				const tabLists = [[], [], [], []];
				this.allList.forEach(item => {
					const start = new Date(item.inspectStartTime);
					// 今天
					if (start >= today && start < tomorrow) tabLists[0].push(item);
					// 明天
					if (start >= tomorrow && start < new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000)) tabLists[1].push(item);
					// 本周
					if (start >= startOfWeek && start <= endOfWeek) tabLists[2].push(item);
					// 本月
					if (start >= startOfMonth && start <= endOfMonth) tabLists[3].push(item);
				});
				this.tabData = tabLists.map(list => ({ list, pageNum: 1 }));
			},
			handlePrevPage() {
				if (this.tabData[this.current].pageNum > 1) {
					this.tabData[this.current].pageNum--;
					uni.pageScrollTo({ scrollTop: 0, duration: 300 });
				}
			},
			handleNextPage() {
				if (this.tabData[this.current].pageNum < this.totalPages) {
					this.tabData[this.current].pageNum++;
					uni.pageScrollTo({ scrollTop: 0, duration: 300 });
				}
			},
			onClickItem(e) {
				this.current = e.currentIndex;
				uni.pageScrollTo({ scrollTop: 0, duration: 300 });
			},
			search() {
				this.getAllInspectList();
				this.flag = false;
			},
			inspectReport(item) {
				console.log(item)
				var objStr = item.currentTarget.dataset.index

				this.getInspectLocation(objStr)

			},
			async getInspectLocation(inspectInfo) {

				uni.navigateTo({
					url: '/pages/inspect/inspectPlace?inspectInfo=' +
						encodeURIComponent(JSON.stringify(inspectInfo))
				})


			},

			getData() {
				//进入页面就获取到当前的日期
				const today = new Date();
				today.setHours(0, 0, 0, 0);

				function formatDate(date) {
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');
					const seconds = String(date.getSeconds()).padStart(2, '0');
					return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				}
				this.query.inspectStartTime = formatDate(today);
				this.query.inspectEndTime = formatDate(new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1));

			},



			updateDateRange() {
			  const today = new Date();
			  today.setHours(0, 0, 0, 0); // 今天的开始时间（00:00:00）
			
			  // 格式化日期的函数
			  function formatDate(date) {
			    const year = date.getFullYear();
			    const month = String(date.getMonth() + 1).padStart(2, '0');
			    const day = String(date.getDate()).padStart(2, '0');
			    return `${year}-${month}-${day} 00:00:00`; // 返回日期格式 yyyy-MM-dd 00:00:00
			  }
			
			  // 格式化结束日期的函数，23:59:59
			  function formatEndDate(date) {
			    const year = date.getFullYear();
			    const month = String(date.getMonth() + 1).padStart(2, '0');
			    const day = String(date.getDate()).padStart(2, '0');
			    return `${year}-${month}-${day} 23:59:59`; // 返回结束时间格式 yyyy-MM-dd 23:59:59
			  }
			
			  switch (this.current) {
			    case 0: // 今天
			      this.query.inspectStartTime = formatDate(today); // 今天开始时间
			      this.query.inspectEndTime = formatEndDate(today); // 今天结束时间
			      break;
			    case 1: // 明天
			      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000); // 获取明天的日期
			      this.query.inspectStartTime = formatDate(tomorrow); // 明天的开始时间
			      this.query.inspectEndTime = formatEndDate(tomorrow); // 明天的结束时间
			      break;
			    case 2: // 本周，从周一开始
			      const dayOfWeek = today.getDay(); // 获取今天是周几
			      const offset = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 如果今天是周日，则偏移6天；否则调整为周一
			      const startOfWeek = new Date(today.getTime() - offset * 24 * 60 * 60 * 1000); // 本周的开始日期（周一）
			      const endOfWeek = new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000); // 本周的结束日期（周日）
			      this.query.inspectStartTime = formatDate(startOfWeek); // 本周开始时间
			      this.query.inspectEndTime = formatEndDate(endOfWeek); // 本周结束时间
			      break;
			    case 3: // 本月
			      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1); // 本月的第一天
			      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0); // 本月的最后一天
			      this.query.inspectStartTime = formatDate(startOfMonth); // 本月开始时间
			      this.query.inspectEndTime = formatEndDate(endOfMonth); // 本月结束时间
			      break;
			    default:
			      break;
			  }
			
			  console.log("Start Time:", this.query.inspectStartTime);
			  console.log("End Time:", this.query.inspectEndTime);
			}



		},

	}
</script>

<style lang="scss">
	.annex {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
	}

	.nav {
		position: fixed;
		width: 100%;
		top: 0;
		z-index: 1024;
		box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
	}

	.asset_list {
		margin-top: 50rpx;
		display: grid;
		gap: 20rpx;

		.asset_info {
			display: grid;
			gap: 25rpx;

			.tips {
				color: #fff;
				background: #3296fa;
				padding: 5rpx 10rpx;
				border-radius: 20rpx;
			}

			.key {
				flex-basis: 150rpx;
				margin-right: 30rpx;
			}

		}
	}

	.pagination-container {
	  padding: 20rpx;
	  position: fixed;
	  bottom: 0;
	  left: 0;
	  right: 0;
	  background-color: #fff;
	  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.pagination {
	  display: flex;
	  justify-content: space-between;
	  align-items: center;
	  padding: 20rpx 40rpx;

	  .page-btn {
	    background: #4095E5;
	    color: #fff;
	    padding: 10rpx 30rpx;
	    border-radius: 30rpx;
	    font-size: 28rpx;

	    &.disabled {
	      background: #ccc;
	      opacity: 0.7;
	    }
	  }

	  .page-info {
	    font-size: 28rpx;
	    color: #666;
	  }
	}

	.container {
	  padding-bottom: 120rpx;
	}
</style>