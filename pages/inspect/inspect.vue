<template>
	<view class="work_container p20">
		<view class="grid grid-2 serice_list">
			<view class="service_item   b20  just-sbet fl" :style="[{'backgroundColor': item.bgcolor}]"
				v-for="(item,index) in serviceList" :key="index" @click="sericeBtn(item)">
				<view class="service_title w-full just-sbet as">
					<view class="name">
						{{item.name}}
					</view>
					<uni-icons color="#fff" custom-prefix="iconfont" type="icon-shenglvehao"></uni-icons>
				</view>
				<view class="serice_info w-full just-sbet as ">
					<uni-icons color="#fff" size="40" custom-prefix="iconfont" :type="item.icon"></uni-icons>
					<view class="enter">
						查看
						<uni-icons color="#fff" type="right"></uni-icons>
					</view>
				</view>

			</view>

		</view>
	</view>
</template>

<script>
	import {
		show,
		topage
	} from '../../common/common_methods'
	import {
		listHrpRepairRecord
	} from '@/api/repair';
	import {
		getInspectPlanByQRecode
	} from '../../api/inspect'
	import {
		getInfo,
	} from '../../api/login';
	export default {
		data() {
			return {
				hrpInfo: {
					pageNum: 1,
					pageSize: 10,
					cardNumber: null,
					cardName: null
				},


				serviceList: [
					{
						name: "扫设备码巡检",
						icon: "icon-business_monitor",
						link: "../inspect/inspectScan",
						bgcolor: "#ff4f00",
						tips: "",

					},
          {
            name: "扫地点码巡检",
            icon: "icon-business_monitor",
            link: "../inspect/inspectScan",
            bgcolor: "#22d96e",
            tips: "",

          },
					{
						name: "待办巡检任务",
						icon: "icon-home",
						link: "../inspect/index",
						bgcolor: "#926fa8",
						tips: "",

					},
					{
						name: "已办巡检任务",
						icon: "icon-wuyedangan",
						link: "../inspect/inspectedPlan",
						bgcolor: "#91b5d0",
						tips: "",

					}, {
						name: "过期巡检任务",
						icon: "icon-business_monitor",
						link: "../inspect/overTimeInspect",
						bgcolor: "#6e84ea",
						tips: "",
					},

				]
			}
		},
		onLoad() {

		},
		methods: {

			scan() {

				uni.scanCode({

            scanType: ['barCode', 'qrCode'],
            success: async (res) => {
              // #ifndef MP-DINGTALK
              if (res.scanType !== 'QR_CODE') {
                // uni.navigateBack({ delta: 1 });
                uni.showToast({
                  title: '扫码失败，请重新扫码',
                  icon: 'none',
                  duration: 3000
                });
                return;
              }
              // #endif
						console.log('扫码成功，结果：', res);
						if (res.result.includes(',') && res.result.includes(':')) {
							// 扫码的结果是hrp设备编码
							let arr = res.result.split(',');
							arr = arr[0].split(':');
							this.hrpInfo.cardNumber = arr[1];
							// await this.getHrpRepairList();
							const userInfoResponse = await getInfo();
							console.log("userInfoResponse", userInfoResponse);
							// 调用接口获取巡检计划
							const params = {
								deviceCode: this.hrpInfo.cardNumber, // 传递设备编码
								userId: userInfoResponse.user.userId // 假设用户ID是从当前用户信息获取
							}
							console.log("params", params)
							try {
								const result = await getInspectPlanByQRecode(params);
								console.log('巡检计划1:', result);
								if (result && result.data) {
									console.log("result.data", result.data);
									if (Array.isArray(result.data.inspectPlans) && result.data.inspectPlans.length > 0) {
										// 使用 find 查找符合条件的计划
										const plan = result.data.inspectPlans.find(plan =>
											plan.inspectExcuteType === "111" ||
											plan.inspectExcuteType === "101" ||
											plan.inspectExcuteType === "011" ||
											plan.inspectExcuteType === "001"
										);

										if (plan) {
											const encodedParams = encodeURIComponent(JSON.stringify(params)); // 编码传递的参数
											console.log('result.data.currentInspectPlace.inspectPlaceName', result.data.currentInspectPlace.inspectPlaceName);
											const encodedPlaceName = encodeURIComponent(result.data.currentInspectPlace.inspectPlaceName);
											uni.navigateTo({
												url: `/pages/inspect/inspectScanSuccess?params=${encodedParams}&inspectPlaceName=${encodedPlaceName}`
											});
										} else {
											// 没有符合条件的 inspectExcuteType
											uni.navigateBack({
												delta: 1 // 返回上一级页面
											});
											uni.showToast({
												title: '该巡检计划不可进行小程序扫码，请确认后重试。',
												icon: 'none',
												duration: 3000
											});
										}
									} else {
										// 如果 result.data.inspectPlans 为空数组，则返回初始页面并提示用户
										// uni.navigateBack({
										//   delta: 1 // 返回上一级页面
										// });
										uni.showToast({
											title: '今天内该地点没有巡检计划，如要巡检，请联系管理员添加',
											icon: 'none',
											duration: 3000
										});
									}
								} else {
									// 如果没有 result 或 result.data，直接返回并提示
									uni.navigateBack({
										delta: 1
									});
									uni.showToast({
										title: '获取巡检计划失败，请稍后再试。',
										icon: 'none',
										duration: 3000
									});
								}
							} catch (error) {
								console.error('获取巡检计划失败:', error);
								uni.showToast({
									title: `获取巡检计划失败: ${error.message || '未知错误'}`,
									icon: 'none',
									duration: 3000
								});
							}
						} else {
							// 扫码的结果是消防设备编码
							const deviceCode = res.result; // 获取设备编码（二维码内容）
							console.log("111", deviceCode)
							const userInfoResponse = await getInfo();
							console.log("userInfoResponse", userInfoResponse);
							// 调用接口获取巡检计划
							const params = {
								deviceCode: deviceCode, // 传递设备编码
								userId: userInfoResponse.user.userId // 假设用户ID是从当前用户信息获取
							}
							console.log("params", params)
							try {
								const result = await getInspectPlanByQRecode(params);
								console.log('巡检计划1:', result);
								if (result && result.data) {
									console.log("result.data", result.data);
									if (Array.isArray(result.data.inspectPlans) && result.data.inspectPlans.length > 0) {
										// 使用 find 查找符合条件的计划
										const plan = result.data.inspectPlans.find(plan =>
											plan.inspectExcuteType === "111" ||
											plan.inspectExcuteType === "101" ||
											plan.inspectExcuteType === "011" ||
											plan.inspectExcuteType === "001"
										);

										if (plan) {
											const encodedParams = encodeURIComponent(JSON.stringify(params)); // 编码传递的参数
											console.log('result.data.currentInspectPlace.inspectPlaceName', result.data.currentInspectPlace.inspectPlaceName);
											const encodedPlaceName = encodeURIComponent(result.data.currentInspectPlace.inspectPlaceName);
											uni.navigateTo({
												url: `/pages/inspect/inspectScanSuccess?params=${encodedParams}&inspectPlaceName=${encodedPlaceName}`
											});
										} else {
											// 没有符合条件的 inspectExcuteType
											uni.navigateBack({
												delta: 1 // 返回上一级页面
											});
											uni.showToast({
												title: '该巡检计划不可进行小程序扫码，请确认后重试。',
												icon: 'none',
												duration: 3000
											});
										}
									} else {
										// 如果 result.data.inspectPlans 为空数组，则返回初始页面并提示用户
										// uni.navigateBack({
										//   delta: 1 // 返回上一级页面
										// });
										uni.showToast({
											title: '今天内该地点没有巡检计划，如要巡检，请联系管理员添加',
											icon: 'none',
											duration: 3000
										});
									}
								} else {
									// 如果没有 result 或 result.data，直接返回并提示
									uni.navigateBack({
										delta: 1
									});
									uni.showToast({
										title: '获取巡检计划失败，请稍后再试。',
										icon: 'none',
										duration: 3000
									});
								}
							} catch (error) {
								console.error('获取巡检计划失败:', error);
								// uni.showToast({
								// 	title: `获取巡检计划失败: ${error.message || '未知错误'}`,
								// 	icon: 'none',
								// 	duration: 3000
								// });
							}
						}
					},
					fail: (err) => {
						console.error('扫码失败:', err);
						uni.showToast({
							title: '扫码失败',
							icon: 'none',
							duration: 3000
						});
					}
				})
			},
			// hrpRepairReport(item){
			// 	var objStr = JSON.stringify(item)
			// 	uni.navigateTo({
			// 		url: '/pages/assetDetails/assetDetails?repairInfo='+encodeURIComponent(objStr)
			// 	})
			// },
			// async getHrpRepairList() {
      //   console.log("this.hrpInfo",this.hrpInfo)
			// 	let res = await listHrpRepairRecord(this.hrpInfo);
			// 	if(res ){
			// 		console.log("res",res)
			// 		this.hrpRepairReport(res.rows[0])
			// 	}
      //
			// },
			sericeBtn(item) {
        console.log("item",item);
				if (item.name == "扫地点码巡检" || item.name == "扫设备码巡检") {
					this.scan()
				} else {
					topage(item.link)
				}

				// show("功能还在开发中", 1)
			}
		}
	}
</script>

<style lang="scss">
	.work_container {
		.grid {
			.service_item {
				padding: 30rpx;
				height: 280rpx;
				color: #fff;

				.service_title {
					.name {
						font-size: 34rpx;
					}
				}

				.serice_info {
					align-items: flex-end;

					.enter {
						display: flex;
						justify-content: center;
					}
				}
			}

		}

		.grid-2 {}

		.serice_list {}
	}
</style>