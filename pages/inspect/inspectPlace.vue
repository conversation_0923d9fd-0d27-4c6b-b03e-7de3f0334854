<template>
	<view class="container">

		<view class="asset_list p20">
			<view class="p20 bf card asset_info" v-for="item in inspectLocationList" :key="item.id">
				<view class="just-sbet ">
					<view class="key">
						巡检地点
					</view>
					<view class="">
						{{item.inspectPlaceName}}
					</view>
				</view>
				<view class="just-sbet ">
					<view class="key">
						巡检科室
					</view>
					<view class="">
						{{item.deptName}}
					</view>
				</view>
				<view v-if="item.isDevice" class="just-sbet ">
					<view class="key">
						设备编号
					</view>
					<view class="">
						{{item.deviceId}}
					</view>
				</view>
				<view class="just-sbet ">
					<view class="key">
						详情
					</view>
					<view class="">
						{{item.detail}}
					</view>
				</view>

				<view class="just-sbet ">
					<view class="key">
					</view>
					<!-- <view class="tips center" @click="hrpRepairReport(item)" >
							报修<uni-icons type="forward" color="#fff"></uni-icons>
						</view> -->
					<view class="tips center" @click="inspectLocationReport" :data-index="item">
						{{item.status?"已完成":"巡检"}}<uni-icons type="forward" color="#fff"></uni-icons>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		listInspectRecord,
		listInspectLocation,
		InspectedPlan,
		getInspectPlanByQRecode
	} from '@/api/inspect.js';
	import {
		getInfo,
	} from '../../api/login';

	export default {
		data() {
			return {

				propertyIdInput: "", // 输入的id
				flag: false, // 搜索按钮是否显示
				inspectInfo: {},
				inspectLocationList: [],
				query: {
					inspectPlanId: 0,
					status: 1
				}
			}
		},
		async onLoad(option) {
			console.log("op", option)

			if (option.inspectInfo) {

				// 对传递的 JSON 字符串进行解码

				var objStr = decodeURIComponent(option.inspectInfo);
				// 将 JSON 字符串转换回对象
				console.log('objStr:' + objStr)
				var obj = JSON.parse(objStr)
				console.log("obj", obj)
				this.inspectInfo = obj
				this.query.inspectPlanId = this.inspectInfo.inspectPlanId; // 设置 query 的 inspectPlanId
				console.log('this.query.inspectPlanId:' + this.query.inspectPlanId)
			}


		},
		async onShow(option) {
			if (!this.query.inspectPlanId) {
				console.error('inspectPlanId is not set');
				return
			}
			this.query.inspectPlanId = this.inspectInfo.inspectPlanId

			let res = await listInspectLocation(this.query);
			console.log("res", res)
			if (res) {
				console.log(res.rows)
				this.inspectLocationList = res.rows
				if (this.inspectInfo.status) {
					return
				}
				var j = 0
				for (var i = 0; i < this.inspectLocationList.length; i++) {
					if (this.inspectLocationList[i].status) {
						j++
					}
				}

				if (j && j == this.inspectLocationList.length) {
					var query = this.inspectInfo
					query.status = 1;
					uni.showModal({
						title: '该巡检已完成请确认',

						success: async function(res) {
							if (res.confirm) {
								//确认
								console.log("query",query)
								let res = await InspectedPlan(query)
								if (res) {
									console.log(query)
									uni.navigateBack({
										delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
									});
								}

								// 用户点击了确定按钮的相关逻辑可以放在这里
							} else if (res.cancel) {

								// 用户点击了取消按钮的相关逻辑可以放在这里
							}
						}
					});
				}
			}

		},

		methods: {
			async inspectLocationReport(item) {

				console.log("this.inspectInfo", this.inspectInfo)

				this.inspectPlantype = this.inspectInfo.inspectExcuteType
				console.log("this.inspectPlantype", this.inspectPlantype)

				// item.currentTarget.dataset.index.formModel = "";
				// var objStr = JSON.stringify(item.currentTarget.dataset.index);

				// if (item.currentTarget.dataset.index.status) {
				// 	uni.navigateTo({
				// 		url: '/pages/inspect/inspectPlaceRecord?inspectDetails=' + encodeURIComponent(objStr) +
				// 			'&inspectInfo=' + encodeURIComponent(JSON.stringify(this.inspectInfo))
				// 	});
				// } else {

				// }
				const currentTime = new Date();
				
				// 处理 startTime 和 endTime 格式，将空格替换为 T，确保符合 ISO 8601 格式
				let startTimeStr = this.inspectInfo.inspectStartTime;
				let endTimeStr = this.inspectInfo.inspectEndTime;
				
				startTimeStr = startTimeStr.replace(' ', 'T');  // 将空格替换为 T
				endTimeStr = endTimeStr.replace(' ', 'T');      // 将空格替换为 T
				
				// 将时间字符串转换为 Date 对象
				const startTime = new Date(startTimeStr);
				const endTime = new Date(endTimeStr);
				
				console.log('startTime', startTime);
				console.log('endTime', endTime);
				
				// 判断时间是否有效
				if (isNaN(startTime) || isNaN(endTime)) {
				    console.error('Invalid startTime or endTime');
				    return;
				}
				
				// 先进行status判断，符合条件后进行跳转
				item.currentTarget.dataset.index.formModel = "";
				var objStr = JSON.stringify(item.currentTarget.dataset.index);
				
				// 判断status是否符合条件，最先进行判断
				if (item.currentTarget.dataset.index.status) {
				    uni.navigateTo({
				        url: '/pages/inspect/inspectPlaceRecord?inspectDetails=' + encodeURIComponent(objStr) +
				            '&inspectInfo=' + encodeURIComponent(JSON.stringify(this.inspectInfo))
				    });
				    return; // 跳转后直接返回，不继续执行后续的时间判断
				}

				// 如果status不符合条件，再进行时间判断
				if (currentTime < startTime) {
				    // 如果当前时间早于巡检开始时间
				    uni.showToast({
				        title: '巡检计划尚未开始',
				        icon: 'none',
				        duration: 2000
				    });
				} else if (currentTime > endTime) {
				    // 如果当前时间晚于巡检结束时间
				    uni.showToast({
				        title: '巡检计划已过期',
				        icon: 'none',
				        duration: 2000
				    });
				} else {
            if (this.inspectInfo.statusId == 0){
              uni.showToast({
                title: '该页面仅用于展示，不可操作',
                icon: 'none',
                duration: 2000
              })
              return;
            }
				    // 如果当前时间在巡检时间范围内，继续执行后续逻辑
				    if (['000', '001', '101'].includes(this.inspectPlantype)) {
				        uni.showToast({
				            title: '该巡检地点计划不支持小程序巡检',
				            icon: 'none', // 不显示图标
				            duration: 2000 // 显示时长
				        });
				    } else if (['010', '011', '110', '111'].includes(this.inspectPlantype)) {
				        uni.navigateTo({
				            url: '/pages/inspect/inspectPlaceDetails?inspectDetails=' + encodeURIComponent(objStr) +
				                '&inspectInfo=' + encodeURIComponent(JSON.stringify(this.inspectInfo))
				        });
				    } else {
				        uni.showToast({
				            title: '该巡检地点计划仅支持网页端巡检',
				            icon: 'none', // 不显示图标
				            duration: 2000 // 显示时长
				        });
				    }
				}
			},


		},

	}
</script>

<style lang="scss">
	.annex {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
	}

	.nav {
		position: fixed;
		width: 100%;
		top: 0;
		z-index: 1024;
		box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
	}

	.asset_list {
		margin-top: 135rpx;
		display: grid;
		gap: 20rpx;

		.asset_info {
			display: grid;
			gap: 25rpx;

			.tips {
				color: #fff;
				background: #3296fa;
				padding: 5rpx 10rpx;
				border-radius: 20rpx;
			}

			.key {
				flex-basis: 150rpx;
				margin-right: 30rpx;
			}

		}
	}
</style>