<template>
	<view class="nav bf">
		<view class="p20 bf card asset_info">
			<view v-if="inspectPlanDetails" class="just-sbet ">
				<view class="key">
					巡检地点
				</view>
				<view class="">
					{{inspectPlanDetails.inspectPlaceName}}
				</view>
			</view>
			<view v-if="inspectPlanDetails" class="just-sbet ">
				<view class="key">
					巡检科室
				</view>
				<view class="">
					{{inspectPlanDetails.deptName}}
				</view>
			</view>
			<view v-if="inspectPlanDetails" class="just-sbet ">
				<view class="key">
					设备编号
				</view>
				<view class="">
					{{inspectPlanDetails.deviceId}}
				</view>
			</view>
			<view v-if="inspectPlanDetails" class="just-sbet ">
				<view class="key">
					详情
				</view>
				<view class="">
					{{inspectPlanDetails.detail}}
				</view>
			</view>
		</view>

		<uni-forms>
			<DynamicForm ref="localDynamicFormRef" :formModel="formModel" :formContent="formContent" @getData="getData">
			</DynamicForm>
			<uni-forms-item class="flex  align-center p20" label-width="80px" label="巡检意见" name="detail">
				<uni-easyinput type="text" v-model="formData.detail" placeholder="请输入巡检意见" />
			</uni-forms-item>
		</uni-forms>
		
		<!-- 巡检结果按钮组 -->
		  <uni-forms-item class="flex align-center p20" label-width="80px" label="巡检结果" name="inspectResultType">
		      <!-- #ifndef APP-PLUS -->
		      <view class="button-grid">
		        <button 
		          v-for="item in inspectResults" 
		          :key="item.value"
		          :class="['button', { 'active': formData.inspectResultType === item.value }]"
		          @click="selectInspectResult(item.value)">
		          {{ item.label }}
		        </button>
		      </view>
		      <!-- #endif -->
		      
		      <!-- #ifdef APP-PLUS -->
		      <view class="app-button-grid">
		        <view 
		          v-for="item in inspectResults" 
		          :key="item.value"
		          :style="{
		            backgroundColor: formData.inspectResultType === item.value ? '#3b83ef' : '#ffffff',
		            color: formData.inspectResultType === item.value ? '#ffffff' : '#333333',
		            borderColor: formData.inspectResultType === item.value ? '#3b83ef' : '#e0e0e0'
		          }"
		          class="app-button"
		          @tap="selectInspectResult(item.value)">
		          {{ item.label }}
		        </view>
		      </view>
		      <!-- #endif -->
		    </uni-forms-item>
			
		<uni-section title="上传录音" type="line">
			<view class="example-body p20 buttonRecode">
				<button @touchstart="touchstart" @touchend="touchend">按住说话 </button>
				<button @tap="playVoice" style="color: #3b83ef">播放录音</button>
				<button @click="uploadAudio" style="color: #3b83ef">上传录音</button>
			</view>
		</uni-section>
		<uni-section title="上传视频" type="line">
			<view>
				<uni-file-picker ref="videoPicker" v-model="videoValue" limit="1" title="最多选择1个视频" fileMediatype="video"
					mode="grid" @select="handleVideoSelect" @success="handleVideoSuccess"
					@delete="handleVideoDelete"></uni-file-picker>
			</view>
		</uni-section>
		<uni-section title="上传图片" type="line">
			<view class="">
				<uni-file-picker ref="filePicker" v-model="imageValue" limit="5" title="最多选择5张图片" fileMediatype="image"
					mode="grid" @select="handleSelect" @success="handleSuccess"
					@delete="handleDelete"></uni-file-picker>
			</view>
		</uni-section>
		<button type="primary" @click="toReportForRepairs(inspectDetails)"
			style="width: 100px; margin-left: 10px;margin-bottom: 10px">点击报修</button>
		<button class="btn_big" :disabled="inspectInfo.status > 0" @click="submitForm">确定</button>
		<view style="width: 100%; padding: 20rpx; display: flex; justify-content: space-between;" v-if="isVideoInspect">
			<navigator
				:url="`/pages/inspect/inspectVideo?deviceId=${encodeURIComponent(this.inspectDetails.deviceId)}&listLength=${5-(formData.inspectImageList.length)}`">
				<button class="btn_big">跳转视频巡检</button>
			</navigator>
			<button class="btn_big" @click="getImage">从监控中截屏</button>
		</view>

	</view>

</template>

<script>
	import DynamicForm from '@/components/parser/DynamicForm'
	import {
		uploadImage,
		uploadAudio,
		uploadVideo
	} from '@/api/system/user'
	import {
		addInspectRecord,
		getVideoUrl,
		getVideoImage,
		getFormByInspectId,
		addInspectPlanScan,
		getInspectPlanByQRecode,
		getInspectUser
	} from '@/api/inspect';
	import {
		getToken
	} from '@/utils/auth'
	import {
		getInfo,
		login
	} from '../../api/login';
	import {
		getCard
	} from "../../api/HRP/card";

	// #ifdef MP-DINGTALK
  if (dd.canIUse('getRecorderManager')) {     // 端上支持
    const recorderManager = dd.getRecorderManager()
  } else {     // 端上不支持
    dd.alert({ content: '请升级钉钉版本至7.0.10以支持录音功能' })
  }

	//#endif

	//目前支持App端 支持录音和播放录音，钉钉小程序因组件问题需另外考虑
	// #ifndef MP-DINGTALK
	const recorderManager = uni.getRecorderManager();
	const innerAudioContext = uni.createInnerAudioContext();
	innerAudioContext.autoplay = true;
	// #endif

	export default {
		data() {
			return {
				queryFormByInspectId: {
					inspectTypeId: 0,
				},
				isLoading: false,
				formData: {
					inspectResultType: 1,
					inspectResultDetail: '正常',
					//图片地址列表
					inspectImageList: [],
					// 音频地址列表
					inspectAudioList: [],
					// 视频地址列表
					inspectVedioList: [],
					//需要提交的自定义表单数据
					formContent: null,
					detail: ''
				},
				//添加视频双向绑定对象
				videoValue: [],
				imageValue: [],
				inspectDetails: null,
				inspectPlanDetails: null,
				inspectInfo: {},
				realPlayer: null,
				src: null,
				isVideoInspect: false,
				//自定义表单JSON对象
				formModel: {},
				//自定义表单数据动态保存对象
				formContent: {},
				// 保存的录音地址
				voicePath: '',
				inspectResults: [{
						label: '正常',
						value: 1
					},
					{
						label: '轻微故障',
						value: 2
					},
					{
						label: '中等故障',
						value: 3
					},
					{
						label: '重度故障',
						value: 4
					},
					{
						label: '完全损坏',
						value: 5
					}
				],
        userInfoMap: new Map() // 用于存储每个计划的用户信息
			}
		},
		components: {
			DynamicForm
		},
		onHide() {
			// #ifndef MP-DINGTALK
			innerAudioContext.stop()
			// #endif
		},
		async onLoad(options) {
			// 显示加载提示
			this.isLoading = true;
			uni.showLoading({
				title: '加载中...'
			});

			try {
				// #ifndef MP-DINGTALK
				let self = this;
				//设置录屏停止事件，在录音停止事件中获取录音文件路径
				recorderManager.onStop(function(res) {
					console.log('recorder stop' + JSON.stringify(res));
					self.voicePath = res.tempFilePath;
				});
				// #endif
        // #ifdef MP-DINGTALK
        recorderManager.onStop((res) => {
          console.log('recorder stop' + JSON.stringify(res));
          this.voicePath = res.tempFilePath;
        });
        // #endif

				if (options.params) {
					const params = JSON.parse(decodeURIComponent(options.params));
					console.log('接收到的 params:', params);
					this.params = params; // 将参数赋值给页面的数据
				}

				// 调用 getScanInspectPlan 获取巡检计划数据
				await this.getScanInspectPlan();
				// 确保获取到数据后再调用 getFormByInspectId
				await this.getFormByInspectId();
				console.log("ref", this.resForm);
				//根据表单类型编号获取自定义表单定义JSON
				this.formModel = JSON.parse(this.resForm.data.formContent)
				console.log("this.formModel", this.formModel)
				console.log("this.formData", this.formData)
				// getVideoUrl({
				// 	channelId: this.inspectDetails.deviceId
				// }).then(res => {
				// 	if (res) {
				// 		this.isVideoInspect = true
				// 	}
				// }).catch(error => {
				// 	if (error) {
				// 		this.isVideoInspect = false
				// 	}
				// })
				await this.getScanInspectPlan();
				await this.getInspectUser()
			} catch (error) {
				console.error('数据加载失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none',
					duration: 2000
				});
			} finally {
				// 隐藏加载提示
				this.isLoading = false;
				uni.hideLoading();
			}
		},
		methods: {
			async getScanInspectPlan() {

				if (this.params) {
					console.log("使用传递过来的 params:", this.params);
					try {
						const result = await getInspectPlanByQRecode(this.params); // 使用 this.params
						console.log('巡检计划2:', result); // 确保这里能够打印结果
						this.data = result.data;
					} catch (error) {
						console.error('获取巡检计划失败:', error);
					}
				} else {
					console.log('没有传递有效的 params');
				}
				
			},
      async getInspectUser() {

        // 检查 this.data 和 inspectPlans 是否存在
        if (this.data && this.data.inspectPlans) {
          // 使用 map 获取所有的 inspectPlanId
          const planIds = this.data.inspectPlans.map(plan => plan.inspectPlanId);
          console.log("所有计划的 ID:", planIds);

          // 对每个计划 ID 分别处理
          for (const planId of planIds) {
            console.log("当前处理的计划 ID:", planId);
            const res = await getInspectUser(planId);
            console.log(`计划 ${planId} 的用户信息:`, res);
            // 将用户信息存储到 Map 中
            this.userInfoMap.set(planId, {
              inspectUserId: res.data.inspectUserId,
              inspectUserName: res.data.inspectUserName
            });
          }
        } else {
          console.log("没有找到巡检计划数据");
        }
      },
			async getFormByInspectId() {
				console.log("获取到的巡检计划数据:", this.data);
				this.inspectPlanDetails = this.data.currentInspectPlace
				if (this.data && this.data.inspectPlans && Array.isArray(this.data.inspectPlans) && this.data
					.inspectPlans.length > 0) {
					// 使用 for...of 来遍历 inspectPlans 数组，并确保每次异步操作完成后再继续
					for (const plan of this.data.inspectPlans) {
						console.log('巡检计划的 inspectTypeId:', plan.inspectTypeId);

						// 处理每个 plan 的 inspectTypeId
						this.queryFormByInspectId.inspectTypeId = plan.inspectTypeId;
						// 获取 inspectRoute 和 list4RoutePlaces
						const list4RoutePlaces = plan.inspectRoute?.list4RoutePlaces;

						if (Array.isArray(list4RoutePlaces) && list4RoutePlaces.length > 0) {
							let tempFormData = []; // 使用临时数组存储
							// 遍历 list4RoutePlaces 数组
							for (const inspectRoute of list4RoutePlaces) {
								console.log('处理的 inspectPlace:', inspectRoute);
								this.inspectDetails = inspectRoute.inspectPlace;
								console.log("this.inspectDetails", this.inspectDetails)
								let formItem = Object.assign({}, this.inspectDetails);
								delete formItem.detail; // 删除 detail 属性
								// 将处理后的对象添加到临时数组中
								tempFormData.push(formItem);
							}
							// 保持原有的 formData 基础属性
							const baseFormData = {
								inspectResultType: this.formData.inspectResultType,
								inspectResultDetail: this.formData.inspectResultDetail,
								inspectImageList: this.formData.inspectImageList,
								inspectAudioList: this.formData.inspectAudioList,
								inspectVedioList: this.formData.inspectVedioList,
								formContent: this.formData.formContent,
								detail: this.formData.detail
							};
							// 合并数组
							this.formData = [...tempFormData];
							// 恢复基础属性
							Object.assign(this.formData, baseFormData);
						}

						try {
							// 等待每次异步操作完成
							let resForm = await getFormByInspectId(this.queryFormByInspectId);
							console.log('获取的表单数据:', resForm);
							this.resForm = resForm;
							// 你可以继续处理获取到的表单数据
						} catch (error) {
							console.error('获取表单数据失败:', error);
						}
					}
				} else {
					console.log("没有找到有效的巡检计划数据");
				}
			},
			
			// 选择巡检结果类型并更新详细描述
			    selectInspectResult(value) {
			      // 直接更新值
			      this.formData.inspectResultType = value;
			      
			      const selectedResult = this.inspectResults.find(item => item.value === value);
			      if (selectedResult) {
			        this.formData.inspectResultDetail = selectedResult.label;
			        // 强制更新视图
			        this.$forceUpdate();
			        console.log('巡检结果选中的值:', this.formData.inspectResultType);
			        console.log('巡检结果详情:', this.formData.inspectResultDetail);
			      }
			    },
			// 上传录音

			touchstart() {
				uni.showLoading({
					title: "说话中"
				})
				// 开始录音
				// #ifndef MP-DINGTALK
				recorderManager.start({
					format: 'PCM' // 音频格式，有效值 aac/mp3
				});
				// #endif
        // #ifdef MP-DINGTALK
        recorderManager.start({
          format: 'PCM' // 音频格式，有效值 aac/mp3
        });
        // #endif
			},
			touchend() {
				uni.hideLoading();
				// 结束录音
				// #ifndef MP-DINGTALK
				recorderManager.stop();
				// #endif
        // #ifdef MP-DINGTALK
        recorderManager.stop();
        // #endif
			},
			playVoice() {
				console.log('播放录音');
				if (this.voicePath) {
					// #ifndef MP-DINGTALK
					innerAudioContext.src = this.voicePath;
					innerAudioContext.play();
					// #endif
				}
			},
			async uploadAudio() {
			    if (!this.voicePath) {
			        uni.showToast({
			            title: "没有录音文件",
			            icon: "none"
			        });
			        return;
			    }
			
			    let data = {
			        name: 'file',
			        filePath: this.voicePath
			    };
			    console.log("当前录音文件路径:", this.voicePath); // 确保录音文件路径有效
			    try {
			        let res = await uploadAudio(data); // 调用上传接口
			        console.log("上传返回的结果:", res); // 查看返回的数据结构，确保 res.url 存在
			
			        if (res.code === 200) {
			            if (!Array.isArray(this.formData.inspectAudioList)) {
			                this.formData.inspectAudioList = []; // 初始化为空数组
			            }
			            const AudioItem = {
			                inspectMediaId: null,
			                inspectPlaceRecordId: null,
			                inspectAudioUrl: res.url,
			                delTag: 0
			            };
			            this.formData.inspectAudioList.push(AudioItem); // 将返回的 URL 添加到列表中
			            console.log("录音上传成功", this.formData.inspectAudioList);
			            uni.showToast({
			                title: "录音上传成功",
			                icon: "success"
			            });
			        } else {
			            uni.showToast({
			                title: "上传失败",
			                icon: "none"
			            });
			        }
			    } catch (error) {
			        console.error("上传录音出错:", error);
			        uni.showToast({
			            title: "上传失败",
			            icon: "none"
			        });
			    }
			},

			// 上传视频

			async handleVideoSelect(res) {
				// 选择视频
				this.uploadVideo(res.tempFiles);
			},

			async handleVideoSuccess(e) {
				console.log(e);
			},

			async uploadVideo(tempFiles) {
				if (!tempFiles.length) {
					uni.showToast({
						title: "请先选择视频",
						icon: "none"
					});
					return;
				} // 检查是否选择了文件
				const path = tempFiles.pop(); // 获取最后一个选中的文件
				let data = {
					name: 'file',
					filePath: path.path
				};

				try {
					let res = await uploadVideo(data); // 调用上传接口
					console.log("视频上传成功",res)
					if (res.code === 200) {
						// 确保 inspectVedioList 是一个数组
						if (!Array.isArray(this.formData.inspectVedioList)) {
							this.formData.inspectVedioList = []; // 初始化为空数组
						}
						const VideoItem = {
							inspectMediaId:null, 
							inspectPlaceRecordId: null, 
							inspectVideoUrl: res.url, 
							delTag: 0 
						};
						this.formData.inspectVedioList.push(VideoItem); // 将返回的 URL 添加到列表中
						console.log("this.formData.inspectVedioList", this.formData.inspectVedioList)
						
						uni.showToast({
							title: "视频上传成功",
							icon: "success"
						});
					} else {
						uni.showToast({
							title: "上传失败",
							icon: "none"
						});
					}
				} catch (error) {
					console.error("上传视频出错:", error);
					uni.showToast({
						title: "上传失败",
						icon: "none"
					});
				}
			},

			handleVideoDelete(e) {
				// 视频删除
				let {
					url
				} = e.tempFile;
				const num = this.formData.inspectVedioList.findIndex((e) => e == url);
				this.formData.inspectVedioList.splice(num, 1);
			},

			// 上传图片

			async handleSelect(res) {
				// 选择并上传图片
				this.uploadImg(res.tempFiles)
			},
			async handleSuccess(e) {
				console.log(e)
			},
			async uploadImg(tempFiles) {
			    if (!tempFiles.length) return;
			    for (let i = 0; i < tempFiles.length; i++) {
			        const path = tempFiles[i];
			        let data = {
			            name: 'file',
			            filePath: path.path
			        };
			        try {
			            let res = await uploadImage(data);  // 调用上传接口
			            console.log("图片上传成功", res);
			
			            if (res.code === 200) {
			                if (!Array.isArray(this.formData.inspectImageList)) {
			                    this.formData.inspectImageList = []; // 初始化为空数组
			                }
			                const ImageItem = {
			                    inspectMediaId: null,
			                    inspectPlaceRecordId: null,
			                    inspectImageUrl: res.url,
			                    delTag: 0
			                };
			                this.formData.inspectImageList.push(ImageItem); // 将返回的 URL 添加到列表中
			                console.log("上传后的图片 URL 列表：", this.formData.inspectImageList);
			                uni.showToast({
			                    title: "图片上传成功",
			                    icon: "success"
			                });
			            } else {
			                uni.showToast({
			                    title: "上传失败",
			                    icon: "none"
			                });
			            }
			        } catch (error) {
			            console.error("图片上传失败", error);
			            uni.showToast({
			                title: "上传失败",
			                icon: "none"
			            });
			        }
			    }
			},
			handleDelete(e) {
				// 图片删除
				let {
					url
				} = e.tempFile
				const num = this.formData.inspectImageList.findIndex((e) => e == url)
				this.formData.inspectImageList.splice(num, 1)
			},

			getData(localFormContent) {
				// console.log("localFormContent", localFormContent)
				this.formContent = localFormContent
			},
			fetchData() {
				this.$refs.localDynamicFormRef.getData()
			},
			async submitForm() {
				console.log("submitForm 被触发");
				this.fetchData();
				console.log("执行到这里");
				console.log("this.formData22", this.formData);
				console.log(this.data)
				if (Array.isArray(this.data.inspectPlans) && this.data.inspectPlans.length > 0) {
					this.data.inspectPlans.forEach(plan => {
            // 从 Map 中获取对应计划的用户信息
            const userInfo = this.userInfoMap.get(plan.inspectPlanId) || {};
						if (Array.isArray(plan.inspectRoute.list4RoutePlaces) && plan.inspectRoute
							.list4RoutePlaces.length > 0) {
							plan.inspectRoute.list4RoutePlaces.forEach(place => {
								let formItem = {}; // 创建一个新对象，用于存储每个巡检地点的表单数据
								// 填充 formItem 的属性
								formItem.formContent = JSON.stringify(this.formContent); // 假设 formContent 是一个 JSON 对象
								formItem.inspectPlaceId = plan.currentInspectPlace.inspectPlaceId;
								console.log("formItem1", formItem);
								formItem.inspectPlaceName = plan.currentInspectPlace.inspectPlaceName;
								console.log("formItem2", formItem);
								formItem.inspectPlanId = plan.inspectPlanId;
								console.log("formItem3", formItem);
								formItem.inspectPlanName = plan.inspectPlanName;
								formItem.inspectTypeId = plan.inspectTypeId;
								formItem.inspectTypeName = plan.inspectTypeName;
                formItem.inspectUserId = userInfo.inspectUserId;
                formItem.inspectUserName = userInfo.inspectUserName;
								formItem.inspectRouteId = plan.inspectRouteId;
								formItem.inspectRouteName = plan.inspectRouteName;
								formItem.isDevice = plan.currentInspectPlace.isDevice;
								formItem.deviceId = plan.currentInspectPlace.deviceId;
								formItem.status = 1;
								formItem.inspectResultType = this.formData.inspectResultType;
								formItem.inspectResultDetail = this.formData.inspectResultDetail;
								formItem.longitude = "56.2737"; // 示例经度
								formItem.latitude = "-66.6315"; // 示例纬度
								console.log("formItem4", formItem);
								formItem.inspectVedioList = this.formData.inspectVedioList; // 确保每个 formItem 有独立的 video list
								console.log("formItem5", formItem);
								formItem.inspectImageList = this.formData.inspectImageList;
								console.log("formItem6", formItem);
								formItem.inspectAudioList = this.formData.inspectAudioList;
								console.log("formItem7", formItem);
								// 如果有 detail 属性，添加到 formItem
								if (place.inspectPlace && place.inspectPlace.detail) {
									formItem.detail = place.inspectPlace.detail;
								}

								// 将每个 formItem 添加到 this.formData 数组中
								this.formData.push(formItem);
							});
						}
					});
				}

				// 基于多个字段去重
				this.formData = this.formData.filter((value, index, self) =>
					index === self.findIndex((t) => 
						t.inspectPlaceId === value.inspectPlaceId && 
						t.inspectPlanId === value.inspectPlanId
					)
				);
				// 过滤掉包含 formModel 的数组元素
				this.formData = this.formData.filter(item => !item.formModel);

				console.log("去重后的 this.formData:", this.formData);






				// console.log("this.formData19", this.formData);
				// 调用 addInspectPlan 
				try {
					await this.addInspectPlanScan();
				} catch (error) {
					console.error("提交巡检计划时出错:", error);
				}
			},
			// async addInspectRecord() {
			// 	let res = await addInspectRecord(this.formData);
			//      console.log("this.formData",this.formData)
			// 	if (res) {
			// 		uni.navigateBack({
			// 			delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
			// 		});
			// 	}
			// },
			async addInspectPlanScan() {
				console.log("this.formData", this.formData);
        const filteredFormData = this.formData.filter(item =>
            item.deviceId == this.params.deviceCode
        );
        console.log("过滤后的 formData:", filteredFormData);
				try {
					console.log("准备调用 addInspectPlanScan");
					let res = await addInspectPlanScan(filteredFormData);
					console.log("addInspectPlanScan 返回的结果:", res); // 如果返回正常，应该会打印
					if (res) {
						uni.navigateBack({
							delta: 2 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
						});
						uni.showToast({
							title: '该巡检地点计划已完成',
							icon: 'none',
							duration: 3000
						});
					} else {
						console.log("addInspectPlanScan 返回了无效的结果");
					}
				} catch (error) {
					console.error("调用 addInspectPlanScan 发生错误:", error); // 捕获 addInspectPlanScan 的错误
					uni.showToast({
						title: "提交失败，请重试",
						icon: "none"
					});
				}
			},
			previewImage(attachment, index = 1) {
				// 预览图片
				attachment = JSON.parse(attachment).map((item) => {
					return item.url
				})
				uni.previewImage({
					urls: attachment,
					current: index
				})
				console.log(attachment, 'attachment');
			},
			async toReportForRepairs(inspectDetails) {
				try {
					// 根据 inspectDetails.deviceType 判断跳转的页面
					switch (inspectDetails.deviceType) {
						case 1: // 如果是 assetDetails 页面，调用 getCard 获取详细信息
							const cardResponse = await getCard(inspectDetails.deviceId);
							const cardData = cardResponse.data;

							// 将 getCard 查询到的数据赋值到 inspectDetails
							this.inspectDetails.recordTitle = cardData.cardName + '报修';
							this.inspectDetails.deviceName = cardData.cardName;
							this.inspectDetails.deviceCode = cardData.cardNumber;
							this.inspectDetails.deviceUseDeptName = cardData.deptName;
							this.inspectDetails.deviceUseDeptId = cardData.deptCode;
							this.inspectDetails.deviceLocation = inspectDetails.inspectPlaceName;
							this.inspectDetails.cardNumber = cardData.cardNumber;
							this.inspectDetails.deviceTypeOne = inspectDetails.deviceType; // 设备类型一
							this.inspectDetails.deviceTypeOneName = 'hrp设备';
							this.inspectDetails.deviceTypeTwo = cardData.class1Id;
							this.inspectDetails.deviceTypeTwoName = cardData.class1Name;
							this.inspectDetails.deviceTypeThree = cardData.class2Id;
							this.inspectDetails.deviceTypeThreeName = cardData.class2Name;
							this.inspectDetails.deviceTypeFour = cardData.class3Id;
							this.inspectDetails.deviceTypeFourName = cardData.class3Name;
							this.inspectDetails.repairTell = '';
							this.inspectDetails.repairName = '';
							this.inspectDetails.repairCostMoney = 0;

							// 获取用户信息
							const userInfoResponse = await getInfo();
							this.inspectDetails.reportName = userInfoResponse.user.userName;
							this.inspectDetails.reportTell = userInfoResponse.user.phonenumber;

							// 编码 inspectDetails 数据为 URL 参数
							const inspectDetailsString = encodeURIComponent(JSON.stringify(this.inspectDetails));

							// 跳转到 assetDetails 页面
							uni.navigateTo({
								url: `/pages/assetDetails/assetDetails?inspectDetails=${inspectDetailsString}`,
							});
							break;
						case 2:
							// 获取用户信息
							const userInfoResponseForOther = await getInfo();
							this.inspectDetails.reportName = userInfoResponseForOther.user.userName;
							this.inspectDetails.reportTell = userInfoResponseForOther.user.phonenumber;

							uni.navigateTo({
								url: `/pages/other/index?inspectDetails=${encodeURIComponent(JSON.stringify(inspectDetails))}`,
							});
							break;

						case 3:
							uni.navigateTo({
								url: `/pages/dahua/index?inspectDetails=${encodeURIComponent(JSON.stringify(inspectDetails))}`,
							});
							break;

						case 4:
							uni.navigateTo({
								url: `/pages/huawei/index?inspectDetails=${encodeURIComponent(JSON.stringify(inspectDetails))}`,
							});
							break;

						default:
							console.warn('未知的设备类型');
					}
				} catch (error) {
					console.error('数据加载失败:', error);
				}
			},
			getValue(data) {
				const imageList = [JSON.parse(data.imageList)]
				if (typeof imageList[0] === 'string') {
					this.formData.inspectImageList.push(decodeURIComponent(decodeURIComponent(imageList[0])))
				} else {
					imageList[0].forEach(item => {
						this.formData.inspectImageList.push(decodeURIComponent(decodeURIComponent(item)))
					})
				}
				console.log(this.formData.inspectImageList);
				this.formData.inspectImageList.forEach(item => {
					let fileObj = {
						url: item,
						extname: 'png',
						name: 'shuijiao.png'
					}
					this.imageValue.push(fileObj)
				})
			},
			async getImage() {
				console.log(this.formData.inspectImageList);
				console.log(this.imageValue);
				if (this.formData.inspectImageList.length >= 5) {
					uni.showToast({
						title: "图片已达最大数量"
					})
					return
				}
				uni.showLoading({
					title: "获取图像中"
				})
				console.log('getVideoImage' + this.formData.deviceId)
				let res = await getVideoImage({
					channelCode: this.inspectDetails.deviceId
				})
				uni.hideLoading()
				this.formData.inspectImageList.push(res.data)
				this.imageValue = []
				this.formData.inspectImageList.forEach(item => {
					let fileObj = {
						url: item,
						extname: 'png',
						name: 'shuijiao.png'
					}
					this.imageValue.push(fileObj)
				})

			}
		}
	}
</script>

<style lang="scss">
.nav {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;

  .card {
    background: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    &.asset_info {
      .just-sbet {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }

        .key {
          color: #666;
          font-size: 28rpx;
          min-width: 160rpx;
        }
      }
    }
  }

  .uni-forms-item {
    margin-bottom: 30rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .uni-forms-item__label {
      font-size: 28rpx;
      color: #333;
      font-weight: normal;
    }

    .uni-easyinput {
      background: #ffffff;
      border-radius: 8rpx;
      padding: 10rpx;
      
      input {
        font-size: 28rpx;
      }
    }
  }

  // 保持原有的巡检结果按钮样式
  .button-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    justify-items: center;
    background: #ffffff;
    padding: 20rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }
  .button {
    padding: 10px 0;
    font-size: 16px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
    width: 100%;
    height: 50px;
    box-sizing: border-box;
    margin: 0;
  }
  .button.active {
    background-color: #3b83ef;
    color: white;
  }
  .button:hover {
    background-color: #ddd;
    transform: scale(1.1);
  }

  .example-body {
    display: flex;
    gap: 20rpx;
    margin: 20rpx 0;
    background: #ffffff;
    padding: 20rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    button {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      background: #f8f8f8;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #666;

      &:active {
        background: #e8e8e8;
      }

      &[style*="color: #3b83ef"] {
        color: #3b83ef !important;
      }
    }
  }

  .uni-section {
    margin-bottom: 30rpx;
    background: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .uni-section-header {
      padding: 20rpx;
      
      .uni-section__content-title {
        font-size: 30rpx;
        color: #333;
      }
    }

    .uni-file-picker {
      padding: 20rpx;
    }
  }

  .btn_big {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background: #3b83ef;
    color: #ffffff;
    font-size: 32rpx;
    border-radius: 44rpx;
    border: none;
    margin: 40rpx 0;
    text-align: center;

    &:active {
      opacity: 0.9;
    }

    &:disabled {
      background: #cccccc;
      opacity: 0.7;
    }
  }

  .buttonRecode {
    display: flex;
    justify-content: flex-end;
    gap: 20rpx;
    margin: 20rpx;
  }
}

/* APP 平台特定样式 */
.app-button-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 0 8px;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.app-button {
  padding: 8px 4px;
  font-size: 14px;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  text-align: center;
  height: 40px;
  line-height: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.2s ease-in-out;
}

.app-active {
  background-color: #3b83ef !important;
  color: #ffffff !important;
  border-color: #3b83ef !important;
  box-shadow: 0 1px 4px rgba(59, 131, 239, 0.3);
  transform: scale(0.98);
}
</style>