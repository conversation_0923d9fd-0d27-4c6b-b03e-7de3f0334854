<template>
  <view>
    <web-view id="web-view-1" :src="url" @message="handleMessage"></web-view>
  </view>
</template>

<script>
import {
  login
} from '../../api/login'
import {
  getToken
} from '../../utils/auth'
import {
  getVideoUrl
} from '@/api/inspect.js'
import config from '../../config'

export default {
  data() {
    return {
      url: null,
      src: null,
      deviceId: '',
      listLength: ''
    }
  },
  onLoad(e) {
    this.deviceId = e.deviceId
    this.listLength = e.listLength
    this.getUrl()
    // 处理传递的参数
    if (e.param) {
      let pages = getCurrentPages()
      let prevPage = pages[pages.length - 2] // 假设当前页面是第二个页面
      prevPage.$vm.getValue({
        imageList: JSON.stringify(e.param)
      })
      uni.navigateBack({
        delta: 1
      })
    }
  },
  // onShow() {
  //   if (!this.$route.query) {
  //     console.log('this.$route.query')
  //     return
  //   }
  //
  //   if (this.$route.query.param) {
  //     let pages = getCurrentPages()
  //     let prevPage = pages[pages.length - 3]
  //     prevPage.$vm.getValue({
  //       imageList: JSON.stringify(this.$route.query.param)
  //     })
  //     uni.navigateBack({
  //       delta: 1
  //     })
  //   }
  //
  // },

  methods: {
    async getUrl() {
      let baseUrl = config.rouyiUrl + '/appletVideoInspect';
      const appToken = encodeURIComponent(uni.getStorageSync('App-Token'));
      const channelId = encodeURIComponent(this.deviceId);
      const length = encodeURIComponent(this.listLength);

      this.url = `${baseUrl}?appToken=${appToken}&channelId=${channelId}&listLength=${length}`;

    },
    handleMessage(event) {
      this.webViewContext.postMessage({
        'sendToWebView': '1'
      });
      ;
    }

  }
}
</script>

<style>
.p {
  width: 300px;
  height: 300px;
}
</style>