<template>
  <view>
    <view class="" style="background: white;margin: 20rpx;height: 400rpx;border-radius: 25rpx;">
      <view style="margin-top: 40%;text-align: center;padding-top: 20rpx;font-size: 60rpx;font-weight: bolder;">
        {{ inspectPlaceName }}巡检地点是否正常
      </view>
      <view style="display: flex; justify-content: center; gap: 20rpx;padding: 40rpx">
        <button style="margin-left: 100rpx;font-size: 60rpx;border-radius: 25rpx;" @click="submitForm">正常</button>
        <button style="margin-right: 100rpx;font-size: 60rpx;border-radius: 25rpx;" @click="getInspect">不正常</button>
      </view>
    </view>
  </view>
</template>

<script>
import {
  addInspectPlanScan,
  getInspectPlanByQRecode,
  getInspectUser
} from "../../api/inspect";

export default {
  data() {
    return {
      formData: [],
      inspectPlaceName: '',
      userInfoMap: new Map() // 用于存储每个计划的用户信息
    }
  },
  async onLoad(options) {
    // 解析传递的参数
    const params = options.params ? JSON.parse(decodeURIComponent(options.params)) : null;
    this.inspectPlaceName = decodeURIComponent(options.inspectPlaceName);
    console.log('接收到的 inspectPlaceName:', this.inspectPlaceName);
    if (params) {
      console.log('接收到的 params:', params);
      this.params = params;
    } else {
      console.log('没有接收到有效的参数');
      uni.showToast({
        title: '参数错误，请重新尝试。',
        icon: 'none'
      });
    }
    await this.getScanInspectPlan();
    await this.getInspectUser()

  },


  methods: {
    getInspect() {
      console.log("///", this.params)

      uni.navigateTo({
        url: `/pages/inspect/inspectScan?params=${encodeURIComponent(JSON.stringify(this.params))}`
      })
    },

    async getScanInspectPlan() {
      if (this.params) {
        console.log("使用传递过来的 params:", this.params);
        try {
          const result = await getInspectPlanByQRecode(this.params); // 使用 this.params
          console.log('巡检计划2:', result); // 确保这里能够打印结果
          this.data = result.data;
        } catch (error) {
          console.error('获取巡检计划失败:', error);
        }
      } else {
        console.log('没有传递有效的 params');
      }
    },
    // 检查是否存在 inspectPlanId


    async getInspectUser() {

      // 检查 this.data 和 inspectPlans 是否存在
      if (this.data && this.data.inspectPlans) {
        // 使用 map 获取所有的 inspectPlanId
        const planIds = this.data.inspectPlans.map(plan => plan.inspectPlanId);
        console.log("所有计划的 ID:", planIds);

        // 对每个计划 ID 分别处理
        for (const planId of planIds) {
          console.log("当前处理的计划 ID:", planId);
          const res = await getInspectUser(planId);
          console.log(`计划 ${planId} 的用户信息:`, res);
          // 将用户信息存储到 Map 中
          this.userInfoMap.set(planId, {
            inspectUserId: res.data.inspectUserId,
            inspectUserName: res.data.inspectUserName
          });
        }
      } else {
        console.log("没有找到巡检计划数据");
      }
    },
    async submitForm() {
      console.log("获取到的巡检计划数据1:", this.data);
      // 判断是否有有效的 inspectPlans 数组
      if (this.data.inspectPlans && this.data.inspectPlans.length > 0) {
        // 遍历 inspectPlans 数组，逐个处理巡检计划
        this.data.inspectPlans.forEach(plan => {
          // 从 Map 中获取对应计划的用户信息
          const userInfo = this.userInfoMap.get(plan.inspectPlanId) || {};
          plan.inspectRoute.list4RoutePlaces.forEach(place => {
            this.formData.push({
              inspectPlaceId: plan.currentInspectPlace.inspectPlaceId,
              inspectPlaceName: plan.currentInspectPlace.inspectPlaceName,
              inspectPlanId: plan.inspectPlanId,
              inspectPlanName: plan.inspectPlanName,
              inspectTypeId: plan.inspectTypeId,
              inspectTypeName: plan.inspectTypeName,
              inspectUserId: userInfo.inspectUserId,
              inspectUserName: userInfo.inspectUserName,
              inspectRouteId: plan.inspectRouteId,
              inspectRouteName: plan.inspectRouteName,
              deviceId: plan.currentInspectPlace.deviceId,
              detail: plan.currentInspectPlace.detail, // 获取每个巡检地点的详细信息
              status: 1, // 假设为已巡检状态
              isDevice: plan.currentInspectPlace.isDevice,
              inspectResultType: 1,
              // inspectResultDetail: plan.inspectResultDetail,
              inspectResultDetail: "正常",
              // longitude: this.data.currentInspectPlace.longitude,
              longitude: "56.2737",  // 使用硬编码的经度
              // latitude: this.data.currentInspectPlace.latitude
              latitude: "-66.6315",  // 使用硬编码的纬度
              delTag: plan.currentInspectPlace.delTag, // 删除标记：0未删除；1已删除
            });
          });
        });
        console.log("this.formData", this.formData);
        // 调用 addInspectPlan
        try {
          await this.addInspectPlan();
        } catch (error) {
          console.error("提交巡检计划时出错:", error);
        }
      } else {
        console.log("没有有效的巡检计划数据");
      }
    },
    async addInspectPlan() {
      try {
        console.log("原始 formData:", this.formData);
        // 在提交前过滤 formData
        let filteredFormData = this.formData.filter(item =>
            item.deviceId == this.params.deviceCode
        );
        
        // 对数据进行去重，使用 inspectPlaceId 作为唯一标识符
        const uniqueData = [];
        const seenIds = new Set();
        
        for (const item of filteredFormData) {
            // 创建一个唯一键，可以根据需要组合多个字段
            const uniqueKey = `${item.inspectPlaceId}_${item.inspectPlanId}`;
            if (!seenIds.has(uniqueKey)) {
                seenIds.add(uniqueKey);
                uniqueData.push(item);
            }
        }
        
        // 使用去重后的数据
        filteredFormData = uniqueData;

        console.log("过滤和去重后的 formData:", filteredFormData);

        // 使用过滤后的数据提交
        let res = await addInspectPlanScan(filteredFormData);
        console.log("addInspectPlanScan 返回的结果:", res);

        if (res) {
          uni.navigateBack({
            delta: 1
          });
          uni.showToast({
            title: '该巡检地点计划已完成',
            icon: 'none',
            duration: 3000
          });
        } else {
          console.log("addInspectPlanScan 返回了无效的结果");
        }
      } catch (error) {
        console.error("调用 addInspectPlanScan 发生错误:", error);
      }
    }

  }
}
</script>

<style>

</style>