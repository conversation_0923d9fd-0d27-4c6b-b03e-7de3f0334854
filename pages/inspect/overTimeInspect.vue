<template>
	<view class="container">
		<view class="flex  align-center p20">
			
			<uni-easyinput prefixIcon="search" @focus="flag=true"   v-model="query.inspectPlanName" 
				placeholder="请输入计划名称">
			</uni-easyinput>
			
		</view>
		<view class="p20" v-if="flag">
			<view class="btn_big" @click="search">
				搜索
			</view>
		
		</view>
			<view class="asset_list p20">
				<view class="p20 bf card asset_info" v-for="item in inspectList" :key="item.id">
					<view class="just-sbet " >
						<view class="key">
							巡检计划 
						</view>	
						<view class="">
							{{item.inspectPlanName}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							巡检路线 
						</view>	
						<view class="">
							{{item.inspectRouteName}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							开始时间 
						</view>	
						<view class="">
							{{item.inspectStartTime}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							结束时间 
						</view>	
						<view class="">
							{{item.inspectEndTime}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							巡检类型 
						</view>	
						<view class="">
							{{item.inspectTypeName}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
						</view>	
						<!-- <view class="tips center" @click="hrpRepairReport(item)" >
							报修<uni-icons type="forward" color="#fff"></uni-icons>
						</view> -->
						<view class="tips center" @click="inspectReport" :data-index="item" >
							详情<uni-icons type="forward" color="#fff"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			<!-- 分页栏 -->
			<view class="pagination-container" v-if="total > 0">
				<view class="pagination">
					<view class="page-btn" :class="{ disabled: query.pageNum <= 1 }" @click="handlePrevPage">
						上一页
					</view>
					<view class="page-info">
						第 {{query.pageNum}} 页 / 共 {{totalPages}} 页
					</view>
					<view class="page-btn" :class="{ disabled: query.pageNum >= totalPages }" @click="handleNextPage">
						下一页
					</view>
				</view>
			</view>
	</view>
</template>

<script>
	import {
		listOverTimeInspectRecord
	} from '@/api/inspect.js';
	
	export default {
		data() {
			return {
				propertyIdInput: "", // 输入的id
				flag: false, // 搜索按钮是否显示
				inspectList: [],
				total: 0,  // 添加total字段
				query:{
					pageNum: 1,
					pageSize: 10,
					inspectPlanName:''
				}
			}
		},
		onLoad() {
			this.query.pageNum=1
			this.getInspectList()
			
		},
		onShow(){
			this.getInspectList()
		},
		
		methods: {
			search() {
				
				this.query.pageNum=1
				
				this.getInspectList()
				this.flag=false
				
				
			},
			inspectReport(item){
				console.log(item)
			    var objStr = item.currentTarget.dataset.index
				
				this.getInspectLocation(objStr)
				
			},
			async getInspectList() {
				
				let res = await listOverTimeInspectRecord(this.query);
				if(res ){
					console.log(res.rows)
					this.total = res.total || res.pages;  // 获取总页数
					this.inspectList = res.rows;
				}

			},
			async getInspectLocation(inspectInfo) {
				inspectInfo.status=1;
				uni.navigateTo({
					url: '/pages/inspect/overTimeInspectPlace?inspectInfo='+
					encodeURIComponent(JSON.stringify(inspectInfo))
				})
			},
			handlePrevPage() {
				if (this.query.pageNum > 1) {
					this.query.pageNum -= 1;
					this.getInspectList();
					uni.pageScrollTo({ scrollTop: 0, duration: 300 });
				}
			},
			handleNextPage() {
				if (this.query.pageNum < this.totalPages) {
					this.query.pageNum += 1;
					this.getInspectList();
					uni.pageScrollTo({ scrollTop: 0, duration: 300 });
				}
			},
		},
		computed: {
			totalPages() {
				return Math.ceil(this.total / this.query.pageSize) || 1;
			}
		}
	}
</script>

<style lang="scss">
	.annex {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
	}

	.nav {
		position: fixed;
		width: 100%;
		top: 0;
		z-index: 1024;
		box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
	}
	.asset_list{
	margin-top: 15rpx;
	display: grid;
	gap: 20rpx;
	.asset_info{
		display: grid;
		gap:25rpx;
		.tips{
			    color: #fff;
			    background: #3296fa;
			    padding: 5rpx 10rpx;
			    border-radius: 20rpx;
		}
			.key {
				flex-basis: 150rpx;
				margin-right: 30rpx;
			}

	}
	}
	.pagination-container {
		padding: 20rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.pagination {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 40rpx;

		.page-btn {
			background: #4095E5;
			color: #fff;
			padding: 10rpx 30rpx;
			border-radius: 30rpx;
			font-size: 28rpx;

			&.disabled {
				background: #ccc;
				opacity: 0.7;
			}
		}

		.page-info {
			font-size: 28rpx;
			color: #666;
		}
	}

	.container {
		padding-bottom: 120rpx;
	}
</style>