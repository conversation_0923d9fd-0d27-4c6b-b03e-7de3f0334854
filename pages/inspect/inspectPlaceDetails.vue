<template>
	<view class="nav bf">
		<view class="p20 bf card asset_info">
			<view class="just-sbet ">
				<view class="key">
					巡检地点
				</view>
				<view class="">
					{{inspectDetails.inspectPlaceName}}
				</view>
			</view>
			<view class="just-sbet ">
				<view class="key">
					巡检科室
				</view>
				<view class="">
					{{inspectDetails.deptName}}
				</view>
			</view>
			<view v-if="inspectDetails.isDevice" class="just-sbet ">
				<view class="key">
					设备编号
				</view>
				<view class="">
					{{inspectDetails.deviceId}}
				</view>
			</view>
			<view class="just-sbet ">
				<view class="key">
					详情
				</view>
				<view class="">
					{{inspectDetails.detail}}
				</view>
			</view>
		</view>

		<uni-forms>
			<DynamicForm ref="localDynamicFormRef" :formModel="formModel" :formContent="formContent" @getData="getData">
			</DynamicForm>
			<uni-forms-item class="flex  align-center p20" label-width="80px" label="巡检意见" name="detail">
				<uni-easyinput type="text" v-model="formData.detail" placeholder="请输入巡检意见" />
			</uni-forms-item>
		</uni-forms>
		<!-- 巡检结果按钮组 -->
		  <uni-forms-item class="flex align-center p20" label-width="80px" label="巡检结果" name="inspectResultType">
		      <view class="button-grid">
		        <button 
		          v-for="item in inspectResults" 
		          :key="item.value"
		          :class="['button', { 'active': formData.inspectResultType === item.value }]"
		          @click="selectInspectResult(item.value)">
		          {{ item.label }}
		        </button>
		      </view>
		    </uni-forms-item>

		<uni-section title="上传录音" type="line">
			<view class="example-body p20 buttonRecode">
				<button @touchstart="touchstart" @touchend="touchend">按住说话 </button>
				<button @tap="playVoice" style="color: #3b83ef">播放录音</button>
				<button @click="uploadAudio" style="color: #3b83ef">上传录音</button>
			</view>
		</uni-section>
		<uni-section title="上传视频" type="line">
			<view>
				<uni-file-picker ref="videoPicker" v-model="videoValue" limit="1" title="最多选择1个视频" fileMediatype="video"
					mode="grid" @select="handleVideoSelect" @success="handleVideoSuccess"
					@delete="handleVideoDelete"></uni-file-picker>
			</view>
		</uni-section>
		<uni-section title="上传图片" type="line">
			<view class="">
				<uni-file-picker ref="filePicker" v-model="imageValue" limit="5" title="最多选择5张图片" fileMediatype="image"
					mode="grid" @select="handleSelect" @success="handleSuccess"
					@delete="handleDelete"></uni-file-picker>
			</view>
		</uni-section>
		<button type="primary" @click="toReportForRepairs(inspectDetails)"
			style="width: 100px; margin-left: 10px;margin-bottom: 10px">点击报修</button>
		<button class="btn_big" :disabled="inspectInfo.status > 0" @click="submitForm">确定</button>
		<view style="width: 100%; padding: 20rpx; display: flex; justify-content: space-between;" v-if="isVideoInspect">
			<navigator
				:url="`/pages/inspect/inspectVideo?deviceId=${encodeURIComponent(this.inspectDetails.deviceId)}&listLength=${5-(formData.inspectImageUrlList.length)}`">
				<button class="btn_big">跳转视频巡检</button>
			</navigator>
			<button class="btn_big" @click="getImage">从监控中截屏</button>
		</view>

	</view>

</template>

<script>
	import DynamicForm from '@/components/parser/DynamicForm'
	import {
		uploadImage,
		uploadAudio,
		uploadVideo
	} from '@/api/system/user'
	import {
		// addInspectRecord,
		// alterInspectRecord,
		addInspectPlanScan,
		getVideoUrl,
		getVideoImage,
		getFormByInspectId,
		getInspectUser
	} from '@/api/inspect';
	import {
		getToken
	} from '@/utils/auth'
	import {
		getInfo,
		login
	} from '../../api/login';
	import {
		getCard
	} from "../../api/HRP/card";

	// #ifdef MP-DINGTALK

	//#endif

	//目前支持App端 支持录音和播放录音，钉钉小程序因组件问题需另外考虑
	// #ifndef MP-DINGTALK
	const recorderManager = uni.getRecorderManager();
	const innerAudioContext = uni.createInnerAudioContext();
	innerAudioContext.autoplay = true;
	// #endif

	export default {
		data() {
			return {

				queryFormByInspectId: {
					inspectTypeId: 0,
				},
				formData: {
					inspectPlaceId: null,
					inspectPlaceName: null,
					inspectPlanId: null,
					inspectPlanName: null,
					inspectTypeId: null,
					inspectTypeName: null,
					inspectRouteId: null,
					inspectRouteName: null,
					inspectResultType: 1,
					inspectResultDetail: '正常',
					detail: null,
					status: null,
					isDevice: null,
					deviceId: null,
					//图片地址列表
					inspectImageList: [],
					// 音频地址列表
					inspectAudioList: [],
					// 视频地址列表
					inspectVideoList: [],
					//需要提交的自定义表单数据
					formContent: null,
				},
				//添加视频双向绑定对象
				videoValue: [],
				imageValue: [],
				inspectDetails: null,
				inspectInfo: {},
				realPlayer: null,
				src: null,
				isVideoInspect: false,
				//自定义表单JSON对象
				formModel: {},
				//自定义表单数据动态保存对象
				formContent: {},
				// 保存的录音地址
				voicePath: '',
				// 巡检结果的五个选项
				inspectResults: [{
						label: '正常',
						value: 1
					},
					{
						label: '轻微故障',
						value: 2
					},
					{
						label: '中等故障',
						value: 3
					},
					{
						label: '重度故障',
						value: 4
					},
					{
						label: '完全损坏',
						value: 5
					}
				],
			}
		},
		components: {
			DynamicForm
		},
		onHide() {
			// #ifndef MP-DINGTALK
			innerAudioContext.stop()
			// #endif
		},
		async onLoad(option) {
			console.log("this.inspectResults", this.inspectResults);
			// #ifndef MP-DINGTALK
			let self = this;
			//设置录屏停止事件，在录音停止事件中获取录音文件路径
			recorderManager.onStop(function(res) {
				console.log('recorder stop' + JSON.stringify(res));
				self.voicePath = res.tempFilePath;
			});
			// #endif
			if (option.inspectDetails) {
				// 对传递的 JSON 字符串进行解码
				var objStr = decodeURIComponent(option.inspectDetails);
				// 将 JSON 字符串转换回对象
				var obj = JSON.parse(objStr)
				this.inspectDetails = obj
				console.log("obj", obj)
				console.log('this.formData.deviceId' + this.inspectDetails.deviceId)
				this.formData.deviceId = this.inspectDetails.deviceId
				//this.inspectDetails.formModel = JSON.parse(this.inspectDetails.formModel)
			}
			if (option.inspectInfo) {
				// 对传递的 JSON 字符串进行解码
				var objStr = decodeURIComponent(option.inspectInfo);
				// 将 JSON 字符串转换回对象
				var obj = JSON.parse(objStr)
				this.inspectInfo = obj
			}

			//根据表单类型编号获取自定义表单定义JSON
			this.queryFormByInspectId.inspectTypeId = this.inspectInfo.inspectTypeId
			let resForm = await getFormByInspectId(this.queryFormByInspectId)
			//根据表单类型编号获取自定义表单定义JSON
			this.formModel = JSON.parse(resForm.data.formContent)
			console.log("this.formModel", this.formModel)


			// getVideoUrl({
			// 	channelId: this.inspectDetails.deviceId
			// }).then(res => {
			// 	if (res) {
			// 		this.isVideoInspect = true
			// 	}
			// }).catch(error => {
			// 	if (error) {
			// 		this.isVideoInspect = false
			// 	}
			// })
			await this.getInspectUser();
		},
		methods: {

			// 选择巡检结果类型并更新详细描述
			    selectInspectResult(value) {
			      this.formData.inspectResultType = value;
			      
			      const selectedResult = this.inspectResults.find(item => item.value === value);
			      if (selectedResult) {
			        this.formData.inspectResultDetail = selectedResult.label;
			        console.log('巡检结果选中的值:', this.formData.inspectResultType);
			        console.log('巡检结果详情:', this.formData.inspectResultDetail);
			      }
			    },

			// 上传录音

			touchstart() {
				uni.showLoading({
					title: "说话中"
				})
				// 开始录音
				// #ifndef MP-DINGTALK
				recorderManager.start({
					format: 'mp3' // 音频格式，有效值 aac/mp3
				});
				// #endif
			},
			touchend() {
				uni.hideLoading();
				// 结束录音
				// #ifndef MP-DINGTALK
				recorderManager.stop();
				// #endif
			},
			playVoice() {
				console.log('播放录音');
				if (this.voicePath) {
					// #ifndef MP-DINGTALK
					innerAudioContext.src = this.voicePath;
					innerAudioContext.play();
					// #endif
				}
			},
			async uploadAudio() {
				if (!this.voicePath) {
					uni.showToast({
						title: "没有录音文件",
						icon: "none"
					});
					return;
				}

				let data = {
					name: 'file',
					filePath: this.voicePath
				};

				try {
					let res = await uploadAudio(data); // 调用上传接口
					console.log("上传返回的结果:", res); // 查看返回的数据结构，确保 res.url 存在

					if (res.code === 200) {
						if (!Array.isArray(this.formData.inspectAudioList)) {
							this.formData.inspectAudioList = []; // 初始化为空数组
						}
						const AudioItem = {
							inspectMediaId: null,
							inspectPlaceRecordId: null,
							inspectAudioUrl: res.url,
							delTag: 0
						};
						this.formData.inspectAudioList.push(AudioItem); // 将返回的 URL 添加到列表中
						console.log("录音上传成功", this.formData.inspectAudioList);
						uni.showToast({
							title: "录音上传成功",
							icon: "success"
						});
					} else {
						uni.showToast({
							title: "上传失败",
							icon: "none"
						});
					}
				} catch (error) {
					console.error("上传录音出错:", error);
					uni.showToast({
						title: "上传失败",
						icon: "none"
					});
				}
			},

			// 上传视频

			async handleVideoSelect(res) {
				// 选择视频
				this.uploadVideo(res.tempFiles);
			},

			async handleVideoSuccess(e) {
				console.log(e);
			},

			async uploadVideo(tempFiles) {
				if (!tempFiles.length) {
					uni.showToast({
						title: "请先选择视频",
						icon: "none"
					});
					return;
				} // 检查是否选择了文件
				const path = tempFiles.pop(); // 获取最后一个选中的文件
				let data = {
					name: 'file',
					filePath: path.path
				};

				try {
					let res = await uploadVideo(data); // 调用上传接口
					console.log("视频上传成功", res)
					if (res.code === 200) {
						// 确保 inspectVideoList 是一个数组
						if (!Array.isArray(this.formData.inspectVideoList)) {
							this.formData.inspectVideoList = []; // 初始化为空数组
						}
						const VideoItem = {
							inspectMediaId: null,
							inspectPlaceRecordId: null,
							inspectVideoUrl: res.url,
							delTag: 0
						};
						this.formData.inspectVideoList.push(VideoItem); // 将返回的 URL 添加到列表中
						console.log("this.formData.inspectVideoList", this.formData.inspectVideoList)

						uni.showToast({
							title: "视频上传成功",
							icon: "success"
						});
					} else {
						uni.showToast({
							title: "上传失败",
							icon: "none"
						});
					}
				} catch (error) {
					console.error("上传视频出错:", error);
					uni.showToast({
						title: "上传失败",
						icon: "none"
					});
				}
			},

			handleVideoDelete(e) {
				// 视频删除
				let {
					url
				} = e.tempFile;
				const num = this.formData.inspectVideoList.findIndex((e) => e == url);
				this.formData.inspectVideoList.splice(num, 1);
			},

			// 上传图片

			async handleSelect(res) {
				// 选择并上传图片
				this.uploadImg(res.tempFiles)
			},
			async handleSuccess(e) {
				console.log(e)
			},
			async uploadImg(tempFiles) {
				if (!tempFiles.length) return;
				for (let i = 0; i < tempFiles.length; i++) {
					const path = tempFiles[i];
					let data = {
						name: 'file',
						filePath: path.path
					};
					try {
						let res = await uploadImage(data); // 调用上传接口
						console.log("图片上传成功", res);

						if (res.code === 200) {
							if (!Array.isArray(this.formData.inspectImageList)) {
								this.formData.inspectImageList = []; // 初始化为空数组
							}
							const ImageItem = {
								inspectMediaId: null,
								inspectPlaceRecordId: null,
								inspectImageUrl: res.url,
								delTag: 0
							};
							this.formData.inspectImageList.push(ImageItem); // 将返回的 URL 添加到列表中
							console.log("上传后的图片 URL 列表：", this.formData.inspectImageList);
							uni.showToast({
								title: "图片上传成功",
								icon: "success"
							});
						} else {
							uni.showToast({
								title: "上传失败",
								icon: "none"
							});
						}
					} catch (error) {
						console.error("图片上传失败", error);
						uni.showToast({
							title: "上传失败",
							icon: "none"
						});
					}
				}
			},
			handleDelete(e) {
				// 图片删除
				let {
					url
				} = e.tempFile
				const num = this.formData.inspectImageList.findIndex((e) => e == url)
				this.formData.inspectImageList.splice(num, 1)
			},

			getData(localFormContent) {
				// console.log("localFormContent", localFormContent)
				this.formContent = localFormContent
			},
			fetchData() {
				this.$refs.localDynamicFormRef.getData()
			},
			async getInspectUser() {
				try {
					
					const inspectPlanId = this.inspectInfo.inspectPlanId
					const response = await getInspectUser(inspectPlanId); // 接口修改增加参数inspectPlanId
					console.log("response", response);
					if (response.code === 200) {
						this.inspectUser = response.data;
					} else {
						this.$message.error('获取巡检用户失败');
					}
				} catch (error) {
					this.$message.error('网络错误，无法获取巡检用户');
				}
			},

			async submitForm() {
				this.fetchData();
				console.log("this.inspectUser", this.inspectUser)
				//console.log("this.formContent", this.formContent)
				//自定义表单动态数据设置到提交对象this.formData中
				this.formData.formContent = JSON.stringify(this.formContent)
				this.formData.inspectPlaceId = this.inspectDetails.inspectPlaceId
				this.formData.inspectPlaceName = this.inspectDetails.inspectPlaceName
				this.formData.inspectPlanId = this.inspectInfo.inspectPlanId
				this.formData.inspectPlanName = this.inspectInfo.inspectPlanName
				this.formData.inspectTypeId = this.inspectInfo.inspectTypeId
				this.formData.inspectTypeName = this.inspectInfo.inspectTypeName
				this.formData.inspectRouteId = this.inspectInfo.inspectRouteId
				this.formData.inspectRouteName = this.inspectInfo.inspectRouteName
				this.formData.isDevice = this.inspectDetails.isDevice
				this.formData.deviceId = this.inspectDetails.deviceId
				this.formData.status = 1
				// this.formData.inspectResultDetail = "正常"
				this.formData.longitude = '56.2737'
				this.formData.latitude = '-66.6315'
				this.formData.inspectUserId = this.inspectUser.inspectUserId
				this.formData.inspectUserName = this.inspectUser.inspectUserName
				// this.formData.inspectResultType = 0
				// this.formData.inspectResultDetail = ''
				// 接口已修改，this，formData的值可能有改变
				//console.log("this.formData", this.formData)
				await this.addInspectPlanScan(); //新接口
				// await this.addInspectRecord();
				// console.log("res",res)
				// if (res) {
				// 	uni.navigateBack({
				// 		delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
				// 	});
				// }
			},
			async addInspectPlanScan() {
				console.log("this.formDataac", this.formData)
				let addFormData = []
				addFormData.push(this.formData);
				console.log("addFormData", addFormData)
				let res = await addInspectPlanScan(addFormData);
				// console.log("this.formData",this.formData)
				if (res) {
					uni.navigateBack({
						delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
					});
				}
			},
			previewImage(attachment, index = 1) {
				// 预览图片
				attachment = JSON.parse(attachment).map((item) => {
					return item.url
				})
				uni.previewImage({
					urls: attachment,
					current: index
				})
				console.log(attachment, 'attachment');
			},
			async toReportForRepairs(inspectDetails) {
				try {
					// 根据 inspectDetails.deviceType 判断跳转的页面
					switch (inspectDetails.deviceType) {
						case 1: // 如果是 assetDetails 页面，调用 getCard 获取详细信息
							const cardResponse = await getCard(inspectDetails.deviceId);
							const cardData = cardResponse.data;

							// 将 getCard 查询到的数据赋值到 inspectDetails
							this.inspectDetails.recordTitle = cardData.cardName + '报修';
							this.inspectDetails.deviceName = cardData.cardName;
							this.inspectDetails.deviceCode = cardData.cardNumber;
							this.inspectDetails.deviceUseDeptName = cardData.deptName;
							this.inspectDetails.deviceUseDeptId = cardData.deptCode;
							this.inspectDetails.deviceLocation = inspectDetails.inspectPlaceName;
							this.inspectDetails.cardNumber = cardData.cardNumber;
							this.inspectDetails.deviceTypeOne = inspectDetails.deviceType; // 设备类型一
							this.inspectDetails.deviceTypeOneName = 'hrp设备';
							this.inspectDetails.deviceTypeTwo = cardData.class1Id;
							this.inspectDetails.deviceTypeTwoName = cardData.class1Name;
							this.inspectDetails.deviceTypeThree = cardData.class2Id;
							this.inspectDetails.deviceTypeThreeName = cardData.class2Name;
							this.inspectDetails.deviceTypeFour = cardData.class3Id;
							this.inspectDetails.deviceTypeFourName = cardData.class3Name;
							this.inspectDetails.repairTell = '';
							this.inspectDetails.repairName = '';
							this.inspectDetails.repairCostMoney = 0;

							// 获取用户信息
							const userInfoResponse = await getInfo();
							this.inspectDetails.reportName = userInfoResponse.user.userName;
							this.inspectDetails.reportTell = userInfoResponse.user.phonenumber;

							// 编码 inspectDetails 数据为 URL 参数
							const inspectDetailsString = encodeURIComponent(JSON.stringify(this.inspectDetails));

							// 跳转到 assetDetails 页面
							uni.navigateTo({
								url: `/pages/assetDetails/assetDetails?inspectDetails=${inspectDetailsString}`,
							});
							break;
						case 2:
							// 获取用户信息
							const userInfoResponseForOther = await getInfo();
							this.inspectDetails.reportName = userInfoResponseForOther.user.userName;
							this.inspectDetails.reportTell = userInfoResponseForOther.user.phonenumber;

							uni.navigateTo({
								url: `/pages/other/index?inspectDetails=${encodeURIComponent(JSON.stringify(inspectDetails))}`,
							});
							break;

						case 3:
							uni.navigateTo({
								url: `/pages/dahua/index?inspectDetails=${encodeURIComponent(JSON.stringify(inspectDetails))}`,
							});
							break;

						case 4:
							uni.navigateTo({
								url: `/pages/huawei/index?inspectDetails=${encodeURIComponent(JSON.stringify(inspectDetails))}`,
							});
							break;

						default:
							console.warn('未知的设备类型');
					}
				} catch (error) {
					console.error('数据加载失败:', error);
				}
			},
			getValue(data) {
				const imageList = [JSON.parse(data.imageList)]
				if (typeof imageList[0] === 'string') {
					this.formData.inspectImageUrlList.push(decodeURIComponent(decodeURIComponent(imageList[0])))
				} else {
					imageList[0].forEach(item => {
						this.formData.inspectImageUrlList.push(decodeURIComponent(decodeURIComponent(item)))
					})
				}
				console.log(this.formData.inspectImageUrlList);
				this.formData.inspectImageUrlList.forEach(item => {
					let fileObj = {
						url: item,
						extname: 'png',
						name: 'shuijiao.png'
					}
					this.imageValue.push(fileObj)
				})
			},
			async getImage() {
				console.log(this.formData.inspectImageUrlList);
				console.log(this.imageValue);
				if (this.formData.inspectImageUrlList.length >= 5) {
					uni.showToast({
						title: "图片已达最大数量"
					})
					return
				}
				uni.showLoading({
					title: "获取图像中"
				})
				console.log('getVideoImage' + this.formData.deviceId)
				let res = await getVideoImage({
					channelCode: this.inspectDetails.deviceId
				})
				uni.hideLoading()
				this.formData.inspectImageUrlList.push(res.data)
				this.imageValue = []
				this.formData.inspectImageUrlList.forEach(item => {
					let fileObj = {
						url: item,
						extname: 'png',
						name: 'shuijiao.png'
					}
					this.imageValue.push(fileObj)
				})

			}
		},
		 watch: {
		    // 监听inspectResultType的变化，及时更新inspectResultDetail
		    'formData.inspectResultType': function (newVal) {
		      const selectedResult = this.inspectResults.find(
		        (item) => item.value === newVal
		      );
		      
		      if (selectedResult) {
		        this.formData.inspectResultDetail = selectedResult.label;
		      }
		    }
			}
	}
</script>

<style lang="scss">
	// .form-el-radio-group{
	// 	padding: 20rpx;
	// }
	.btn_big {
		padding: 20rpx;
		font-size: 50rpx;
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #5b73f9;
		color: #fff;
		border-radius: 10rpx;
	}

	.uni-forms-item__label {
		font-size: 30px;
		/* 设置字体大小 */
		// color: red; /* 设置字体颜色 */
		font-weight: bold;
		/* 设置字体粗细 */
	}

	.property_info {
		// margin-top: 135rpx;
		// display: grid;
		// gap: 45rpx;

		.customer_item {
			padding: 30rpx 0;

			.content {
				flex-basis: 130rpx;
				margin-right: 30rpx;
			}

			.key {
				flex: 1;
				text-align: right;

			}
		}


	}

	.popup-content {
		padding: 20px;
	}

	.form-item {
		margin-bottom: 15px;
	}

	.dialog-footer {
		display: flex;
		justify-content: space-between;
	}

	.buttonRecode {
		display: flex;
		justify-content: flex-end;
		margin-right: 30px;
	}
	.button-grid {
	  display: grid;
	  grid-template-columns: repeat(5, 1fr); /* 设置为三列网格布局 */
	  gap: 10px;
	  justify-items: center; /* 使按钮居中 */
	}
	.button {
		padding: 10px 0; /* 增加上下内边距 */
		  font-size: 16px; /* 设置统一字体大小 */
		  background-color: #f0f0f0;
		  border: 1px solid #ccc; /* 设置统一边框 */
		  cursor: pointer;
		  transition: background-color 0.3s, transform 0.2s;
		  width: 100%; /* 按钮占据网格单元的全部宽度 */
		  height: 50px; /* 统一按钮高度 */
		  box-sizing: border-box; /* 确保内边距和边框不影响按钮的尺寸 */
		  margin: 0; /* 重置外边距 */
	}
	.button.active {
	  background-color: #3b83ef;
	  color: white;
	}
	
	.button:hover {
	  background-color: #ddd;
	  transform: scale(1.1); /* 鼠标悬停时稍微放大按钮 */
	}
</style>