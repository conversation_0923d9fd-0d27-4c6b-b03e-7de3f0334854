<template>
  <view class="nav bf">
    <view class="p20 bf card asset_info">
      <view class="just-sbet ">
        <view class="key">
          巡检地点
        </view>
        <view class="">
          {{ inspectDetails.inspectPlaceName }}
        </view>
      </view>
      <view class="just-sbet ">
        <view class="key">
          巡检科室
        </view>
        <view class="">
          {{ inspectDetails.deptName }}
        </view>
      </view>
      <view v-if="inspectDetails.isDevice" class="just-sbet ">
        <view class="key">
          设备编号
        </view>
        <view class="">
          {{ inspectDetails.deviceId }}
        </view>
      </view>
      <view class="just-sbet ">
        <view class="key">
          详情
        </view>
        <view class="">
          {{ inspectDetails.detail }}
        </view>
      </view>
    </view>
    <uni-forms :modelValue="formData">

      <DynamicForm v-if="formContent && formModel" ref="localDynamicFormRef" :formModel="formModel" :formContent="formContent" >
      </DynamicForm>
      <uni-forms-item class="flex align-center p20" label-width="80px" label="巡检意见" name="detail">
            <uni-easyinput type="text" v-model="formData.detail" placeholder="请输入巡检意见" />
          </uni-forms-item>
      
          
          <uni-forms-item class="flex align-center p20" label-width="80px" label="完成巡检人" name="inspectPerson">
            <uni-easyinput type="text" v-model="formData.inspectUserName" placeholder="请输入完成巡检人姓名" />
          </uni-forms-item>

	  
    </uni-forms>
    <uni-section title="播放录音" type="line">
      <view class="example-body">
        <button  @tap="playVoice" style="color: #3b83ef">播放录音</button>
      </view>
    </uni-section>
    <uni-section title="上传视频" type="line">
      <view>
<!--        {{this.videoSrc}}-->
        <button  @tap="previewVideo" style="color: #3b83ef">预览视频</button>
        <view v-html="videoHtml"></view>
      </view>
    </uni-section>
    <uni-section title="上传图片" type="line">
      <view class="example-body">
        <uni-file-picker v-model="imageValue" limit="5" title="最多选择5张图片" fileMediatype="image" mode="grid"
                         @select="handleSelect" @success="handleSuccess" @delete="handleDelete"></uni-file-picker>
      
	  <!-- 展示图片 -->
	      <view class="image-gallery">
				<view v-for="(item, index) in formData.inspectImageList" :key="index" class="image-item">
					<image :src="item.inspectImageUrl" class="uploaded-image" @click="previewImage(item.inspectImageUrl, index)"/>
				</view>
	        </view>
	  </view>
    </uni-section>

    <button class="btn_big" :disabled="isSubmitDisabled" @click="submitForm">确定</button>
  </view>

</template>

<script>
import DynamicForm from '@/components/parser/DynamicForm'
import {
  uploadImage
} from '@/api/system/user'
import {
  alterInspectRecord, getInspectRecord, getFormByInspectId
} from '@/api/inspect';

// #ifdef MP-DINGTALK

//#endif

//目前支持App端 支持录音和播放录音，钉钉小程序因组件问题需另外考虑
// #ifndef MP-DINGTALK
const recorderManager = uni.getRecorderManager();
const innerAudioContext = uni.createInnerAudioContext();
innerAudioContext.autoplay = true;
// #endif

export default {
  // 注册的组件
  components: {
    DynamicForm
  },
  // 在真机调试时使用video标签导致视频黑屏无法播放，定义了一个 videoHtml 函数，用于返回一个包含视频元素的 HTML 字符串
  computed:{
    videoHtml: function() {
      return `<video  autoplay loop muted controls="controls" width="200px" height="200px"><source src="${this.videoSrc}" type="video/mp4"></video>`;
    }
  },

  data() {
    return {
      formData: {},
      inspectDetails: {},
      query: {
        inspectPlaceRecordId: 0
      },
      imageValue: [],
      queryFormByInspectId: {
        inspectTypeId: 0,
      },
      //自定义表单JSON对象
      formModel: {},
      // 提交的表单
      formContent: {}, // 初始化为空对象而不是null
      // 用于绑定视频源
      videoSrc: '',
      isSubmitDisabled: false
    }
  },
  onLoad(option) {
    if (option.inspectDetails) {
      // 对传递的 JSON 字符串进行解码
      var objStr = decodeURIComponent(option.inspectDetails);
      // 将 JSON 字符串转换回对象
      this.inspectDetails = JSON.parse(objStr)
      console.log("this.inspectDetails",this.inspectDetails)
      // this.query.inspectPlaceRecordId=this.inspectDetails.inspectPlaceRecordId
      this.query.inspectPlaceRecordId = this.inspectDetails.inspectPlaceRecordId
    } // 可以使用新接口来获得inspectDetails

    this.getInspectRecord()
  },
  methods: {
	  // onImageError(event) {
	  //     console.error('图片加载失败', event);
	  //   },
    handleError(event) {
      console.error('视频播放错误', event);
    },
    async getInspectRecord() {

      var res = await getInspectRecord(this.query) 
      if (res) {
        console.log("res.data",res.data)
        this.formData = res.data
		// console.log("formData.inspectImageUrlList", this.formData.inspectImageUrlList);
        this.inspectDetails.inspectAudioList = res.data.inspectAudioList
        this.inspectDetails.inspectVideoDtoList = res.data.inspectVideoDtoList
        //把表单json字符串转为JSON对象
        try {
          this.formData.formContent = JSON.parse(this.formData.formContent || '{}')
          this.formContent = this.formData.formContent || {}
        } catch (e) {
          console.error('解析formContent失败', e)
          this.formContent = {}
        }
        console.log(" this.formContent",this.formContent)

        //根据巡检类型ID获取巡检自定义表单JSON格式
        this.queryFormByInspectId.inspectTypeId = this.formData.inspectTypeId
        let resForm = await getFormByInspectId(this.queryFormByInspectId)
        console.log("resForm",resForm)
        console.log("inspectDetails:", this.inspectDetails);
        try {
          this.formModel = JSON.parse(resForm.data.formContent || '{}')
        } catch (e) {
          console.error('解析formModel失败', e)
          this.formModel = {}
        }
        this.formModel.disabled = true
        console.log("formModel",this.formModel)
        if (this.formData.inspectImageList) {
          for (let i = 0; i < this.formData.inspectImageList.length; i++) {

            this.imageValue.push({url: this.formData.inspectImageList[i]})
          }

        }


      }
    },
    playVoice() {
      console.log('播放录音');
      console.log("this.inspectDetails.inspectAudioList", this.inspectDetails.inspectAudioList);
    
      // 假设你要播放数组中的第一个音频
      if (this.inspectDetails && Array.isArray(this.inspectDetails.inspectAudioList) && this.inspectDetails.inspectAudioList.length > 0) {
        const audioUrl = this.inspectDetails.inspectAudioList[0].inspectAudioUrl;
        
        if (audioUrl) {
          // #ifndef MP-DINGTALK
          innerAudioContext.src = audioUrl.toString(); // 将音频URL转换为字符串
          console.log("innerAudioContext.src", innerAudioContext.src);
          
          innerAudioContext.volume = 1; // 设置音量
          innerAudioContext.onCanplay(() => {
            innerAudioContext.play(); // 播放音频
          });
          
          innerAudioContext.onError((res) => {
            console.error('播放音频错误', res.errMsg); // 错误处理
          });
          // #endif
        } else {
          console.error("没有找到有效的音频URL");
        }
      } else {
        console.error("inspectAudioList 数组为空或无效");
      }
    },

    previewVideo() {
      console.log('播放视频');
	  // 假设你要遍历每个对象，获取其中的 inspectVideoUrl 并存储到一个数组
	  const videoUrls = this.inspectDetails.inspectVideoDtoList.map(item => item.inspectVideoUrl);
	  
	  console.log("所有 inspectVideoUrl:", videoUrls);
	  
	  // 如果你想赋值给 videoSrc（假设只是拿第一个视频 URL）
	  this.videoSrc = videoUrls[0].toString();  // 例如取第一个 URL

      // #ifndef MP-DINGTALK
      const videoContext = uni.createVideoContext('myVideo');
      // videoContext.src = (this.inspectDetails.inspectVideoDtoList).toString();
	  videoContext.src = this.videoSrc;

      console.log(this.videoSrc)

        videoContext.play();
      // 等待视频加载
      // #endif
    },
    videoErrorCallback(event) {
      console.error('视频播放错误:', event);

      // 打印事件的完整对象
      console.log('完整错误对象:', JSON.stringify(event, null, 2));

      // 检查 `target` 是否存在并打印相关信息
      if (event && event.target) {
        console.log('目标元素信息:', event.target);
        console.log('目标元素 ID:', event.target.id);
      }

      // 如果有额外的详细信息，可以再打印
      if (event && event.detail) {
        console.log('错误详情:', event.detail);
      } else {
        console.log('未获取到详细错误信息');
      }
    },
     
    // async uploadImg(tempFiles) {

    //   if (!tempFiles.length) return
    //   const path = tempFiles.pop()

    //   let data = {
    //     name: 'file',
    //     filePath: path.path
    //   }

    //   let res = await uploadImage(data)

    //   this.formData.inspectImageList.push(res.url)

    //   this.uploadImg(tempFiles)
    // },
    handleDelete(e) {
      // 图片删除
      console.log(e.tempFile)

      let {
        url
      } = e.tempFile

      const num = this.formData.inspectImageList.findIndex((e) => e == url)
      this.formData.inspectImageList.splice(num, 1)


    },

    async submitForm() {
      try {
        this.isSubmitDisabled = true;
        
        // 创建一个新的对象来存储提交的数据
        const submitData = {
          ...this.formData,
          // 将 formContent 转换为 JSON 字符串
          formContent: JSON.stringify(this.formContent)
        };
        
        console.log("submitData", submitData);
        
        // 显示loading
        uni.showLoading({
          title: '提交中...'
        });
        
        const res = await alterInspectRecord(submitData);
        
        if (res && res.code === 200) {
          uni.showToast({
            title: '提交成功',
            icon: 'success'
          });
          
          uni.navigateBack({
            delta: 1
          });
        } else {
          throw new Error(res.msg || '提交失败');
        }
      } catch (error) {
        console.error('提交表单错误：', error);
        uni.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isSubmitDisabled = false;
        uni.hideLoading();
      }
    },
    previewImage(attachment, index = 1) {
      // 预览图片
      attachment = JSON.parse(attachment).map((item) => {
        return item.url
      })
      uni.previewImage({
        urls: attachment,
        current: index
      })
      console.log(attachment, 'attachment');
    },
    handleSelect(e) {
      console.log("文件选择", e);
      // 可以在这里处理文件选择逻辑，例如验证文件类型或大小
    },
    
    handleSuccess(e) {
      console.log("文件上传成功", e);
      // 文件上传成功后的处理逻辑
      // if (e.tempFiles && e.tempFiles.length > 0) {
      //   // 这里可以处理上传成功后的逻辑
      //   // 如果需要将上传的图片添加到 formData.inspectImageList
      //   const uploadedFiles = e.tempFiles.map(file => {
      //     return { inspectImageUrl: file.url };
      //   });
      //
      //   // 如果 inspectImageList 不存在，则创建它
      //   if (!this.formData.inspectImageList) {
      //     this.formData.inspectImageList = [];
      //   }
      //
      //   // 添加新上传的图片
      //   this.formData.inspectImageList.push(...uploadedFiles);
      // }
    },
  }
}
</script>

<style lang="scss">

.btn_big {
  padding: 20rpx;
  font-size: 50rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #5b73f9;
  color: #fff;
  border-radius: 10rpx;
}

.uni-forms-item__label {
  font-size: 30px; /* 设置字体大小 */
  // color: red; /* 设置字体颜色 */
  font-weight: bold; /* 设置字体粗细 */
}

.property_info {
  // margin-top: 135rpx;
  // display: grid;
  // gap: 45rpx;

  .customer_item {
    padding: 30rpx 0;

    .content {
      flex-basis: 130rpx;
      margin-right: 30rpx;
    }

    .key {
      flex: 1;
      text-align: right;

    }
  }


}
</style>