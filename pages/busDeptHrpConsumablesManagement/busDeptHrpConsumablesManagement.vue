<template>
  <view class="container">
    <view class="filter-bar">
      <view class="search-row">
        <view class="date-pickers">
          <day-picker
              v-model="dateRange"
              @change="onDateRangeChange"
              :clearIcon="false"
              :disable-future-dates="true"
              returnType="string"
          ></day-picker>
        </view>
        <view class="search-box">
          <input
              class="uni-input"
              v-model="searchKeyword"
              placeholder="输入物品名称或编码"
              @input="onSearch"
          />
        </view>
      </view>
      <view class="action-btns">
        <button class="btn" @click="handleExportAll">导出全部</button>
        <button class="btn" :disabled="!selectedItems.length" @click="handleExportSelected">导出选中</button>
        <button class="btn" :disabled="!selectedItems.length" @click="handleToggleDownloadFlag">
          切换标记
        </button>
      </view>
    </view>
    <view class="list-container">
      <view v-for="item in list" :key="item.id" class="item-card"
            :class="{
					selected: selectedItems.includes(item.id),
					'downloaded': item.dowloadFlag === 1,
					'not-downloaded': item.dowloadFlag === 0
				}"
            @click="toggleSelect(item)">
        <view class="checkbox">
          <uni-icons :type="selectedItems.includes(item.id) ? 'checkbox-filled' : 'circle'" size="20" color="#007AFF"></uni-icons>
        </view>
        <view class="item-content">
          <view class="item-header">
            <text class="item-title">{{ item.productName }}</text>
            <text class="item-spec">{{ item.productSpec }}</text>
          </view>
          <view class="item-info">
            <text>数量: {{ item.totalQuantity }}{{ item.unit }}</text>
            <text>单价: {{ item.productPrice }}</text>
            <text>总价: {{ item.totalPrice }}</text>
          </view>
          <view class="item-footer">
            <text>登记日期: {{ item.addDate }}</text>
            <text>科室: {{ item.deviceUseDeptName }}</text>
          </view>
        </view>
        <view class="download-status" v-if="item.dowloadFlag !== undefined">
          <text class="status-tag" :class="item.dowloadFlag === 1 ? 'downloaded-tag' : 'not-downloaded-tag'">
            {{ item.dowloadFlag === 1 ? '已导出' : '未导出' }}
          </text>
        </view>
      </view>
    </view>
    <view class="pagination-container" v-if="total > 0">
      <view class="pagination">
        <view class="page-btn" :class="{ disabled: pageNum <= 1 }" @click="handlePrevPage">
          上一页
        </view>
        <view class="page-info">
          第 {{pageNum}} 页 / 共 {{totalPages}} 页
        </view>
        <view class="page-btn" :class="{ disabled: pageNum >= totalPages }" @click="handleNextPage">
          下一页
        </view>
      </view>
    </view>
    <view v-if="loading" class="loading">加载中...</view>
    <view v-if="!loading && list.length === 0" class="empty">暂无数据</view>
  </view>
</template>

<script>
import {addUpdateDowloadFlag, addUpdateUnDowloadFlag, getPerHrpmat} from '@/api/repair/hrpmatRecords.js'
import {getInfo} from '@/api/login'
import DayPicker from '@/components/MyFormComponents/time-picker/DayPicker.vue'
import { exportExcel } from '@/utils/exportUtil'

export default {
  components: {
    DayPicker
  },
  data() {
    return {
      list: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      hasMore: true,
      dateRange: [],
      startAddDate: '',
      endAddDate: '',
      repairUserId: null,
      selectedItems: [],
      searchKeyword: '', // 新增搜索关键字
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSize)
    }
  },
  async onLoad() {
    await this.getUserInfo()
    // Initialize dateRange if startAddDate and endAddDate are set
    if (this.startAddDate && this.endAddDate) {
      this.dateRange = [this.startAddDate, this.endAddDate]
    }
    await this.getList()
  },
  methods: {
    async getUserInfo() {
      try {
        const res = await getInfo()
        if (res && res.user && res.user.deptId) {
          this.flowableDeptId = res.user.deptId
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        })
      }
    },
    onSearch() {
      this.pageNum = 1
      this.getList()
    },
    async getList() {
      if (this.loading) return
      this.loading = true
      console.log('获取列表数据，参数：', {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        startAddDate: this.startAddDate,
        endAddDate: this.endAddDate,
        flowableDeptId: this.flowableDeptId,
        searchKeyword: this.searchKeyword
      })
      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          startAddDate: this.startAddDate,
          endAddDate: this.endAddDate,
          flowableDeptId: this.flowableDeptId,
        }

        // 添加搜索条件
        if (this.searchKeyword) {
          // 如果输入的全是数字且长度大于等于5，认为是在搜索产品编码
          if (/^\d{5,}$/.test(this.searchKeyword)) {
            params.productCode = this.searchKeyword
          } else {
            // 否则按产品名称搜索，支持名称中包含数字的情况
            params.productName = this.searchKeyword
          }
        }

        const res = await getPerHrpmat(params)
        if (res.code === 200) {
          this.total = res.total
          this.list = res.rows
          this.hasMore = this.list.length < this.total
        }
      } finally {
        this.loading = false
      }
    },
    // 处理时间范围
    onDateRangeChange(value) {
      if (Array.isArray(value) && value.length === 2) {
        this.startAddDate = value[0]
        this.endAddDate = value[1]
      } else {
        this.startAddDate = ''
        this.endAddDate = ''
      }
      this.pageNum = 1
      this.getList()
    },
    // 处理分页功能
    async handlePrevPage() {
      if (this.pageNum > 1) {
        this.pageNum--
        await this.getList()
      }
    },
    async handleNextPage() {
      if (this.pageNum < this.totalPages) {
        this.pageNum++
        await this.getList()
      }
    },
    toggleSelect(item) {
      const index = this.selectedItems.indexOf(item.id)
      if (index === -1) {
        this.selectedItems.push(item.id)
      } else {
        this.selectedItems.splice(index, 1)
      }
    },
    // 导出全部耗材
    async handleExportAll() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        uni.showToast({
          title: '请选择日期范围',
          icon: 'none'
        })
        return
      }

      // 显示提示框
      uni.showModal({
        content: '导出的Excel文件将自动打开',
        title: '温馨提示',
        success: async (res) => {
          if (res.confirm) {
            try {
              const params = {
                startAddDate: this.startAddDate,
                endAddDate: this.endAddDate,
                repairUserId: this.repairUserId,
                dowloadFlag: 0
              }

              await exportExcel({
                url: '/repair/hrpmatRecords/repairHrpmatRecords/appexport4search',
                params
              })
            } catch (error) {
              console.error('导出失败:', error)
            }
          }
        }
      })
    },
    // 导出选中的耗材
    async handleExportSelected() {
      if (!this.selectedItems.length) {
        uni.showToast({
          title: '请选择要导出的项目',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({
          title: '导出中...'
        })
        await exportExcel({
          url: '/repair/hrpmatRecords/repairHrpmatRecords/appexport4Select',
          params: {
            ids: this.selectedItems.join(',')
          }
        })
      } catch (error) {
        console.error('导出失败:', error)
      } finally {
        uni.hideLoading()
      }
    },
    // 处理标记状态
    async handleToggleDownloadFlag() {
      if (!this.selectedItems.length) {
        uni.showToast({
          title: '请选择要标记的项目',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({
          title: '处理中...'
        })

        // 获取选中项目的当前状态
        const selectedItems = this.list.filter(item => this.selectedItems.includes(item.id))
        const allDownloaded = selectedItems.every(item => item.dowloadFlag === 1)

        // 根据当前状态决定调用哪个接口
        const api = allDownloaded ? addUpdateUnDowloadFlag : addUpdateDowloadFlag
        const res = await api(JSON.stringify(this.selectedItems))

        if (res.code === 200) {
          uni.showToast({
            title: '标记更新成功',
            icon: 'success'
          })
          // 刷新列表
          await this.getList()
          // 清空选中
          this.selectedItems = []
        } else {
          throw new Error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('更新标记失败:', error)
        uni.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    }
  }
}
</script>

<style>
.container {
  /* 分页按钮的高度(44px) + 上下内边距(20px) + 额外空间(10px) */
  padding: 10px 10px 74px;
}

.filter-bar {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 10px;
  padding: 0 10px;
}

.search-row {
  display: flex;
  gap: 10px;
  width: 100%;
}

.date-pickers {
  display: flex;
  gap: 10px;
  flex: 2;
  min-width: 200px;
}

.search-box {
  display: flex;
  flex: 1;
  min-width: 150px;
}

.search-box .uni-input {
  width: 100%;
  height: 70rpx;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 10px;
  font-size: 12px;
}

.search-box .placeholder {
  color: #999;
}

.date-pickers :deep(.uni-date) {
  width: 100%;
}

.date-pickers :deep(.uni-date-editor--x) {
  width: 100%;
}

.date-pickers :deep(.uni-date__x-input) {
  height: 35px;
  text-align: center;
}

.action-btns {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: flex-start;
}

.btn {
  font-size: 14px;
  padding: 0 15px;
  height: 35px;
  line-height: 35px;
  background: #007AFF;
  color: #fff;
  border-radius: 4px;
  white-space: nowrap;
}

.btn[disabled] {
  background: #ccc;
}

.item-card {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 10px;
  box-shadow: 0 2px 8px #f0f1f2;
}

.checkbox {
  padding: 10px;
  margin-right: 10px;
}

.item-content {
  flex: 1;
}

.item-card.selected {
  background: #f0f7ff;
  border: 1px solid #007AFF;
}

.item-card.downloaded {
  background: #f0fff0;
  border: 1px solid #00cc00;
}

.item-card.not-downloaded {
  background: #fff0f0;
  border: 1px solid #cc0000;
}

.item-header {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  margin-bottom: 5px;
}

.item-info, .item-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.download-status {
  margin-left: auto;
}

.status-tag {
  font-size: 12px;
  padding: 2px 5px;
  border-radius: 4px;
}

.downloaded-tag {
  background: #00cc00;
  color: #fff;
}

.not-downloaded-tag {
  background: #cc0000;
  color: #fff;
}

.loading, .empty {
  text-align: center;
  color: #999;
  margin: 20px 0;
}

.pagination-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
}

.page-btn {
  padding: 6px 20px;
  margin: 0 15px;
  background: #f5f5f5;
  border-radius: 4px;
  color: #333;
  font-size: 14px;
}

.page-btn.disabled {
  color: #bbb;
  background: #eee;
}

.page-info {
  font-size: 14px;
  color: #666;
  min-width: 120px;
  text-align: center;
}
</style>
