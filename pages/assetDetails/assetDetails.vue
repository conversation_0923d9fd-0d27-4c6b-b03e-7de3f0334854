<template>
	
	<view class="nav bf">
			<uni-forms :modelValue="formData">
				<uni-forms-item class="flex  align-center p20"  label-width="80px" label="报修名称" name="recordTitle">
					<uni-easyinput type="text" v-model="formData.recordTitle" placeholder="请输入报修名称" />
				</uni-forms-item>
				<uni-forms-item class="flex  align-center p20" label-width="80px" label="详细地址" name="deviceLocation">
					<uni-easyinput type="text" v-model="formData.deviceLocation" placeholder="请输入报修详细地址" />
				</uni-forms-item>
				<uni-forms-item class="flex  align-center p20" label-width="80px" label="使用科室" name="deviceUseDeptName">
					<uni-easyinput type="text" v-model="formData.deviceUseDeptName" placeholder="请输入使用科室" />
				</uni-forms-item>
				<uni-forms-item class="flex  align-center p20" label-width="80px" label="设备名称" name="deviceName">
					<uni-easyinput type="text" v-model="formData.deviceName" placeholder="设备名称" />
				</uni-forms-item>
				<uni-forms-item class="flex  align-center p20" label-width="80px" label="故障描述" name="faultDetail">
					<uni-easyinput type="text" v-model="formData.faultDetail" placeholder="请输入故障描述" />
				</uni-forms-item>
				<!-- 
				<uni-forms-item required name="repairImages" label="上传">
				<view style="margin-right: 20rpx;" v-for="(jtem,jndex) in JSON.parse(formData.repairImages)" :key="jtem.uid"
					@click="previewImage(propertyInfo.attachment,jndex)">
					<image style="width: 120rpx;height: 120rpx;" :src="jtem.url" mode=""></image>
				</view>
				</uni-forms-item> -->
				
			</uni-forms>
			<uni-section title="上传图片" type="line">
				<view class="example-body">
					<uni-file-picker v-model="imageValue" limit="5" title="最多选择5张图片" fileMediatype="image" mode="grid"
						@select="handleSelect" @success="handleSuccess" @delete="handleDelete"></uni-file-picker>
				</view>
			</uni-section>
			
			<button class="btn_big" @click="submitForm">确定</button>
		</view>
	
</template>

<script>
	import {
		assetOneInfo
	} from '@/api/assetInfo';
	import {
		uploadImage
	} from '@/api/system/user'
	import {
		addHrpRepairRecord
	} from '@/api/repair';
	import {
		getAssetNum
	} from '@/common/common_methods';
	export default {
		data() {
			return {
				imageValue:null,
				formData: {
					recordTitle:null,
					deviceName:null,
					cardNumber:null,
          deviceUseDeptName:null,
					deviceLocation:null,
					repairImages:[]
					
				},
				hrpInfo: {
					pageNum: 1,
					pageSize: 10,
					cardNumber: null,
					cardName: null
				}
				
			}
		},
    onLoad(option) {
      // 处理 repairInfo
      if (option.repairInfo) {
        // 对传递的 JSON 字符串进行解码
        var objStr = decodeURIComponent(option.repairInfo);
        // 将 JSON 字符串转换回对象
        var obj = JSON.parse(objStr);
		console.log("obj",obj);
        // 处理 repairInfo 数据
        this.formData.recordTitle = obj.cardName + '报修';
        this.formData.deviceName = obj.cardName;
        this.formData.cardNumber = obj.cardNumber;
        this.formData.deviceUseDeptName = obj.deptName;
        this.formData.deviceLocation = obj.storagePlace;
		this.formData.deviceUseDeptId = obj.deptCode;
		
        console.log(this.formData);
      }

      // 处理 inspectDetails
      if (option.inspectDetails) {
        // 对传递的 JSON 字符串进行解码
        var inspectDetailsStr = decodeURIComponent(option.inspectDetails);
        // 将 JSON 字符串转换回对象
        var inspectDetails = JSON.parse(inspectDetailsStr);

        // 在此根据 formData 数据更新相应的字段或执行其他操作
        console.log(inspectDetails);
        
        this.formData.recordTitle=inspectDetails.recordTitle+'报修';
        this.formData.deviceName=inspectDetails.deviceName;
        this.formData.deviceCode=inspectDetails.deviceCode;
        this.formData.deviceUseDeptName=inspectDetails.deviceUseDeptName;
        this.formData.deviceUseDeptId=inspectDetails.deviceUseDeptId;
        this.formData.deviceLocation=inspectDetails.deviceLocation;
        this.formData.cardNumber=inspectDetails.cardNumber;
        this.formData.deviceTypeOne=inspectDetails.deviceTypeOne // 设备类型一;
        this.formData.deviceTypeOneName='hrp设备';
        this.formData.deviceTypeTwo=inspectDetails.deviceTypeTwo;
        this.formData.deviceTypeTwoName=inspectDetails.deviceTypeTwoName;
        this.formData.deviceTypeThree=inspectDetails.deviceTypeThree;
        this.formData.deviceTypeThreeName=inspectDetails.deviceTypeThreeName;
        this.formData.deviceTypeFour=inspectDetails.deviceTypeFour;
        this.formData.deviceTypeFourName=inspectDetails.deviceTypeFourName;
        this.formData.repairTell=''
        this.formData.repairName=''
        this.formData.repairCostMoney=0
        this.formData.reportName =inspectDetails.reportName;
        this.formData.reportTell = inspectDetails.reportTell; // 修改为 phonenumber

      }
    },
		methods: {
			async handleSelect(res) {
				// 选择图片
				this.uploadImg(res.tempFiles)
				
			
			},
			async handleSuccess(e) {
				
				console.log(e)
				
			
			},
			async uploadImg(tempFiles) {
				
				if (!tempFiles.length) return
				const path = tempFiles.pop()
			
				let data = {
					name: 'file',
					filePath: path.path
				}
				
				let res = await uploadImage(data)
				
				this.formData.repairImages.push(res.url)
				
				this.uploadImg(tempFiles)
				
				
			},
			 handleDelete(e) {
				// 图片删除
				
				let {
					url
				} = e.tempFile
	
				const num = this.formData.repairImages.findIndex((e) => e == url)
				this.formData.repairImages.splice(num, 1)
				
			},
			
			async submitForm(){
			 let res = await addHrpRepairRecord(this.formData);
			 console.log(res)
             if(res ){
				  uni.navigateTo({
					url: '/pages/repairList/myRepair'
				  })
			}
				
			},
			previewImage(attachment, index = 1) {
				// 预览图片
				attachment = JSON.parse(attachment).map((item) => {
					return item.url
				})
				uni.previewImage({
					urls: attachment,
					current: index
				})
				console.log(attachment, 'attachment');
			},
			async getAssetOneInfo(assetId) {
				let res = await assetOneInfo(assetId);
				this.propertyInfo = res
				if (this.propertyInfo == '') {
					uni.showToast({
						title: '未查到相关资产信息',
						icon: 'none'
					})
					this.propertyInfo = {}
					this.propertyIdInput = ''
					// this.flag = true
				}

			},
			getNum(id) {
				return getAssetNum(id)
			},
		}
	}
</script>

<style lang="scss">
	
	.btn_big {
		padding: 20rpx;
		font-size: 50rpx;
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #5b73f9;
		color: #fff;
		border-radius: 10rpx;
	}
	 .uni-forms-item__label {
	    font-size: 30px; /* 设置字体大小 */
	    // color: red; /* 设置字体颜色 */
	    font-weight: bold; /* 设置字体粗细 */
	  }
	.property_info {
		// margin-top: 135rpx;
		// display: grid;
		// gap: 45rpx;

		.customer_item {
			padding: 30rpx 0;

			.content {
				flex-basis: 130rpx;
				margin-right: 30rpx;
			}

			.key {
				flex: 1;
				text-align: right;

			}
		}


	}
</style>