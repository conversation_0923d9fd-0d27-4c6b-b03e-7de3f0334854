<template>
	<view class="p20">
		<view class=" customer_list grid gap">
			<!-- @click="customerBtnClick(item)" -->
			<view class="customer_item bf " v-for="(item,index) in takeOverList" :key="index">
				<view class="title just-sbet ">
					<view class="idCard">
						<view class="" v-for="(jtem, index) in item.resourceId" :key="index">
						       {{ getNum(jtem) }}
						</view>
					</view>
					<view class="tips">
						应收款项
					</view>
				</view>
				<view class="customer_info just-sbet">
					<view class="company">
						合同编号
					</view>
					<view class="clientName">
						{{item.contractNumber}}
					</view>
				</view>
				<view class="customer_info just-sbet">
					<view class="company">
						客户姓名
					</view>
					<view class="clientName">
						{{item.clientName}}
					</view>
				</view>
				<view class="customer_info just-sbet">
					<view class="company">
						应收总数
					</view>
					<view class="clientName" style="color: #cc0000;">
						￥{{item.expectMoney}}
					</view>
				</view>
				<view class="customer_info just-sbet">
					<view class="company">
						实收总数
					</view>
					<view class="clientName" style="color: #cc0000;">
						￥{{item.realMoney}}
					</view>
				</view>

				<view class="customer_info just-sbet">
					<view class="company">
						差额
					</view>
					<view class="clientName" style="color: #cc0000;">
						￥{{item.money}}
					</view>
				</view>

				<!-- 			<view class="customer_info just-sbet">
								<view class="company">
									收款时间
								</view>
								<view class="clientName">
									{{item.takeOverTime}}
								</view>
							</view> -->
				<!-- 		<view class="customer_info just-sbet">
					<view class="company">
						备注
					</view>
					<view class="clientName">
						{{item.memo==null?'':item.memo}}
					</view>
				</view> -->
				<!-- 	<view class="customer_btn">
					查看详情
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	import {
		takeOverInfo
	} from '../../api/takeOver';
	import {
		getAssetNum
	} from '../../common/common_methods';
	export default {
		data() {
			return {
				takeOverList: [],
				customList: [],
				query: {
					pageNum: 1,
					pageSize: 15
				},
				searchValue: "",
				flag: false
			};
		},
		// onReachBottom() {
		// 	this.query.pageNum++
		// 	this.getCustomList()

		// },
		onLoad() {
			this.getTakeOverList()
		},
		methods: {
			async getTakeOverList() {
				let res = await takeOverInfo();
				this.takeOverList = res.map((item,index)=>{
					return {
						...item,
						resourceId: Array.isArray(JSON.parse(item.resourceId)) ? JSON.parse(item.resourceId) :[Number(item.resourceId)]
					}
				})
				console.log(this.takeOverList);
			},
			getNum(id) {
				return getAssetNum(id)
			}
		}
	}
</script>

<style lang="scss">
	.customer_list {

		.customer_item {
			box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
			padding: 30rpx;
			border-radius: 10rpx;


			.title {
				margin: 10rpx 0;

				.idCard {
					color: #000;
				}

				.tips {
					border-radius: 20rpx;
					padding: 5rpx 20rpx;
					background-color: rgba(91, 115, 249, 0.1);
					color: #5b73f9;
				}
			}

			.customer_info {
				border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
				padding-bottom: 20rpx;
				margin-top: 40rpx;
				color: #868686;

				.company {}

				.clientName {
					color: #333;
					font-weight: bold;
				}
			}

			.customer_btn {
				margin-top: 45rpx;
				padding: 20rpx;
				text-align: center;
				background-color: #5b73f9;
				color: #fff;
				border-radius: 10rpx;
			}
		}
	}
</style>