<template>
	<view class="container">
		<view class="nav bf">
			<view class="flex  align-center p20">
				<!-- 			<uni-search-bar type="number" class="w-full" bgColor="#fff" radius="5" placeholder="请输入资产编号" clearButton="auto"
				@confirm="search" /> -->
				<uni-easyinput prefixIcon="search" @focus="flag=true"   v-model="propertyIdInput" 
					placeholder="请输入设备编号或者名称">
				</uni-easyinput>
				<!-- <uni-icons type="scan" style="margin-left: 20rpx;" size="25" @click="scan"></uni-icons> -->
			</view>
			<view class="p20" v-if="flag">
				<view class="btn_big" @click="search">
					搜索
				</view>

			</view>
		</view>
		<view class="content">
			<view class="asset_list p20">
				<view class="p20 bf card asset_info" v-for="item in hrpRepairList" :key="item.id">
					<view class="just-sbet " >
						<view class="key">
							二级分类 
						</view>	
						<view class="">
							{{item.class1Name}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							三级分类 
						</view>	
						<view class="">
							{{item.class2Name}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							设备名 
						</view>	
						<view class="">
							{{item.cardName}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							编号 
						</view>	
						<view class="">
							{{item.cardNumber}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							使用科室
						</view>
						<view class="">
							{{item.deptName}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							设备状态
						</view>
						<view class="status-badge" :class="{
							'status-enabled': item.lifecycleState === 0,
							'status-repair': item.lifecycleState === 1,
							'status-disabled': item.lifecycleState === 2,
							'status-scrapped': item.lifecycleState === 3,
							'status-unknown': item.lifecycleState !== 0 && item.lifecycleState !== 1 && item.lifecycleState !== 2 && item.lifecycleState !== 3
						}">
							{{getStatusName(item.lifecycleState, item.lifecycleStateName)}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
						</view>
						<!-- <view class="tips center" @click="hrpRepairReport(item)" >
							报修<uni-icons type="forward" color="#fff"></uni-icons>
						</view> -->
						<view class="tips center" @click="hrpRepairReport" :data-index="item" >
							报修<uni-icons type="forward" color="#fff"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 分页栏 -->
		<view class="pagination-container bf" v-if="total > 0">
			<view class="pagination">
				<view class="page-btn" :class="{ disabled: hrpInfo.pageNum <= 1 }" @click="handlePrevPage">
					上一页
				</view>
				<view class="page-info">
					第 {{hrpInfo.pageNum}} 页 / 共 {{totalPages}} 页
				</view>
				<view class="page-btn" :class="{ disabled: hrpInfo.pageNum >= totalPages }" @click="handleNextPage">
					下一页
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		listHrpRepairRecord
	} from '@/api/repair';
	import {
		getAssetNum
	} from '@/common/common_methods';
	export default {
		data() {
			return {
				propertyIdInput: "", // 输入的id
				flag: false, // 搜索按钮是否显示
				hrpRepairList: [],
				total: 0, // 总记录数
				hrpInfo: {
					pageNum: 1,
					pageSize: 10,
					cardNumber: null,
					cardName: null
				}
			}
		},
		computed: {
			totalPages() {
				return Math.ceil(this.total / this.hrpInfo.pageSize)
			}
		},
		onLoad() {
			this.getHrpRepairList()
		},
		methods: {
			search() {
				console.log("daf;j")
				this.hrpInfo.pageNum=1
				if(this.propertyIdInput==''){
					this.hrpInfo.cardName=''
					this.hrpInfo.cardNumber=''
				}
				else if(isNaN(this.propertyIdInput)){
					this.hrpInfo.cardName=''
					this.hrpInfo.cardNumber=this.propertyIdInput
				}
				else{
					this.hrpInfo.cardName=this.propertyIdInput
					this.hrpInfo.cardNumber=''
				}
				this.getHrpRepairList()
			},
			scan() {
				uni.scanCode({
					scanType: ['barCode', 'qrCode'],
					success: async (res) => {
						console.log('res');
						let id = res.result.slice(7)
						this.hrpRepairReport(id)
					}
				})
			},
			hrpRepairReport(item){
				console.log(item)
				var objStr = JSON.stringify(item.currentTarget.dataset.index)
				uni.navigateTo({
					url: '/pages/assetDetails/assetDetails?repairInfo='+encodeURIComponent(objStr)
				})
			},
			async getHrpRepairList() {
				let res = await listHrpRepairRecord(this.hrpInfo);
				if(res) {
					console.log(res.rows)
					this.hrpRepairList = res.rows;
					this.total = res.total || 0;
				}
			},
			getNum(id) {
				return getAssetNum(id)
			},
			// 处理上一页
			handlePrevPage() {
				if (this.hrpInfo.pageNum > 1) {
					this.hrpInfo.pageNum--;
					this.getHrpRepairList();
				}
			},
			// 处理下一页
			handleNextPage() {
				if (this.hrpInfo.pageNum < this.totalPages) {
					this.hrpInfo.pageNum++;
					this.getHrpRepairList();
				}
			},

			// 获取状态名称
			getStatusName(lifecycleState, lifecycleStateName) {
				// 如果有状态名称，优先使用状态名称
				if (lifecycleStateName) {
					return lifecycleStateName;
				}

				// 根据状态码返回对应名称
				const statusMap = {
					0: '启用',
					1: '维修',
					2: '停用',
					3: '报废'
				};

				return statusMap[lifecycleState] || '未知';
			},
		},
	}
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		position: relative;
		padding-bottom: 100rpx; /* 为分页栏留出空间 */
	}

	.content {
		flex: 1;
		overflow-y: auto;
	}

	.annex {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
	}

	.nav {
		position: fixed;
		width: 100%;
		top: 0;
		z-index: 1024;
		box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
	}
	.asset_list{
	margin-top: 135rpx;
	display: grid;
	gap: 20rpx;
	padding-bottom: 20rpx; /* 增加底部内边距，防止最后一项被分页栏遮挡 */
	.asset_info{
		display: grid;
		gap:25rpx;
		.tips{
			    color: #fff;
			    background: #3296fa;
			    padding: 5rpx 10rpx;
			    border-radius: 20rpx;
		}
			.key {
				flex-basis: 150rpx;
				margin-right: 30rpx;
			}

	}
	}

	.pagination-container {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 20rpx;
		background: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		z-index: 99;

		.pagination {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 20rpx;
			
			.page-btn {
				padding: 10rpx 30rpx;
				background: #3296fa;
				color: #fff;
				border-radius: 10rpx;
				
				&.disabled {
					background: #ccc;
					pointer-events: none;
				}
			}
			
			.page-info {
				color: #666;
			}
		}
	}

	// 状态标签样式
	.status-badge {
		padding: 4rpx 12rpx !important;
		border-radius: 12rpx !important;
		font-size: 24rpx !important;
		color: #fff !important;
		text-align: center !important;
		display: inline-block !important;
		white-space: nowrap !important;
		width: auto !important;
		min-width: 60rpx !important;
		max-width: 120rpx !important;
		flex: none !important; // 防止被父级flex影响
		margin-left: 0 !important; // 重置margin

		&.status-enabled {
			background: #52c41a !important; // 启用 - 绿色
			color: #fff !important;
		}

		&.status-repair {
			background: #fa8c16 !important; // 维修 - 橙色
			color: #fff !important;
		}

		&.status-disabled {
			background: #8c8c8c !important; // 停用 - 灰色
			color: #fff !important;
		}

		&.status-scrapped {
			background: #f5222d !important; // 报废 - 红色
			color: #fff !important;
		}

		&.status-unknown {
			background: #d9d9d9 !important; // 未知 - 浅灰色
			color: #fff !important;
		}
	}
</style>