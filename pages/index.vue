<template>
	<view class="container">
		<view class="nav bf">
			<view class="flex  align-center p20">
				<!-- 			<uni-search-bar type="number" class="w-full" bgColor="#fff" radius="5" placeholder="请输入资产编号" clearButton="auto"
				@confirm="search" /> -->
				<uni-easyinput prefixIcon="search" @focus="flag=true" @blur="flag=false" v-model="propertyIdInput" type="number"
					placeholder="请输入资产编号">
				</uni-easyinput>
				<uni-icons type="scan" style="margin-left: 20rpx;" size="25" @click="scan"></uni-icons>
			</view>
			<view class="p20" v-if="flag">
				<view class="btn_big" @click="search">
					搜索
				</view>

			</view>
		</view>
			<view class="asset_list p20">
				<view class="p20 bf card asset_info" v-for="item in assetList" :key="item.id">
					<view class="just-sbet " >
						<view class="key">
							资产编号 
						</view>	
						<view class="">
							{{getNum(item.id)}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							原产权单位 
						</view>	
						<view class="">
							{{item.oldOwner==null?"未知":item.oldOwner}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
							新地址 
						</view>	
						<view class="">
							{{item.newAddress==null?"未知":item.newAddress}}
						</view>
					</view>
					<view class="just-sbet ">
						<view class="key">
						</view>	
						<view class="tips center" @click="toAssetInfo(item.id)" >
							查看详情<uni-icons type="forward" color="#fff"></uni-icons>
						</view>
					</view>
				</view>
			</view>

	</view>
</template>

<script>
	import {
		assetInfo
	} from '@/api/assetInfo';
	import {
		getAssetNum
	} from '@/common/common_methods';
	export default {
		data() {
			return {

				propertyIdInput: "", // 输入的id
				flag: false, // 搜索按钮是否显示
				assetList: [],
				assetInfoKey: {
					"id": "",
					"oldOwner": "",
					"assetProperty": "",
					"street": "",
					"address": "",
					"newAddress": "",
					"orginType": "",
					"notes": "",
					"propertyNumber": "",
					"newOwner": "",
					"propertyAreaStart": "",
					"propertyAreaEnd": "",
					"roomSquareStart": "",
					"roomSquareEnd": "",
					"storefrontSquareStart": "",
					"storefrontSquareEnd": "",
					"assetValueStart": "",
					"assetValueEnd": "",
					"buildingRate": "",
					"createTimeBegin": "",
					"createTimeEnd": "",
					"buildingYearBegin": "",
					"buildingYearEnd": "",
					"pageNum": 1,
					"pageSize": 15
				}
			}
		},
		onLoad() {
			this.getAssetList()
		},
		onReachBottom() {
			this.assetInfoKey.pageNum++
			this.getAssetList()
			
		},
		methods: {

			search() {
				this.toAssetInfo(this.propertyIdInput)
			},
			scan() {
				uni.scanCode({
					scanType: ['barCode', 'qrCode'],
					success: async (res) => {
						// console.log(res);
						let id = res.result.slice(7)
					this.toAssetInfo(id)
					}
				})
			},
			toAssetInfo(id){
				uni.navigateTo({
					url: `/pages/assetDetails/assetDetails?assetId=${id}`,
				})
			},
			async getAssetList() {
				let res = await assetInfo(this.assetInfoKey);
				if(res && res.list && res.list.length){
					this.assetList =[...this.assetList,... res.list].filter((obj, index, self) =>
  index === self.findIndex(item => item.id === obj.id)
);
				}

			},
			getNum(id) {
				return getAssetNum(id)
			},


		},

	}
</script>

<style lang="scss">
	.annex {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
	}

	.nav {
		position: fixed;
		width: 100%;
		top: 0;
		z-index: 1024;
		box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
	}
	.asset_list{
	margin-top: 135rpx;
	display: grid;
	gap: 20rpx;
	.asset_info{
		display: grid;
		gap:25rpx;
		.tips{
			    color: #fff;
			    background: #3296fa;
			    padding: 5rpx 10rpx;
			    border-radius: 20rpx;
		}
			.key {
				flex-basis: 150rpx;
				margin-right: 30rpx;
			}

	}
	}
</style>