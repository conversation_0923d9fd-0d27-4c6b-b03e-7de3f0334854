<template>
	<view>
		<view class="bf">
			<uni-segmented-control :current="current" :values="items" style-type="text" @clickItem="onClickItem" />
		</view>
		<div class="tabBox">
			<div>请选择部门：</div>
			<div @click="toggle">
				<text>{{value===''?'选择部门':value}}</text>
				<uni-icons type="bottom" size="16" color="#8c8c8c"></uni-icons>
			</div>
		</div>
		<div class="tabBox" style="color: black;">
			<div :class="active===0?'active':''" @click="timeTab(0)">{{current===0?'今天':current===1?'今月':'今年'}}</div>
			<div :class="active===1?'active':''" @click="timeTab(1)">{{current===0?'上一天':current===1?'上一月':'上一年'}}</div>
			<div class="js" :class="active===2?'active':''">
				<picker ref="picker" mode="date" :value="dateValue" @change="bindDateChange" :fields="fields">
					<view>{{dateValue}}</view>
				</picker>
				<uni-icons type="bottom" size="16" color="#8c8c8c"></uni-icons>
			</div>
		</div>

		<uni-card title="数据图表">
			<qiun-data-charts type="mix" :opts="opts" :chartData="chartData" :ontouch="true" />
		</uni-card>
		<uni-card>
			<template v-slot:title>
				<view
					style="display: flex; justify-content: space-between; padding: 20rpx 10rpx;font-size: 30rpx; border-bottom: #ebeef4 solid 1rpx; line-height: 80rpx;">
					<view>数据列表 (30天)</view>
					<view style="width: 60%; height: 80rpx;">
						<uni-forms label-width="80" style="width: 100%; height: 100%;">
							<uni-forms-item label="电价/元">
								<uni-easyinput style="width: 150rpx; height: 30rpx;" v-model="price"></uni-easyinput>
							</uni-forms-item>
						</uni-forms>
					</view>


				</view>
			</template>
			<view
				style="display: grid; grid-template-columns: repeat(5, 1fr); text-align: center; font-weight: 800; margin-bottom: 10px;">
				<view>时间</view>
				<view>电量</view>
				<view>电费</view>
				<view>环比</view>
				<view>同比</view>
			</view>
			<scroll-view scroll-y style="height: 300rpx;">
				<view
					style="display: grid; grid-template-columns: repeat(5, 1fr);  text-align: center; font-size: 23rpx;"
					v-for="(item,index) in timeList" :key="index">
					<view>{{timeList[index]+''}}</view>
					<view>{{dataList[index]}}</view>
					<view>{{(dataList[index]*price).toFixed(2)}}元</view>
					<view>{{qoqList[index]+''}}</view>
					<view>{{yoyList[index]+''}}</view>
				</view>
			</scroll-view>
		</uni-card>
		<uni-popup ref="popup" background-color="#fff">
			<view style="width: 100%;height: 60vh; border-radius: 30rpx;">
				<view style="display: flex; justify-content: space-between; margin: 20rpx 10rpx;">
					<text style="font-size: 16px;">选择部门</text>
					<uni-icons type='closeempty' @click="$refs.popup.close()" size="18"></uni-icons>
				</view>

				<view style="padding: 10px;">
					<scroll-view style="height: 400px;" scroll-y="true">
						<TreeItem v-for="(item,index) in treeData" :key="item.id" :node="item" @clickNode="clickNode">
						</TreeItem>
					</scroll-view>
				</view>
			</view>

		</uni-popup>
	</view>
</template>

<script>
	import {
		getDeptTree,
		dahuaElectricityHour
	} from '../../api/energy.js';

	import TreeItem from '../../components/tree-item.vue'
	import {
		getToken
	} from '../../utils/auth.js';
	export default {
		data() {
			return {
				current: 0,
				items: ['日耗电量', '月耗电量', '年耗电量'],
				treeData: [],
				value: '赣州市肿瘤医院',
				dateValue: '',
				fields: 'day',
				deptId: -1,
				active: 0,
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					enableScroll: true,
					ontouch: true,
					dataLabel: false,
					legend: {},
					xAxis: {
						disableGrid: true,
						scrollShow: true,
						itemCount: 4,
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						gridColor: "#CCCCCC",
						padding: 10,
						showTitle: true,
						data: [{
								position: "right",
								title: "折线图"
							},
							{
								position: "left",
								title: "柱状图"
							}
						]
					}
				},
				chartData: {},
				dataList: [],
				qoqList: [],
				yoyList: [],
				timeList: [],
				price: 0.7

			}
		},
		onLoad() {
			this.getUserTree();
			this.getDate(0)
			this.getServerData();
			this.getDataList()
		},
		methods: {
			onClickItem(e) {
				this.current = e.currentIndex
				this.fields = this.current === 0 ? 'day' : this.current === 1 ? 'month' : 'year'
				this.getDate(this.current)
				this.getServerData()
			},
			async getUserTree() {
				const res = await getDeptTree();
				const data = this.addParentId(res.data, null); // 设置初始状态
				this.treeData = data
			},
			addParentId(nodes, parentId) {
				for (let i = 0; i < nodes.length; i++) {
					const node = nodes[i];
					if (node.children && Array.isArray(node.children)) {
						this.addParentId(node.children, node.id); // 递归处理子节点
					}
					node.parentId = parentId; // 设置当前节点的 parentId
					node.isOpen = false
				}
				return nodes
			},
			toggle() {
				this.$refs.popup.open('bottom')
			},
			clickNode(item) {
				this.$emit('clickNode', item)
				this.value = item.label
				this.deptId = item.id
				this.getServerData()
				this.getDataList()
				this.$refs.popup.close('bottom')
			},
			async bindDateChange(e) {
				this.timeTab(2)
				let res = await dahuaElectricityHour({
					deptId: this.deptId,
					dateType: this.current,
					dateTime: e.detail.value
				})
				if (res.data === undefined || res.data.dataSize === 0) {
					return uni.showToast({
						title: '该时间段无能耗数据'
					})
				}
				this.dateValue = e.detail.value
				this.dateValue = this.dateValue.replace(/\//g, '-');
				this.getServerData()


			},
			getDate(dateType) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				if (dateType === 0) {
					this.dateValue = `${year}-${month}-${day}`;
				} else if (dateType === 1) {
					this.dateValue = `${year}-${month}`;
				} else {
					this.dateValue = `${year}`;
				}

			},
			async getServerData() {
				let res = await dahuaElectricityHour({
					deptId: this.deptId,
					dateType: this.current,
					dateTime: this.dateValue
				})

				let charObj = {
					categories: [],
					series: [{
							name: "耗电量",
							type: "column",
							data: res.data.dataList.map(item => item === '' ? 0 : +item)
						}, {
							name: "同比",
							type: "line",
							color: "#4b5bec",
							data: res.data.yoyList.map(item => item === '' ? 0 : +item)
						},
						{
							name: "环比",
							type: "line",
							color: "#20dcc1",
							data: res.data.qoqList.map(item => item === '' ? 0 : +item)
						}
					]
				};
				if (this.current === 0) {
					for (let i = 0; i <= res.data.dataSize; i++) {
						let time = i >= 10 ? i : '0' + i
						charObj.categories.push(time + ':00')
					}
				} else {
					for (let i = 0; i <= res.data.dataSize; i++) {
						let time = i >= 10 ? i : '0' + i
						charObj.categories.push(time)
					}
				}

				this.chartData = JSON.parse(JSON.stringify(charObj));
			},
			async getDataList() {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				let time = `${year}-${month}`;
				let res = await dahuaElectricityHour({
					deptId: 103,
					dateType: 1,
					dateTime: time
				})
				this.dataList = res.data.dataList
				this.qoqList = res.data.qoqList
				this.yoyList = res.data.yoyList

				const date1 = new Date();
				let year1 = date1.getFullYear();
				let month1 = date1.getMonth();
				let day1 = date1.getDate();
				month1 = month1 > 9 ? month1 : '0' + month1;
				let time1 = `${year1}-${month1}`;
				let res1 = await dahuaElectricityHour({
					deptId: this.deptId,
					dateType: 1,
					dateTime: time1
				})
				res1.data.dataList.reverse()
				res1.data.qoqList.reverse()
				res1.data.yoyList.reverse()
				for (let i = 0; i < 30 - (res.data.dataSize); i++) {
					this.dataList.push(res1.data.dataList[i])
					this.qoqList.push(res1.data.qoqList[i])
					this.yoyList.push(res1.data.yoyList[i])
				}

				for (let i = 0; i < 30; i++) {
					const today = new Date();
					// 复制当前日期
					let date = new Date(today);
					// 减去i天
					date.setDate(today.getDate() - i);
					// 格式化日期为 YYYY-MM-DD
					const formattedDate = this.formatDate(date);
					// 将格式化的日期添加到数组中
					this.timeList.push(formattedDate);
				}

			},
			formatDate(date) {
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从 0 开始的
				const day = date.getDate().toString().padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			timeTab(index) {
				this.active = index
				let date = new Date()
				if (this.current === 0 && this.active === 1) {
					date.setDate(date.getDate() - 1);
					this.dateValue = this.formatDate(date)
				} else if (this.current === 1 && this.active === 1) {
					date.setMonth(date.getMonth() - 1);
					this.dateValue = this.formatDate(date).slice(0,7)

				} else if (this.current === 2 && this.active === 1) {
					date.setFullYear(date.getFullYear() - 1);
					this.dateValue = this.formatDate(date).slice(0,4)
				}
				if(this.active===0){
					this.getDate(this.current)
				}
				console.log(this.dateValue);
				this.getServerData()
			}

		}
	}
</script>

<style>
	page {
		padding-bottom: 20rpx;
	}

	.tabBox {
		display: flex;
		justify-content: space-between;
		font-size: 30rpx;
		background-color: #fff;
		padding: 30rpx;
		color: #8c8c8c;
		line-height: 20rpx;
		margin: 15rpx 30rpx;
		border-radius: 15rpx;
		border: 1px solid #EBEEF5;
		box-shadow: rgba(0, 0, 0, 0.08) 0px 0px 3px 1px;
	}

	.active {
		color: #3f6af8;
	}
</style>