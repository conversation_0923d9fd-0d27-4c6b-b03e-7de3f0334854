<template>
	<view class="nav bf">
		<uni-forms ref='form' :modelValue="formData" :rules="rules">
			<uni-forms-item class="flex align-center p20" label-width="70px" label="报修区域" 
			required name="repairArea">
				<uni-data-picker
					v-model="formData.repairArea"
					:localdata="areaTree"
					popup-title="请选择报修区域"
					@change="handleAreaChange"
				/>
			</uni-forms-item>

			<uni-forms-item class="flex align-center p20" label="详细地址" label-width="70px"
			required name="detailAddress">
				<uni-easyinput type="text" v-model="formData.detailAddress" placeholder="请输入报修详细地址" />
			</uni-forms-item>

			<uni-forms-item class="flex align-center p20" label="报修类型" label-width="70px"
			required name="repairType">
				<uni-data-picker
					v-model="formData.repairType"
					:localdata="repairTypeTree"
					popup-title="请选择报修类型"
					@change="handleTypeChange"
				/>
			</uni-forms-item>

			<uni-forms-item class="flex align-center p20" label="报修详细" label-width="70px"
			name="repairDetail">
				<uni-easyinput type="text" v-model="formData.repairDetail" placeholder="请输入报修详细信息" />
			</uni-forms-item>

			<uni-forms-item class="flex align-center p20" label="使用科室" label-width="70px"
			required name="deviceUseDeptName">
				<uni-data-picker
					v-model="formData.deviceUseDeptId"
					:localdata="deptTree"
					popup-title="请选择使用科室"
					@change="handleDeptChange"
				/>
			</uni-forms-item>

			<uni-forms-item class="flex align-center p20" label="报修人" label-width="70px"
			required name="repairPerson">
				<uni-easyinput type="text" v-model="formData.repairPerson" placeholder="请输入报修人姓名" />
			</uni-forms-item>

			<uni-forms-item class="flex align-center p20" label="联系方式" label-width="70px"
			required name="contactInfo">
				<uni-easyinput type="text" v-model="formData.contactInfo" placeholder="请输入联系电话" />
			</uni-forms-item>
		</uni-forms>

		<uni-section title="上传图片" type="line">
			<view class="example-body">
				<uni-file-picker v-model="imageValue" limit="5" title="最多选择5张图片" fileMediatype="image" mode="grid"
					@select="handleSelect" @success="handleSuccess" @delete="handleDelete"></uni-file-picker>
			</view>
		</uni-section>
		
		<button class="btn_big" @click="submitForm">提交报修</button>
	</view>
</template>

<script>
import { uploadImage } from '@/api/system/user'
import { getListByParent } from '@/api/commservice/repair'
import { getCategory2Options } from '@/api/commservice/repair'
import { getUserProfile } from '@/api/system/user'
import { addNewOther } from '@/api/repair'
import { getDeptTree } from '@/api/commservice/comSysDept'

export default {
	data() {
		return {
			imageValue: null,
			areaTree: [],
			repairTypeTree: [],
			deptTree: [],
			userInfo: {},
			rules: {
				repairArea: {
					rules: [{
						required: true,
						errorMessage: '请选择报修区域'
					}]
				},
				detailAddress: {
					rules: [{
						required: true,
						errorMessage: '请输入详细地址'
					}, {
						minLength: 3,
						errorMessage: '地址不能少于3个字符'
					}]
				},
				repairType: {
					rules: [{
						required: true,
						errorMessage: '请选择报修类型'
					}]
				},
				repairDetail: {
					rules: [{
						minLength: 3,
						errorMessage: '报修详细不能少于3个字符'
					}]
				},
				deviceUseDeptName: {
					rules: [{
						required: true,
						errorMessage: '请输入使用科室'
					}]
				},
				repairPerson: {
					rules: [{
						required: true,
						errorMessage: '请输入报修人'
					}, {
						minLength: 2,
						errorMessage: '报修人姓名不能少于2个字符'
					}]
				},
				contactInfo: {
					rules: [{
						required: true,
						errorMessage: '请输入联系方式'
					}, {
						validator: (rule, value, callback) => {
							console.log('验证信息：', {
								输入值: value,
								类型: typeof value,
								长度: value.length,
								是否符合格式: /^1[3-9]\d{9}$/.test(value)
							});
							// 先转换为字符串再验证
							const phoneStr = String(value).trim();
							if (phoneStr.length !== 11) {
								return false;
							}
							if (!/^1[3-9]\d{9}$/.test(phoneStr)) {
								return false;
							}
							return true;
						},
						errorMessage: '请输入正确的手机号码'
					}]
				}
			},
			formData: {
				repairArea: [],
				repairAreaOneId: '',
				repairAreaOneName: '',
				repairAreaTwoId: '',
				repairAreaTwoName: '',
				repairType: [],
				repairTypeName: '',
				repairDetail: '',
				repairPerson: '',
				contactInfo: '',
				repairImages: [],
				deviceUseDeptId: '',
				deviceUseDeptName: ''
			}
		}
	},
	created() {
		this.getUserInfo().then(() => {
			this.getRepairAreas()
			this.getRepairTypeTree()
			this.getDepartmentTree()
		})
	},
	methods: {
		async getUserInfo() {
			try {
				const res = await getUserProfile()
				if (res.code === 200) {
					this.userInfo = res.data
					if (!this.formData.repairPerson) {
						this.formData.repairPerson = this.userInfo.nickName || this.userInfo.userName
					}
					if (!this.formData.contactInfo) {
						this.formData.contactInfo = this.userInfo.phonenumber
					}
					if (this.userInfo.dept) {
						this.formData.deviceUseDeptId = this.userInfo.dept.deptId
						this.formData.deviceUseDeptName = this.userInfo.dept.deptName
						// this.formData.repairDetail = this.userInfo.dept.deptName
						this.formData.detailAddress = this.userInfo.dept.deptName
					}
					console.log('获取到的用户信息：', this.userInfo)
				}
				return Promise.resolve()
			} catch (error) {
				console.error('获取用户信息失败：', error)
				uni.showToast({
					title: '获取用户信息失败',
					icon: 'none'
				})
				return Promise.reject(error)
			}
		},
		async getRepairAreas() {
			try {
				const res = await getListByParent(0)
				if (res.code === 200) {
					// 处理原有区域数据
					this.areaTree = await Promise.all(res.data.map(async item => {
						const childRes = await getListByParent(item.id)
						return {
							text: item.areaName,
							value: item.id,
							children: childRes.code === 200 ? childRes.data.map(child => ({
								text: child.areaName,
								value: child.id
							})) : []
						}
					}))
					
					// 添加用户部门作为新的数据项
					if (this.userInfo.dept) {
						// 将用户部门添加到数组开头
						this.areaTree.unshift({
							text: this.userInfo.dept.deptName,
							value: this.userInfo.dept.deptId,
							children: []
						})
						
						// 设置用户部门为默认选中的区域
						this.formData.repairArea = [{
							text: this.userInfo.dept.deptName,
							value: this.userInfo.dept.deptId
						}]
						this.formData.repairAreaOneId = this.userInfo.dept.deptId
						this.formData.repairAreaOneName = this.userInfo.dept.deptName
						// 将二级区域设置为与一级区域一致
						this.formData.repairAreaTwoId = this.userInfo.dept.deptId
						this.formData.repairAreaTwoName = this.userInfo.dept.deptName
					}
				}
			} catch (error) {
				console.error('获取报修区域失败：', error)
				uni.showToast({
					title: '获取报修区域失败',
					icon: 'none'
				})
			}
		},
		handleAreaChange(e) {
			console.log('区域选择变化：', e);
			const selectedValues = e.detail.value;
			if (selectedValues && selectedValues.length > 0) {
				// 设置一级区域
				this.formData.repairAreaOneId = selectedValues[0].value;
				this.formData.repairAreaOneName = selectedValues[0].text;
				
				// 设置二级区域
				if (selectedValues.length > 1) {
					// 如果有选择二级区域
					this.formData.repairAreaTwoId = selectedValues[1].value;
					this.formData.repairAreaTwoName = selectedValues[1].text;
				} else if (this.userInfo.dept && selectedValues[0].value === this.userInfo.dept.deptId) {
					// 如果选择了默认部门，将二级区域设置为与一级区域一致
					this.formData.repairAreaTwoId = this.userInfo.dept.deptId;
					this.formData.repairAreaTwoName = this.userInfo.dept.deptName;
				} else {
					// 其他情况下清空二级区域
					this.formData.repairAreaTwoId = 0;
					this.formData.repairAreaTwoName = '';
				}
				
				// 更新表单的repairArea值
				this.formData.repairArea = selectedValues;
			}
		},
		async getRepairTypeTree() {
			try {
				const res1 = await getCategory2Options(0)
				if (res1.code === 200) {
					this.repairTypeTree = await Promise.all(res1.data.map(async item => {
						const res2 = await getCategory2Options(item.otherTypeId)
						return {
							text: item.typeName,
							value: item.otherTypeId,
							children: res2.code === 200 ? res2.data.map(subItem => ({
								text: subItem.typeName,
								value: subItem.otherTypeId
							})) : []
						}
					}))
				}
			} catch (error) {
				console.error('获取报修类型失败：', error)
				uni.showToast({
					title: '获取报修类型失败',
					icon: 'none'
				})
			}
		},
		/**
		 * 处理报修类型选择变化
		 * @param {Object} e - 事件对象
		 * @param {Array} e.detail.value - 选中的分类数据数组，每个对象包含value(分类ID)和text(分类名称)
		 */
		handleTypeChange(e) {
			console.log('类型选择变化：', e);
			const selectedValues = e.detail.value;
			if (selectedValues && selectedValues.length > 0) {
				// 设置报修类型ID和名称
				this.formData.repairType = selectedValues.map(item => item.value);
				this.formData.repairTypeName = selectedValues.map(item => item.text).join(' - ');
				
				// 设置设备名称（直接拼接两层报修类型）
				if (selectedValues.length > 1) {
					this.formData.deviceName = selectedValues.map(item => item.text).join('');
					// 设置报修详细为报修类型拼接结果加上"故障"
					this.formData.repairDetail = selectedValues.map(item => item.text).join('') + '故障';
				} else if (selectedValues.length === 1) {
					this.formData.deviceName = selectedValues[0].text;
					// 设置报修详细为报修类型加上"故障"
					this.formData.repairDetail = selectedValues[0].text + '故障';
				} else {
					this.formData.deviceName = '';
					this.formData.repairDetail = '';
				}
			}
		},
		async handleSelect(res) {
			this.uploadImg(res.tempFiles)
		},
		async handleSuccess(e) {
			console.log('上传成功', e)
		},
		async uploadImg(tempFiles) {
			if (!tempFiles.length) return
			const path = tempFiles.pop()
			
			let data = {
				name: 'file',
				filePath: path.path
			}
			
			let res = await uploadImage(data)
			this.formData.repairImages.push(res.url)
			this.uploadImg(tempFiles)
		},
		handleDelete(e) {
			let { url } = e.tempFile
			const num = this.formData.repairImages.findIndex((e) => e == url)
			this.formData.repairImages.splice(num, 1)
		},
		formatDateTime(date) {
			const pad = (num) => (num < 10 ? `0${num}` : num)
			return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
		},
		async submitForm() {
			this.$refs.form.validate().then(async result => {
				try {
					const submitData = {
						recordTitle: `${this.formData.repairTypeName}-${this.formData.repairAreaOneName}-${this.formData.repairAreaTwoName}`,
						recordTime: this.formatDateTime(new Date()),
						deviceTypeOne: 2,
						deviceTypeTwo: this.formData.repairType[0] || 0,
						deviceTypeTwoName: this.formData.repairTypeName.split(' - ')[0] || '',
						deviceTypeThree: this.formData.repairType[1] || 0,
						deviceTypeThreeName: this.formData.repairTypeName.split(' - ')[1] || '',
						deviceTypeFour: 0,
						deviceTypeFourName: '',
						deviceCode: '',
						deviceName: this.formData.deviceName,
						deviceUseDeptId: this.formData.deviceUseDeptId,
						deviceUseDeptName: this.formData.deviceUseDeptName,
						deviceLocation: this.formData.detailAddress,
						faultDetail: this.formData.repairDetail,
						reportUserId: this.userInfo.userId,
						reportTell: this.formData.contactInfo,
						reportName: this.formData.repairPerson,
						repairTell: '',
						repairName: '',
						repairCostMoney: 0.0,
						repairImages: this.formData.repairImages,
						repairAreaOneId: this.formData.repairAreaOneId,
						repairAreaOneName: this.formData.repairAreaOneName,
						repairAreaTwoId: this.formData.repairAreaTwoId,
						repairAreaTwoName: this.formData.repairAreaTwoName
					}

					console.log('提交的数据：', submitData)
					const res = await addNewOther(submitData)
					
					if (res.code === 200) {
						uni.showToast({
							title: '提交成功',
							icon: 'success',
							duration: 2000
						})
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/repairList/myRepair'
							})
						}, 1000)
					} else {
						uni.showToast({
							title: res.msg || '提交失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('提交失败：', error)
					uni.showToast({
						title: '提交失败',
						icon: 'none'
					})
				}
			}).catch(err => {
				console.log('表单错误信息：', err)
			})
		},
		async getDepartmentTree() {
			try {
				const res = await getDeptTree()
				if (res.code === 200 && res.data) {
					// Transform the data to match uni-data-picker format
					const transformNode = (node) => {
						const transformed = {
							text: node.label,
							value: node.id
						}
						if (node.children && node.children.length > 0) {
							transformed.children = node.children.map(child => transformNode(child))
						}
						return transformed
					}
					this.deptTree = [transformNode(res.data[0])]
				}
			} catch (error) {
				console.error('获取部门树失败：', error)
				uni.showToast({
					title: '获取部门树失败',
					icon: 'none'
				})
			}
		},
		handleDeptChange(e) {
			console.log('部门选择变化：', e)
			const selectedValues = e.detail.value
			if (selectedValues && selectedValues.length > 0) {
				// Get the last selected department (most specific one)
				const lastSelected = selectedValues[selectedValues.length - 1]
				this.formData.deviceUseDeptId = lastSelected.value
				this.formData.deviceUseDeptName = lastSelected.text
			}
		}
	}
}
</script>

<style lang="scss">
.uni-forms-item__label {
	font-size: 30rpx;
	font-weight: bold;
}

.btn_big {
	margin: 40rpx 20rpx;
	padding: 20rpx;
	font-size: 32rpx;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #5b73f9;
	color: #fff;
	border-radius: 10rpx;
}

.example-body {
	padding: 20rpx;
}
</style>
