<template>
	<view class="work_container p20">
		<view class="grid grid-2 serice_list">
			<view class="service_item   b20  just-sbet fl" :style="[{'backgroundColor': item.bgcolor}]"
				v-for="(item,index) in serviceList" :key="index" @click="sericeBtn(item)">
				<view class="service_title w-full just-sbet as">
					<view class="name">
						{{item.name}}
					</view>
					<uni-icons color="#fff" custom-prefix="iconfont" type="icon-shenglvehao"></uni-icons>
				</view>
				<view class="serice_info w-full just-sbet as ">
					<uni-icons color="#fff" size="40" custom-prefix="iconfont" :type="item.icon"></uni-icons>
					<view class="enter">
						查看
						<uni-icons color="#fff" type="right"></uni-icons>
					</view>
				</view>

			</view>

		</view>
	</view>
	</view>
	
</template>

<script>
	import {
		show,
		topage
	} from '../../common/common_methods'
    import {
		listHrpRepairRecord
	} from '@/api/repair';
	export default {
		data() {
			return {
				hrpInfo: {
						pageNum: 1,
						pageSize: 10,
						cardNumber: null,
						cardName: null
					},
					
				
				serviceList: [
				
			{
					name: "设备报修",
					icon: "icon-home",
					link: "../HrpRepair/hrpRepair",
					bgcolor: "#e8482e",
					tips: "",
				
				},
				{
					name: "扫码报修",
					icon: "icon-task",
					link: "../qrRepair/index",
					bgcolor: "#4c93ed",
					tips: "",

				},{
					name: "其它报修",
					icon: "icon-hetong",
					link: "../other/index",
					bgcolor: "#f15b6c",
					tips: "",

				}, 
				// {
				// 	name: "在线签名",
				// 	icon: "icon-daifukuan",
				// 	link: "../signature/signature",
				// 	bgcolor: "#ffc529",
				// 	tips: "",

				// }, 
				// {
				// 	name: "收缴二维码",
				// 	icon: "icon-a-weibiaoti-1_huaban1fuben28",
				// 	link: "../qrCode/qrCode",
				// 	bgcolor: "#ff5f52",
				// 	tips: "",
				
				// }, {
				// 	name: "到期提醒",
				// 	icon: "icon-shuselishichaxun",
				// 	link: "../contract/contract",
					
				// 	bgcolor: "#a3cf62",
				// 	tips: "",
				
				// }, 
			]
			}
		},
		methods: {
			scan() {
				
			uni.scanCode({
				scanType: ['barCode', 'qrCode'],
				success: async (res) => {
					
					let arr = res.result.split(',')
					
					arr=arr[0].split(':')
					
					this.hrpInfo.cardNumber=arr[1]
					
				    this.getHrpRepairList()
				}
			})
			},
			hrpRepairReport(item){
				var objStr = JSON.stringify(item)
				uni.navigateTo({
					url: '/pages/assetDetails/assetDetails?repairInfo='+encodeURIComponent(objStr)
				})
			},
			async getHrpRepairList() {
				let res = await listHrpRepairRecord(this.hrpInfo);
				if(res ){
					console.log(res.rows)
					this.hrpRepairReport(res.rows[0])
				}
			
			},
			sericeBtn(item) {
				if(item.name=="扫码报修"){
					this.scan()
				}
				else{
					topage(item.link)
				}
				
				// show("功能还在开发中", 1)
			}
		}
	}
</script>

<style lang="scss">
	.work_container {
		.grid {
			.service_item {
				padding: 30rpx;
				height: 280rpx;
				color: #fff;

				.service_title {
					.name {
						font-size: 34rpx;
					}
				}

				.serice_info {
					align-items: flex-end;
					.enter {
						display: flex;
						justify-content: center;
					}
				}
			}

		}

		.grid-2 {}

		.serice_list {}
	}
</style>