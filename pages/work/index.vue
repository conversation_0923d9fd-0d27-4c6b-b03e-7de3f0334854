<template>
  <view class="">
    <!-- 轮播图start -->
    <swiper :autoplay="true" :interval="3000" :duration="1000" circular style="height: 360rpx;">
      <swiper-item>
        <image style="width: 100%;height:100%;" src="../../static/images/banner/nav1.png" mode="scaleToFill">
        </image>
      </swiper-item>
      <swiper-item>
        <image style="width: 100%;height:100%;" src="../../static/images/banner/nav2.jpg" mode="scaleToFill">
        </image>
      </swiper-item>

      <swiper-item>
        <image style="width: 100%;height:100%;" src="../../static/images/banner/nav3.png" mode="scaleToFill">
        </image>
      </swiper-item>
      <swiper-item>
        <image style="width: 100%;height:100%;" src="../../static/images/banner/nav4.png" mode="scaleToFill">
        </image>
      </swiper-item>
      <swiper-item>
        <image style="width: 100%;height:100%;" src="../../static/images/banner/nav5.jpg" mode="scaleToFill">
        </image>
      </swiper-item>
    </swiper>
    <!-- 轮播图 end -->


    <!-- 功能区 start -->
    <view class="padding-sm bg-white text-black">
      <view class="grid" style="grid-template-columns: repeat(4,1fr);gap:40rpx;">
        <block v-for="(item,index) in serviceList" :key="index">
          <view class=" flex flex-direction justify-center align-center" @click="sericeBtn(item)">
            <view class="workIcon " :class="item.icon"></view>
            <view class="text-md padding-top-sm">
              {{ item.name }}
            </view>
          </view>
        </block>
      </view>
    </view>
    <!-- 功能区 end -->
  </view>

</template>

<script>
import {
  show,
  topage
} from '../../common/common_methods'
import {
  listHrpRepairRecord
} from '@/api/repair';
import {
  workService
} from '@/mixins/workService.js'
import {mapGetters} from 'vuex'

export default {
  data() {
    return {
      hrpInfo: {
        pageNum: 1,
        pageSize: 10,
        cardNumber: null,
        cardName: null
      },
    }
  },
  mixins: [workService],
  created() {
    console.log("this.permissions:", this.permissions);//读取权限人名
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    scanDevice() {
      uni.scanCode({
        scanType: ['barCode', 'qrCode'],
        success: async (res) => {
          console.log('设备扫码', res)
          // #ifdef MP-DINGTALK
          // 钉钉小程序，直接走原有逻辑（不判断scanType）
          if (res.result) {
              if (res.result.includes(',') &&
                  res.result.includes(':')) {
                let arr = res.result.split(',')
                arr = arr[0].split(':')
                this.hrpInfo.cardNumber = arr[1]
              } else {
                this.hrpInfo.cardNumber = res.result;
              }
              await this.getHrpRepairList()
          }else {
              uni.showToast({
                icon: 'none',
                title: 'res.result为空',
                duration: 2000
              })
          }
        // #endif

          // #ifndef MP-DINGTALK
          // 非钉钉小程序，先判断scanType非空
          if (res.scanType && res.scanType === 'QR_CODE') {
              if (res.result.includes(',') && res.result.includes(':')) {
                let arr = res.result.split(',')
                arr = arr[0].split(':')
                this.hrpInfo.cardNumber = arr[1]
              } else {
                this.hrpInfo.cardNumber = res.result;
              }
              await this.getHrpRepairList()
          } else {
              uni.showToast({
                icon: 'none',
                title: '扫码失败，请重新扫码',
                duration: 2000
              })
          }
          // #endif
        }
      })
    },
    scanRepair() {
      uni.scanCode({
        scanType: ['barCode', 'qrCode'],
        success: async (res) => {
          console.log('扫码报修', res)

          // #ifdef MP-DINGTALK
          // 钉钉小程序，直接走原有逻辑（不判断scanType）
          if (res.result) {
            if (res.result.includes(',') && res.result.includes(':')) {
              let arr = res.result.split(',')
              arr = arr[0].split(':')
              this.hrpInfo.cardNumber = arr[1]
              console.log("this.hrpInfo.cardNumber", this.hrpInfo.cardNumber)
              await this.getDeviceInfoAndNavigate(this.hrpInfo.cardNumber)
            } else {
              const deviceCode = res.result;
              await this.getDeviceInfoAndNavigate(deviceCode)
            }
          }else {
              uni.showToast({
                icon: 'none',
                title: 'res.result为空',
                duration: 2000
              })
          }
          // #endif

          // #ifndef MP-DINGTALK
          // 非钉钉小程序，先判断scanType非空
          if (res.scanType && res.scanType === 'QR_CODE') {
            if (res.result.includes(',') && res.result.includes(':')) {
              let arr = res.result.split(',')
              arr = arr[0].split(':')
              this.hrpInfo.cardNumber = arr[1]
              console.log("this.hrpInfo.cardNumber", this.hrpInfo.cardNumber)
              await this.getDeviceInfoAndNavigate(this.hrpInfo.cardNumber)
            } else {
              const deviceCode = res.result;
              await this.getDeviceInfoAndNavigate(deviceCode)
            }
          } else {
            uni.showToast({
              icon: 'none',
              title: '扫码失败，请重新扫码',
              duration: 2000
            })
          }
          // #endif
        }
      })
    },
    // hrpRepairReport(item) {
    //   // console.log("item", item)
    //   var objStr = JSON.stringify(item)
    //   uni.navigateTo({
    //     url: '/pages/assetDetails/assetDetails?repairInfo=' + encodeURIComponent(objStr)
    //   })
    // },
    async getHrpRepairList() {
      // let res = await listHrpRepairRecord(this.hrpInfo);
      // if (res) {
      //   console.log(res.rows)
      //   this.hrpRepairReport(res.rows[0])
      // }
      uni.navigateTo({
        url: `/pages/dataService/dataService?cardNumber=${encodeURIComponent(this.hrpInfo.cardNumber)}`
      })
    },

    // 获取设备信息并跳转到报修页面
    async getDeviceInfoAndNavigate(cardNumber) {
      this.hrpInfo.cardNumber = cardNumber;
      this.hrpInfo.cardName = null;
      this.hrpInfo.pageNum = 1;
      this.hrpInfo.pageSize = 10;

      let res = await listHrpRepairRecord(this.hrpInfo);
      if (res && res.rows && res.rows.length > 0) {
        console.log("获取到设备信息:", res.rows[0]);
        // 直接跳转到报修页面，并传递设备信息
        var objStr = JSON.stringify(res.rows[0]);
        uni.navigateTo({
          url: `/pages/assetDetails/assetDetails?repairInfo=${encodeURIComponent(objStr)}`
        });
      } else {
        // 未找到设备信息，提示用户
        uni.showToast({
          icon: 'none',
          title: '未找到设备信息，请重新扫码',
          duration: 2000
        });
      }
    },
    sericeBtn(item) {
      if (item.perm) {
        console.log("item perm", item.perm)
        console.log("this.permissions", this.permissions)
        // admin 可以访问所有功能
        if (this.permissions.includes('*:*:*')) {

        } else if (!this.permissions.includes(item.perm)) {
          show("您没有权限", 1)
          return
        }
      }
      if (item.name == "扫码报修") {
        this.scanRepair()
      } else if (item.name == "设备扫码") {
        this.scanDevice()
      } else {
        topage(item.link)
      }

      // show("功能还在开发中", 1)
    }
  }
}
</script>

<style lang="scss">
@import url("@/static/workServerIcon/workServerIcon.css");

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.work_container {
  .grid {
    .service_item {
      padding: 30rpx;
      height: 280rpx;
      color: #fff;

      .service_title {
        .name {
          font-size: 34rpx;
        }
      }

      .serice_info {
        align-items: flex-end;

        .enter {
          display: flex;
          justify-content: center;
        }
      }
    }

  }

  .grid-2 {
  }

  .serice_list {
  }
}
</style>