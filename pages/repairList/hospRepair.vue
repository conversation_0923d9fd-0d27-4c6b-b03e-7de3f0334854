<template>
  <view class="container">
    <view class="nav bf">
      <view class="flex align-center p20">
        <uni-easyinput prefixIcon="search" @focus="flag=true" @blur="onSearchBlur" @clear="onClear" v-model="queryParams.recordTitle" placeholder="请输入报修标题">
        </uni-easyinput>
      </view>
      <view class="p20" v-if="flag && queryParams.recordTitle">
        <view class="btn_big" @click="search">
          搜索
        </view>
      </view>
    </view>

    <view class="asset_list p20">
      <view class="p20 bf card asset_info" v-for="item in repairList" :key="item.recordId">
        <view class="just-sbet">
          <view class="key">报修编号</view>
          <view class="">{{item.recordId}}</view>
        </view>
        <view class="just-sbet">
          <view class="key">报修标题</view>
          <view class="">{{item.recordTitle}}</view>
        </view>
        <view class="just-sbet">
          <view class="key">设备名称</view>
          <view class="">{{item.deviceName}}</view>
        </view>
        <view class="just-sbet">
          <view class="key">报修时间</view>
          <view class="">{{item.recordTime}}</view>
        </view>
        <view class="just-sbet">
          <view class="key">维修金额</view>
          <view class="">¥{{item.repairCostMoney}}</view>
        </view>
        <view class="just-sbet" v-if="item.flowTaskDto">
          <view class="key">流程状态</view>
          <view class="">
            {{item.flowTaskDto && !item.flowTaskDto.finishTime ? '进行中' : '已完成'}}
          </view>
        </view>
        <view class="just-sbet" v-if="item.flowTaskDto">
          <view class="key">当前节点</view>
          <view class="">{{item.flowTaskDto.taskName}}</view>
        </view>
        <view class="just-sbet" v-if="item.flowTaskDto">
          <view class="key">办理人</view>
          <view class="">{{item.flowTaskDto.assigneeName}}
            <text v-if="item.flowTaskDto.assigneeDeptName">:{{item.flowTaskDto.assigneeDeptName}}</text>
          </view>
        </view>
        <view class="just-sbet" v-if="item.flowTaskDto">
          <view class="key">处理耗时</view>
          <view class="">{{item.flowTaskDto.duration}}</view>
        </view>
        <view class="just-sbet">
          <view class="key"></view>
          <view class="btn-group">
            <!-- 添加导出按钮 -->
            <view 
              class="tips center export-btn" 
              v-if="item.flowTaskDto != null && 
                    item.flowTaskDto.finishTime != null && 
                    item.flowTaskDto.flowProcDefDto != null && 
                    item.flowTaskDto.flowProcDefDto.exportDoc > 0"
              @click="exportToWord(item)"
            >
              导出word<uni-icons type="download" color="#fff"></uni-icons>
            </view>
            <!-- 原有的详情按钮 -->
            <view class="tips center" @click="goToDetail" :data-index="item">
              详情<uni-icons type="forward" color="#fff"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 分页栏 -->
    <view class="pagination-container" v-if="total > 0">
      <view class="pagination">
        <view class="page-btn" :class="{ disabled: queryParams.pageNum <= 1 }" @click="handlePrevPage">
          上一页
        </view>
        <view class="page-info">
          第 {{queryParams.pageNum}} 页 / 共 {{totalPages}} 页
        </view>
        <view class="page-btn" :class="{ disabled: queryParams.pageNum >= totalPages }" @click="handleNextPage">
          下一页
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserProfile } from '@/api/system/user'
import { listDeptRepairRecord } from '@/api/portal/statistics'
import { exportExcel } from '@/utils/exportUtil'

export default {
  data() {
    return {
      repairList: [],
      loading: false,
      flag: false,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: -1,  // 固定为-1
        recordTitle: ''
      }
    }
  },
  onLoad() {
    this.getRepairList()  // 直接获取列表，不需要获取用户信息
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.queryParams.pageSize) || 1;
    }
  },
  methods: {
    async getRepairList() {
      this.loading = true
      try {
        const res = await listDeptRepairRecord(this.queryParams)
        if (res.rows) {
          this.repairList = res.rows
          this.total = res.total
        }
      } catch (error) {
        console.error('获取报修列表失败', error)
      } finally {
        this.loading = false
      }
    },
    handlePrevPage() {
      if (this.queryParams.pageNum > 1) {
        this.queryParams.pageNum -= 1;
        this.getRepairList();
        uni.pageScrollTo({ scrollTop: 0, duration: 300 });
      }
    },
    handleNextPage() {
      if (this.queryParams.pageNum < this.totalPages) {
        this.queryParams.pageNum += 1;
        this.getRepairList();
        uni.pageScrollTo({ scrollTop: 0, duration: 300 });
      }
    },
    search() {
      this.queryParams.pageNum = 1
      this.getRepairList()
      this.flag = false
    },
    onSearchBlur() {
      setTimeout(() => {
        if (!this.queryParams.recordTitle) {
          this.flag = false
        }
      }, 200)
    },
    onClear() {
      this.queryParams.pageNum = 1
      this.getRepairList()
      this.flag = false
    },
    goToDetail(item) {
      console.log(item)
      const row = item.currentTarget.dataset.index

      let params = {
        procInsId: row.flowTaskDto.procInsId,
        executionId: row.flowTaskDto.executionId,
        deployId: row.flowTaskDto.deployId,
        taskId: row.flowTaskDto.taskId,
        taskName: row.flowTaskDto.taskName,
        startUser: row.flowTaskDto.startUserName + "-" + row.flowTaskDto.startDeptName,
      }
      console.log(params, 'aaa');

      uni.navigateTo({
        url: `/pages/myTasks/taskWebView/myProcess?url=${JSON.stringify(params)}`
      })
    },
    // 添加导出Word方法
    async exportToWord(item) {
      try {
        const params = {recordId: item.recordId}
        await exportExcel({
          url: '/repair/record/repairRecord/appWordExport',
          params,
          header: {
            'content-type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'content-disposition': `attachment; filename=${encodeURIComponent('repair_apply_form.docx')}`
          },
          fileType: 'docx'
        })
      } catch (error) {
        console.error('导出失败:', error)
      } finally {
        uni.hideLoading()
      }
    }
  }
}
</script>

<style lang="scss">
.nav {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1024;
  box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
}

.asset_list {
  margin-top: 180rpx;
  display: grid;
  gap: 20rpx;

  .asset_info {
    display: grid;
    gap: 25rpx;

    .tips {
      color: #fff;
      background: #3296fa;
      padding: 5rpx 10rpx;
      border-radius: 20rpx;
    }

    .key {
      flex-basis: 150rpx;
      margin-right: 30rpx;
    }
  }
}

.just-sbet {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.p20 {
  padding: 20rpx;
}

.bf {
  background-color: #fff;
}

.card {
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.btn_big {
  background: #3296fa;
  color: #fff;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.pagination-container {
  padding: 20rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;

  .page-btn {
    background: #4095E5;
    color: #fff;
    padding: 10rpx 30rpx;
    border-radius: 30rpx;
    font-size: 28rpx;

    &.disabled {
      background: #ccc;
      opacity: 0.7;
    }
  }

  .page-info {
    font-size: 28rpx;
    color: #666;
  }
}

.container {
  padding-bottom: 120rpx;
}

.btn-group {
  display: flex;
  gap: 20rpx;
  
  .tips {
    &.export-btn {
      background: #67C23A;  // 使用绿色区分导出按钮
    }
  }
}
</style> 