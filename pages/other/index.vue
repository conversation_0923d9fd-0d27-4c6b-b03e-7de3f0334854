<template>
	<view class="nav bf">
			<uni-forms ref='form' :modelValue="formData">
				<uni-forms-item class="flex  align-center p20" label-width="80px" label="二级分类" 
				required :rules="[{required:true,errorMessage:'请输入二级分类'}]"  name="deviceTypeTwo">
				 <picker @change="bindPickerChange"  range-key="typeName" :value="index" :range="category2Options">
				 	<view class="uni-input">{{formData.deviceTypeTwo}}
				 		<uni-icons type="bottom" style="margin-left: 10rpx;"></uni-icons>
				 	</view>
				 </picker>
				 </uni-forms-item>
                 <uni-forms-item class="flex  align-center p20" label-width="80px" label="三级分类"
				  required :rules="[{required:true,errorMessage:'请输入三级分类'}]" name="deviceTypeThree">
                  <picker @change="bindPickerChange2"  range-key="typeName" :value="index2" :range="category3Options">
                  	<view class="uni-input">{{formData.deviceTypeThree}}
                  		<uni-icons type="bottom" style="margin-left: 10rpx;"></uni-icons>
                  	</view>
                  </picker>
				 
				</uni-forms-item>
				<uni-forms-item class="flex  align-center p20" required label-width="80px"
				:rules="[{required:true,errorMessage:'请输入报修名称'}]" label="报修名称" name="recordTitle">
					<uni-easyinput type="text" v-model="formData.recordTitle" placeholder="请输入报修名称" />
				</uni-forms-item>
				
				<uni-forms-item class="flex  align-center p20" label="详细地址" label-width="80px"
				required :rules="[{required:true,errorMessage:'请输入详细地址'}]"  name="deviceLocation">
					<uni-easyinput type="text" v-model="formData.deviceLocation" placeholder="请输入报修详细地址" />
				</uni-forms-item>
				<uni-forms-item class="flex  align-center p20" label="使用科室" label-width="80px"
				required :rules="[{required:true,errorMessage:'请输入使用科室'}]"  name="deviceUseDeptName">
					<uni-easyinput type="text" v-model="formData.deviceUseDeptName" placeholder="请输入使用科室" />
				</uni-forms-item>
				<uni-forms-item class="flex  align-center p20" label="设备名称" label-width="80px"
				required :rules="[{required:true,errorMessage:'请输入设备名称'}]"  name="deviceName">
					<uni-easyinput type="text" v-model="formData.deviceName" placeholder="设备名称" />
				</uni-forms-item>
				<uni-forms-item class="flex  align-center p20" label="故障描述" label-width="80px"
				 required :rules="[{required:true,errorMessage:'请输入故障描述'}]"  name="faultDetail">
					<uni-easyinput type="text" v-model="formData.faultDetail" placeholder="请输入故障描述" />
				</uni-forms-item>
			</uni-forms>
			<uni-section title="上传图片" type="line">
				<view class="example-body">
					<uni-file-picker v-model="imageValue" limit="5" title="最多选择5张图片" fileMediatype="image" mode="grid"
						@select="handleSelect" @success="handleSuccess" @delete="handleDelete"></uni-file-picker>
				</view>
			</uni-section>
			
			<button class="btn_big" @click="submitForm">确定</button>
		</view>
	
</template>

<script>
	import {
		assetOneInfo
	} from '@/api/assetInfo';
	import {
		uploadImage
	} from '@/api/system/user'
	import {
		addOtherRepairRecord,getCategory2Options,getDeptNameAndAddress
	} from '@/api/repair';
	import {
		getAssetNum
	} from '@/common/common_methods';
	export default {
		data() {
			return {
				
				index: 0,
				category2Options:[{typeName:''}],
				category3Options:[{typeName:''}],
				index2:0,
				selectedValue: '请选择',
				imageValue:null,
				formData: {
					deviceTypeTwo:null,
					deviceTypeThree:null,
					recordTitle:null,
					deviceName:null,
					cardNumber:null,
					deptName:null,
					deviceLocation:null,
					faultDetail:null,
					repairImages:[]
					
				},
				hrpInfo: {
					pageNum: 1,
					pageSize: 10,
					cardNumber: null,
					cardName: null
				}
				
			}
		},
		onLoad(option) {
			
			
			this.getCategory2Options()
			this.getDeptNameAndAddress()
      // 处理 inspectDetails
      if (option.inspectDetails) {
        // 对传递的 JSON 字符串进行解码
        var inspectDetailsStr = decodeURIComponent(option.inspectDetails);
        // 将 JSON 字符串转换回对象
        var inspectDetails = JSON.parse(inspectDetailsStr);

        // 在此根据 formData 数据更新相应的字段或执行其他操作
        console.log(inspectDetails);
        // 假设你想将 formData.deviceName 和 deviceType 赋值给 formData


        this.formData.deviceLocation=inspectDetails.inspectPlaceName;
        this.formData.deviceUseDeptName=inspectDetails.deptName;
        this.formData.deviceUseDeptId=inspectDetails.deptId;
        this.formData.deviceTypeOne=2 // 设备类型一
        this.formData.deviceTypeOneName='其他设备'

        this.formData.repairTell=''
        this.formData.repairName=''
        this.formData.repairCostMoney=0
        this.formData.reportName =inspectDetails.reportName;
        this.formData.reportTell = inspectDetails.reportTell; // 修改为 phonenumber

      }
		},
		
		methods: {
			
			async getDeptNameAndAddress(){
				let res= await getDeptNameAndAddress()
				
				this.formData.deptName=res.data.deptName
				this.formData.deviceLocation=res.data.deptAddress
				
			},
			async bindPickerChange(e){

				this.index =e.detail.value
				this.formData.deviceTypeTwo=this.category2Options[this.index].typeName
				let res =await getCategory2Options(e.detail.value+1)
	
				this.category3Options=res.data
				console.log(res)
			},
			bindPickerChange2(e){
			
				this.index2 =e.detail.value
				this.formData.deviceTypeThree=this.category3Options[this.index2].typeName
				this.formData.recordTitle=this.category3Options[this.index2].typeName+'报修'
			},
			async getCategory2Options(){
			        let res =await getCategory2Options(0)
				    
					this.category2Options=res.data
					
			    },
			async handleSelect(res) {
				// 选择图片
				this.uploadImg(res.tempFiles)
				
			
			},
			async handleSuccess(e) {
				
				console.log(e)
				
			
			},
			async uploadImg(tempFiles) {
				
				if (!tempFiles.length) return
				const path = tempFiles.pop()
			
				let data = {
					name: 'file',
					filePath: path.path
				}
				
				let res = await uploadImage(data)
				
				this.formData.repairImages.push(res.url)
				
				this.uploadImg(tempFiles)
				
				
			},
			 handleDelete(e) {
				// 图片删除
				
				let {
					url
				} = e.tempFile
	
				const num = this.formData.repairImages.findIndex((e) => e == url)
				this.formData.repairImages.splice(num, 1)
				
			},
			
			async submitForm(){
			this.$refs.form.validate().then(result=>{
				this.formData.deviceTypeTwo=this.category2Options[this.index].otherTypeId
				this.formData.deviceTypeThree=this.category3Options[this.index2].otherTypeId
				console.log(this.formData)
				let res =  addOtherRepairRecord(this.formData);
				 if(res ){
					  uni.switchTab({
						url:'/pages/work/index'
					  })
				}
			}).catch(err =>{
				console.log('表单错误信息：', err);
			})

			 
				
			},
			previewImage(attachment, index = 1) {
				// 预览图片
				attachment = JSON.parse(attachment).map((item) => {
					return item.url
				})
				uni.previewImage({
					urls: attachment,
					current: index
				})
				console.log(attachment, 'attachment');
			},
			async getAssetOneInfo(assetId) {
				let res = await assetOneInfo(assetId);
				this.propertyInfo = res
				if (this.propertyInfo == '') {
					uni.showToast({
						title: '未查到相关资产信息',
						icon: 'none'
					})
					this.propertyInfo = {}
					this.propertyIdInput = ''
					// this.flag = true
				}

			},
			getNum(id) {
				return getAssetNum(id)
			},
		}
	}
</script>

<style lang="scss">
	.uni-forms-item__label {
	   font-size: 30px; /* 设置字体大小 */
	   // color: red; /* 设置字体颜色 */
	   font-weight: bold; /* 设置字体粗细 */
	 }
	.picker {
	  padding: 10px;
	  background-color: #fff;
	  text-align: center;
	}
	
	.btn_big {
		padding: 20rpx;
		font-size: 50rpx;
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #5b73f9;
		color: #fff;
		border-radius: 10rpx;
	}
	.property_info {
		// margin-top: 135rpx;
		// display: grid;
		// gap: 45rpx;

		.customer_item {
			padding: 30rpx 0;

			.content {
				flex-basis: 130rpx;
				margin-right: 30rpx;
			}

			.key {
				flex: 1;
				text-align: right;

			}
		}


	}
</style>