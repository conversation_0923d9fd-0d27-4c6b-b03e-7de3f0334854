<template>
	<view class="container">
		<view class="card">
			<view class="filter-section">
				<view class="filter-row">
					<text>能耗类型：</text>
					<uni-data-select
						v-model="energyType"
						:localdata="energyTypeOptions"
						@change="onEnergyTypeChange"
						class="energy-select"
					/>
				</view>
				<view class="filter-row">
					<uni-datetime-picker
						type="daterange"
						v-model="dateRange"
						@change="fetchData"
						:clear-icon="false"
						class="date-picker"
					/>
					<view class="button-group">
						<button type="primary" size="mini" @click="setLastSevenDays">最近7天</button>
						<button type="primary" size="mini" @click="setCurrentMonth">本月</button>
					</view>
				</view>
			</view>
		</view>

		<view class="card">
			<view class="chart-container">
				<view v-if="!chartData.categories || chartData.categories.length === 0" class="no-data">
					暂无数据
				</view>
				<qiun-data-charts
					v-else
					type="column"
					:opts="chartOpts"
					:chartData="chartData"
					canvasId="energyChart"
				/>
			</view>
		</view>

		<view class="card">
			<view class="table-title">{{ energyTypeLabel }}能耗列表</view>
			<view class="table">
				<view class="table-header">
					<view class="th">统计时间</view>
					<view class="th">使用量</view>
					<view class="th">同比</view>
					<view class="th">环比</view>
					<view class="th">均摊量</view>
					<view class="th">总价</view>
				</view>
				<scroll-view scroll-y class="table-body" :style="{ height: tableHeight + 'px' }">
					<view v-for="(item, index) in paginatedTableData" :key="index" class="tr">
						<view class="td">{{ item.statisticsTime }}</view>
						<view class="td">{{ item.usage }}</view>
						<view class="td" :class="{'up': Number(item.onYear) > 0, 'down': Number(item.onYear) < 0}">
							{{ item.onYear }}%
						</view>
						<view class="td" :class="{'up': Number(item.chainRate) > 0, 'down': Number(item.chainRate) < 0}">
							{{ item.chainRate }}%
						</view>
						<view class="td">{{ item.shareUsage }}</view>
						<view class="td">¥{{ item.allAmt }}</view>
					</view>
				</scroll-view>
			</view>
			<view class="pagination">
				<uni-pagination
					:total="total"
					:pageSize="pageSize"
					:current="currentPage"
					@change="handleCurrentChange"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import { qryStatisticsByType } from '@/api/HRP/weg';
import { getDayPeakValue } from '@/api/portal/statistics';

export default {
	data() {
		return {
			energyTypeOptions: [
				{ value: 1, text: '用电' },
				{ value: 2, text: '用水' },
				{ value: 3, text: '用气' }
			],
			energyType: 1,
			deptId: -1,
			dateRange: [],
			chartData: {
				categories: [],
				series: []
			},
			tableData: [],
			currentPage: 1,
			pageSize: 10,
			total: 0,
			peakValue: {
				dayElectricityPeakValue: null,
				dayWaterPeakValue: null,
				dayGasPeakValue: null
			},
			chartOpts: {
				color: ['#409EFF', '#67C23A', '#E6A23C'],
				padding: [15, 15, 0, 15],
				legend: {
					show: true
				},
				xAxis: {
					disableGrid: true
				},
				yAxis: {
					gridType: 'dash',
					dashLength: 2
				},
				extra: {
					column: {
						width: 30
					}
				}
			},
			tableHeight: 400, // 表格高度
		}
	},
	computed: {
		energyTypeLabel() {
			const option = this.energyTypeOptions.find(item => item.value === this.energyType);
			return option ? option.text : '';
		},
		paginatedTableData() {
			const start = (this.currentPage - 1) * this.pageSize;
			const end = start + this.pageSize;
			return this.tableData.slice(start, end);
		}
	},
	onLoad() {
		this.setLastSevenDays();
		// 计算表格高度
		const systemInfo = uni.getSystemInfoSync();
		this.tableHeight = systemInfo.windowHeight * 0.5; // 设置为屏幕高度的50%
	},
	methods: {
		onEnergyTypeChange(value) {
			this.energyType = value;
			this.fetchData();
		},
		setLastSevenDays() {
			const end = new Date();
			const start = new Date();
			start.setDate(end.getDate() - 6);
			this.dateRange = [
				start.toISOString().split('T')[0],
				end.toISOString().split('T')[0]
			];
			this.fetchData();
		},
		setCurrentMonth() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			this.dateRange = [
				`${year}-${month}-01`,
				now.toISOString().split('T')[0]
			];
			this.fetchData();
		},
		handleCurrentChange(e) {
			this.currentPage = e.current;
		},
		async fetchData() {
			if (!this.dateRange || this.dateRange.length !== 2) return;
			const params = {
				timeType: 2,
				startTime: this.dateRange[0],
				endTime: this.dateRange[1],
				typeId: null,
				orgId: this.deptId,
				qryType: this.energyType
			};
			
			try {
				const [res, peakRes] = await Promise.all([
					qryStatisticsByType(params),
					getDayPeakValue()
				]);
				
				if (peakRes && peakRes.code === 200 && peakRes.data) {
					this.peakValue = peakRes.data;
				}
				
				if (res.code === 200 && res.data && res.data.length > 0) {
					const dayMap = new Map();
					res.data.forEach(item => {
						const calcTime = String(item.calcTime);
						const year = calcTime.substring(0, 4);
						const month = calcTime.substring(4, 6);
						const day = calcTime.substring(6, 8);
						const statisticsTime = `${year}-${month}-${day}`;
						
						if (!dayMap.has(statisticsTime)) {
							dayMap.set(statisticsTime, {
								statisticsTime,
								usage: item.usage.toFixed(4),
								onYear: item.onYear !== null ? (item.onYear * 100).toFixed(2) : '-',
								chainRate: item.chainRate !== null ? (item.chainRate * 100).toFixed(2) : '-',
								area: item.area.toFixed(4),
								shareUsage: item.shareUsage !== null ? item.shareUsage.toFixed(4) : '-',
								unitAmt: item.unitAmt !== null ? item.unitAmt.toFixed(2) : '-',
								allAmt: item.allAmt !== null ? item.allAmt.toFixed(2) : '-'
							});
						}
					});
					
					this.tableData = Array.from(dayMap.values()).sort((a, b) => 
						new Date(a.statisticsTime) - new Date(b.statisticsTime)
					);
					this.total = this.tableData.length;
					this.currentPage = 1;
					
					// 更新图表数据
					this.updateChartData();
				} else {
					// 清空数据
					this.tableData = [];
					this.total = 0;
					this.currentPage = 1;
					this.chartData = {
						categories: [],
						series: []
					};
				}
			} catch (error) {
				uni.showToast({
					title: '获取数据失败',
					icon: 'none'
				});
				// 清空数据
				this.tableData = [];
				this.total = 0;
				this.currentPage = 1;
				this.chartData = {
					categories: [],
					series: []
				};
			}
		},
		updateChartData() {
			const categories = this.tableData.map(item => item.statisticsTime);
			const usageData = this.tableData.map(item => Number(item.usage));
			const onYearData = this.tableData.map(item => Number(item.onYear));
			const chainRateData = this.tableData.map(item => Number(item.chainRate));
			
			// 获取当前能耗类型的峰值
			let peak = 0;
			if (this.energyType === 1 && this.peakValue.dayElectricityPeakValue != null) 
				peak = this.peakValue.dayElectricityPeakValue;
			if (this.energyType === 2 && this.peakValue.dayWaterPeakValue != null) 
				peak = this.peakValue.dayWaterPeakValue;
			if (this.energyType === 3 && this.peakValue.dayGasPeakValue != null) 
				peak = this.peakValue.dayGasPeakValue;
			
			// 为超出峰值的数据设置不同的颜色
			const usageDataWithColor = usageData.map(value => ({
				value: value,
				color: value > peak ? '#ff4d4f' : '#409EFF'  // 超出峰值为红色，否则为蓝色
			}));
			
			this.chartData = {
				categories: categories,
				series: [
					{
						name: '使用量',
						data: usageDataWithColor,
						type: 'column'
					},
					{
						name: '同比',
						data: onYearData,
						type: 'line'
					},
					{
						name: '环比',
						data: chainRateData,
						type: 'line'
					}
				]
			};
			
			// 更新图表配置
			this.chartOpts = {
				...this.chartOpts,
				title: {
					name: `${this.energyTypeLabel}能耗图表`,
					fontSize: 16,
					color: '#333'
				},
				markLine: {
					data: [{
						yAxis: peak,
						lineStyle: {
							color: '#faad14',
							type: 'dashed',
							width: 2
						},
						label: {
							formatter: `峰值: ${peak}`,
							position: 'end',
							color: '#faad14'
						}
					}]
				}
			};
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	
	.card {
		background-color: #fff;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 12rpx 0 rgba(0,0,0,0.1);
	}
	
	.filter-section {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		
		.filter-row {
			display: flex;
			align-items: center;
			gap: 20rpx;
			width: 100%;
			
			.energy-select {
				width: 200rpx;
				height: 60rpx;
				line-height: 60rpx;
			}
			
			.date-picker {
				flex: 1;
			}
			
			.button-group {
				display: flex;
				gap: 20rpx;
				
				button {
					margin: 0;
				}
			}
		}
	}
	
	.chart-container {
		width: 100%;
		height: 800rpx;
		position: relative;
		
		.no-data {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			color: #999;
			background: rgba(255,255,255,0.8);
			z-index: 2;
		}
	}
	
	.table-title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20rpx;
		color: #333;
	}
	
	.table {
		width: 100%;
		background: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		
		.table-header {
			display: flex;
			background-color: #f8f9fa;
			position: sticky;
			top: 0;
			z-index: 1;
			
			.th {
				flex: 1;
				padding: 24rpx 12rpx;
				text-align: center;
				font-weight: 500;
				font-size: 26rpx;
				color: #666;
				white-space: nowrap;
			}
		}
		
		.table-body {
			.tr {
				display: flex;
				border-bottom: 1px solid #f0f0f0;
				transition: background-color 0.3s;
				
				&:active {
					background-color: #f5f5f5;
				}
				
				.td {
					flex: 1;
					padding: 24rpx 12rpx;
					text-align: center;
					font-size: 26rpx;
					color: #333;
					white-space: nowrap;
					
					&.up {
						color: #f56c6c;
					}
					
					&.down {
						color: #67c23a;
					}
				}
			}
		}
	}
	
	.pagination {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		padding: 20rpx 0;
	}
}
</style>
