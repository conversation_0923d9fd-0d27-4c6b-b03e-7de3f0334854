<template>
  <view class="energy-consumption">
    <!-- 部门选择区域 -->
    <view class="dept-section">
      <view class="tree-select" @tap="toggleDeptSelect">
        <text class="selected-text">{{ selectedDeptName || '请选择部门' }}</text>
        <uni-icons type="bottom" size="16" :color="showDeptSelect ? '#2A5EFF' : '#666'"></uni-icons>
      </view>

    <!-- 下拉树状选择框 -->
      <view class="tree-dropdown" v-if="showDeptSelect">
        <scroll-view scroll-y class="tree-scroll">
          <tree-item
              v-for="dept in deptTreeList"
              :key="dept.id"
              :node="dept"
              @clickNode="onDeptSelect"
          />
        </scroll-view>
      </view>
    </view>

    <!-- 类型选择器 -->
    <view class="type-selector">
      <uni-segmented-control 
        :current="type - 2" 
        :values="['日', '月', '年']"
        @clickItem="handleSegmentChange"
        styleType="button"
        activeColor="#2A5EFF"
      />
    </view>

    <!-- 日期选择 -->
    <view class="form-item">
      <text class="label">日期：</text>
      
      <!-- 时间选择器容器 -->
      <view class="picker-container">
        <day-picker
            v-if="type === 2"
            v-model="time"
            :single-mode="true"
            :disableFutureDates="true"
        />
        <month-picker
            v-else-if="type === 3"
            v-model="time"
            :single-mode="true"
            :disableFutureDates="true"
        />
        <year-picker
            v-else-if="type === 4"
            v-model="time"
            :single-mode="true"
            :disableFutureDates="true"
        />
      </view>
      
      <!-- 按钮组 -->
      <view class="button-group">
        <!-- 查询按钮 -->
        <view class="query-button" :class="{ 'loading': isLoading }" @click="getStart">
          <uni-icons v-if="!isLoading" type="search" size="16" color="#fff"></uni-icons>
          <uni-icons v-else type="spinner-cycle" size="16" color="#fff" class="loading-icon"></uni-icons>
          <text class="query-text">{{ isLoading ? '查询中...' : '查询' }}</text>
        </view>

        <!-- 导出按钮 -->
        <view class="export-button" @click="handleExport">
          <uni-icons type="download" size="16" color="#fff"></uni-icons>
          <text class="export-text">导出</text>
        </view>
      </view>
    </view>

    <view class="asset_list p20">
      <view class="p20 bf card asset_info" :data="repairList" >
        <view>
          <view class="section-title">设备费用</view>

          <view class="just-sbet">
            <view class="key">设备名称</view>
            <view class="">{{ deviceName }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">一级分类</view>
            <view class="">{{ typeName }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">二级分类</view>
            <view class="">{{ deviceTypeTwoName }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">三级分类</view>
            <view class="">{{ deviceTypeThreeName }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">四级分类</view>
            <view class="">{{ deviceTypeFourName }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">维修费用</view>
            <view class="">{{ subcost }}</view>
          </view>

        </view>
      </view>
    </view>

    <view class="asset_list p20">
      <view class="p20 bf card asset_info" :data="electricList">
        <view>
          <view class="section-title">用电费用</view>

          <view class="just-sbet">
            <view class="key">用途</view>
            <view class="">{{ electricTypeNum }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">统计时间</view>
            <view class="">{{ electricCalcTime }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">使用量</view>
            <view class="">{{ electricUsage }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">面积</view>
            <view class="">{{ electricArea }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">均摊量</view>
            <view class="">{{ electricShareUsage }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">单价</view>
            <view class="">{{ electricUnitAmt }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">费用</view>
            <view class="">{{ electricAllAmt }}</view>
          </view>

        </view>
      </view>
    </view>

    <view class="asset_list p20">
      <view class="p20 bf card asset_info" :data="waterList">
        <view>
          <view class="section-title">用水费用</view>

          <view class="just-sbet">
            <view class="key">用途</view>
            <view class="">{{ waterTypeNum }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">统计时间</view>
            <view class="">{{ waterCalcTime }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">使用量</view>
            <view class="">{{ waterUsage }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">面积</view>
            <view class="">{{ waterArea }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">均摊量</view>
            <view class="">{{ waterShareUsage }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">单价</view>
            <view class="">{{ waterUnitAmt }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">费用</view>
            <view class="">{{ waterAllAmt }}</view>
          </view>

        </view>
      </view>
    </view>

    <view class="asset_list p20">
      <view class="p20 bf card asset_info">
        <view>
          <view class="section-title">用气费用</view>

          <view class="just-sbet">
            <view class="key">用途</view>
            <view class="">{{ gasTypeNum }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">统计时间</view>
            <view class="">{{ gasCalcTime }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">使用量</view>
            <view class="">{{ gasUsage }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">面积</view>
            <view class="">{{ gasArea }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">均摊量</view>
            <view class="">{{ gasShareUsage }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">单价</view>
            <view class="">{{ gasUnitAmt }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">费用</view>
            <view class="">{{ gasAllAmt }}</view>
          </view>

        </view>
      </view>
    </view>

    <!-- HIS数据 -->
    <view class="asset_list p20">
      <view class="p20 bf card asset_info">
        <view>
          <view class="section-title">HIS数据</view>

          <view class="just-sbet">
            <view class="key">入院人数</view>
            <view class="">{{ hisAdmissionCount }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">出院人数</view>
            <view class="">{{ hisDischargeCount }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">收入</view>
            <view class="">{{ hisTotalIncome }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">支出</view>
            <view class="">{{ hisTotalExpense }}</view>
          </view>
          <view class="just-sbet">
            <view class="key">绩效</view>
            <view class="">{{ hisPerformance }}</view>
          </view>

        </view>
      </view>
    </view>
    <!-- 总收入支出绩效统计 -->
    <view class="performance-summary">
      <view class="performance-item">
        <view class="performance-label">总收入</view>
        <view class="performance-value income">{{ totalIncome }}</view>
      </view>
      <view class="performance-operator">-</view>
      <view class="performance-item">
        <view class="performance-label">总支出</view>
        <view class="performance-value expense">{{ totalCost }}</view>
      </view>
      <view class="performance-operator">=</view>
      <view class="performance-item result">
        <view class="performance-label">总绩效</view>
        <view class="performance-value performance">{{ totalPerformance }}</view>
      </view>
    </view>
  </view> <!-- 此行为新增的闭合标签 -->
</template>

<script>
import TreeItem from '@/components/tree-item.vue'
import {getDeptTree} from '@/api/commservice/comSysDept'
import {getDeptHospCostStat4Month, getDeptHospCostStat4Day, getDeptHospCostStat4Year, appExportCostStat4Day, appExportCostStat4Month, appExportCostStat4Year} from "../../api/portal/statistics";
import UniSegmentedControl from "../../uni_modules/uni-segmented-control/components/uni-segmented-control/uni-segmented-control.vue";
import DayPicker from "../../components/MyFormComponents/time-picker/DayPicker.vue";
import MonthPicker from "../../components/MyFormComponents/time-picker/MonthPicker.vue";
import YearPicker from "../../components/MyFormComponents/time-picker/YearPicker.vue";
import { exportExcel } from '@/utils/exportUtil'

import UniIcons from "../../uni_modules/uni-icons/components/uni-icons/uni-icons.vue";

export default {
  components: {
    UniSegmentedControl,
    UniIcons,
    TreeItem,
    'day-picker': DayPicker,
    'month-picker': MonthPicker,
    'year-picker': YearPicker
  },
  data() {
    return {
      // 设备用途map
      deviceMap: {},
      // 查询参数
      queryParams: {
        deptId: 0, // 科室id
        startTime: '',
        endTime: '',
        timeType: 2 // 确保默认值为2
      },
      // 日期选择
      type: 2,  // 将初始值设置为2，这样 type-2=0 就会选中"日"
      time: [],  // 改为数组，匹配组件期望的类型
      placeholder: '请选择日期',
      // 加载状态
      isLoading: false,
      // 表格数据
      repairList: [],
      repairTotal: 0,
      electricList: [],
      electricTotal: 0,
      waterList: [],
      waterTotal: 0,
      gasList: [],
      gasTotal: 0,
      
      // 总计数据
      totalIncome: 0,
      totalCost: 0,
      totalPerformance: 0,
      //部门树相关
      deptId: 0, // 默认部门ID
      deptTreeList: [], // 部门树数据
      selectedDeptName: '', // 选中的部门名称
      showDeptSelect: false, // 是否显示部门选择框

      //repairList里面的参数
      deviceName: '',
      typeName: '',
      deviceTypeTwoName: '',
      deviceTypeThreeName: '',
      deviceTypeFourName: '',
      subcost: null,

      //electricList参数
      electricTypeNum: '',
      electricCalcTime: null,
      electricUsage: null,
      electricArea: null,
      electricShareUsage: null,
      electricUnitAmt: null,
      electricAllAmt: null,

      //waterList参数
      waterTypeNum: '',
      waterCalcTime: null,
      waterUsage: null,
      waterArea: null,
      waterShareUsage: null,
      waterUnitAmt: null,
      waterAllAmt: null,

      //gasList参数
      gasTypeNum: '',
      gasCalcTime: null,
      gasUsage: null,
      gasArea: null,
      gasShareUsage: null,
      gasUnitAmt: null,
      gasAllAmt: null,

      //HIS数据参数
      hisAdmissionCount: null,
      hisDischargeCount: null,
      hisTotalIncome: null,
      hisTotalExpense: null,
      hisPerformance: null,
    }
  },

  watch: {
    deptId: {
      handler(newVal) {
        console.log('当前选中的部门ID:', newVal)
      },
      immediate: true
    },
    type: {
      handler(newVal) {
        if (newVal === 2) this.placeholder = '请选择日期';
        else if (newVal === 3) this.placeholder = '请选择月份';
        else if (newVal === 4) this.placeholder = '请选择年份';
        this.time = [];  // 改为空数组
      }
    }
  },
  onLoad() {
    // if (options.deptId) {
    //   this.deptId = Number(options.deptId)
    // }
    // 获取部门树数据
    this.loadDepartmentTree()
    //获取数据
    //this.getStart()
  },
  methods: {
    // 加载部门树
    async loadDepartmentTree() {
      try {
        const res = await getDeptTree()
        if (res.code === 200 && res.data) {
          this.deptTreeList = this.processDeptData(res.data)
          console.log('部门树数据:', this.deptTreeList)
        } else {
          console.error('获取部门树数据失败:', res)
          uni.showToast({
            title: '获取部门树数据失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载部门树失败:', error)
        uni.showToast({
          title: '加载部门树失败',
          icon: 'none'
        })
      }
    },

    // 处理部门数据
    processDeptData(depts) {
      if (!depts) return []
      return depts.map(dept => ({
        id: dept.id,
        label: dept.label,
        children: dept.children ? this.processDeptData(dept.children) : [],
        isOpen: false
      }))
    },

    // 切换部门选择框的显示状态
    toggleDeptSelect() {
      this.showDeptSelect = !this.showDeptSelect
    },

    // 处理部门选择
    onDeptSelect(node) {
      console.log('选择部门:', node)
      this.deptId = node.id
      this.selectedDeptName = node.label
      this.showDeptSelect = false
    },


    handleSegmentChange(e) {
      const index = e.currentIndex;
      // 确保type值有效，并且同步到queryParams
      this.type = index + 2;
      this.queryParams.timeType = this.type;
      this.time = [];  // 改为空数组
    },
    // 验证查询条件
    validateQuery() {
      if (this.deptId === 0) {
        uni.showToast({
          title: '请选择部门',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.time || !this.time.length || !this.time[0]) {
        uni.showToast({
          title: '请选择日期',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },

    // 格式化查询时间
    formatQueryTime() {
      // 获取数组中的第一个日期值
      const timeValue = this.time && this.time.length > 0 ? this.time[0] : '';
      let fixedTime = timeValue;
      
      try {
        if (this.type === 3 && timeValue) {
          // 月查询：确保格式为 yyyy-MM-01
          const [year, month] = timeValue.split('-');
          if (year && month) {
            fixedTime = `${year}-${month}-01`;
          }
        } else if (this.type === 4 && timeValue) {
          // 年查询：确保格式为 yyyy-01-01
          fixedTime = `${timeValue}-01-01`;
        }
        
        return fixedTime;
      } catch (error) {
        console.error('日期格式处理错误:', error);
        uni.showToast({
          title: '日期格式错误，请重新选择',
          icon: 'none'
        });
        return null;
      }
    },

    // 处理通用数据格式
    processUtilityData(dataMap, type) {
      const keys = Object.keys(dataMap || {});
      if (keys.length === 0) return [];

      return keys.map((key, index) => {
        const item = dataMap[key];
        
        // 设置第一条数据到组件属性
        if (index === 0) {
          this.setUtilityProperties(type, key, item);
        }
        
        return {
          [`${type}TypeNum`]: this.deviceMap[key]?.typeNum || key,
          [`${type}CalcTime`]: item.calcTime,
          [`${type}Usage`]: item.usage,
          [`${type}Area`]: item.area,
          [`${type}ShareUsage`]: item.shareUsage,
          [`${type}UnitAmt`]: item.unitAmt,
          [`${type}AllAmt`]: item.allAmt
        };
      });
    },

    // 设置公用事业属性
    setUtilityProperties(type, key, item) {
      const typeMap = {
        electric: () => {
          this.electricTypeNum = this.deviceMap[key]?.typeNum || key;
          this.electricCalcTime = item.calcTime;
          this.electricUsage = item.usage;
          this.electricArea = item.area;
          this.electricShareUsage = item.shareUsage;
          this.electricUnitAmt = item.unitAmt;
          this.electricAllAmt = item.allAmt;
        },
        water: () => {
          this.waterTypeNum = this.deviceMap[key]?.typeNum || key;
          this.waterCalcTime = item.calcTime;
          this.waterUsage = item.usage;
          this.waterArea = item.area;
          this.waterShareUsage = item.shareUsage;
          this.waterUnitAmt = item.unitAmt;
          this.waterAllAmt = item.allAmt;
        },
        gas: () => {
          this.gasTypeNum = this.deviceMap[key]?.typeNum || key;
          this.gasCalcTime = item.calcTime;
          this.gasUsage = item.usage;
          this.gasArea = item.area;
          this.gasShareUsage = item.shareUsage;
          this.gasUnitAmt = item.unitAmt;
          this.gasAllAmt = item.allAmt;
        }
      };
      
      typeMap[type]?.();
    },

    // 处理查询响应数据
    processQueryResponse(data, timeType) {
      const typeMap = {
        2: 'Day',
        3: 'Month', 
        4: 'Year'
      };
      
      const suffix = typeMap[timeType];
      
      // 处理维修数据
      this.repairList = data.list4GetDeptHospCostStat4RepairVo || [];
      this.repairTotal = data.getDeptHospCostStat4RepairVo4Repair?.subcost || 0;
      
      // 处理电力数据
      this.electricList = this.processUtilityData(
        data[`purposeElectric2DahuaWeg${suffix}CalcMap`], 
        'electric'
      );
      this.electricTotal = data[`dahuaWeg${suffix}Calc4CollectElectric`]?.electricAllAmt || 0;
      
      // 处理水数据
      this.waterList = this.processUtilityData(
        data[`purposeWater2DahuaWeg${suffix}CalcMap`], 
        'water'
      );
      this.waterTotal = data[`dahuaWeg${suffix}Calc4CollectWater`]?.waterAllAmt || 0;
      
      // 处理气数据
      this.gasList = this.processUtilityData(
        data[`purposeGas2DahuaWeg${suffix}CalcMap`], 
        'gas'
      );
      this.gasTotal = data[`dahuaWeg${suffix}Calc4CollectGas`]?.gasAllAmt || 0;
      
      // 处理总计数据
      const totalData = data.getDeptHospCostStat4TotalVo || {};
      this.totalIncome = totalData.totalIncome || 0;
      this.totalCost = totalData.totalCost || 0;
      this.totalPerformance = totalData.performance || 0;
      
      // 处理HIS数据
      const hisData = data.hisVZhhqVO || {};
      this.hisAdmissionCount = hisData.admissionCount || 0;
      this.hisDischargeCount = hisData.dischargeCount || 0;
      this.hisTotalIncome = hisData.totalIncome || 0;
      this.hisTotalExpense = hisData.totalExpense || 0;
      this.hisPerformance = hisData.performance || 0;
    },

    // 清空数据
    clearData() {
      this.repairList = [];
      this.repairTotal = 0;
      this.electricList = [];
      this.electricTotal = 0;
      this.waterList = [];
      this.waterTotal = 0;
      this.gasList = [];
      this.gasTotal = 0;
      
      // 清空总计数据
      this.totalIncome = 0;
      this.totalCost = 0;
      this.totalPerformance = 0;
      
      // 清空HIS数据
      this.hisAdmissionCount = null;
      this.hisDischargeCount = null;
      this.hisTotalIncome = null;
      this.hisTotalExpense = null;
      this.hisPerformance = null;
    },

    // 主查询方法
    async getStart() {
      if (!this.validateQuery()) return;

      // 防止重复点击
      if (this.isLoading) return;

      const fixedTime = this.formatQueryTime();
      if (!fixedTime) return;

      // 设置查询参数
      this.queryParams = {
        deptId: this.deptId,
        startTime: fixedTime,
        endTime: fixedTime,
        timeType: this.type || 2
      };

      console.log("准备查询，参数：", JSON.stringify(this.queryParams));

      // 清空数据
      this.clearData();

      // API映射
      const apiMap = {
        2: getDeptHospCostStat4Day,
        3: getDeptHospCostStat4Month,
        4: getDeptHospCostStat4Year
      };

      const typeNames = { 2: '日', 3: '月', 4: '年' };

      try {
        // 设置加载状态
        this.isLoading = true;

        // 显示加载提示
        uni.showLoading({
          title: `正在查询${typeNames[this.type]}费用数据...`,
          mask: true // 添加遮罩层，防止用户重复点击
        });

        const res = await apiMap[this.type](this.queryParams);
        console.log(`${typeNames[this.type]}费用查询结果:`, res);

        if (res.code === 200) {
          this.processQueryResponse(res.data || {}, this.type);
          // 查询成功提示
          uni.showToast({
            title: '查询成功',
            icon: 'success',
            duration: 1500
          });
        } else {
          uni.showToast({
            title: res.msg || '查询失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error(`查询${typeNames[this.type]}费用失败:`, error);
        uni.showToast({
          title: `查询${typeNames[this.type]}费用失败，请稍后重试`,
          icon: 'none'
        });
      } finally {
        // 确保隐藏加载提示和重置加载状态
        this.isLoading = false;
        uni.hideLoading();
      }
    },


    getRepairSummary(param) {
      const {columns} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总报修费用';
        } else if (column.property === 'subcost') {
          sums[index] = this.repairTotal;
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    getElectricSummary(param) {
      const {columns} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总用电费用';
        } else if (column.property === 'allAmt') {
          sums[index] = this.electricTotal;
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    getWaterSummary(param) {
      const {columns} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总用水费用';
        } else if (column.property === 'allAmt') {
          sums[index] = this.waterTotal;
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    getGasSummary(param) {
      const {columns} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总用气费用';
        } else if (column.property === 'allAmt') {
          sums[index] = this.gasTotal;
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },

    // 导出方法
    async handleExport() {
      if (!this.validateQuery()) return;

      // API映射
      const exportApiMap = {
        2: '/portal/statistics/ads/depthosp/appExport/deptHospCostStat4Day',
        3: '/portal/statistics/ads/depthosp/appExport/deptHospCostStat4Month',
        4: '/portal/statistics/ads/depthosp/appExport/deptHospCostStat4Year'
      };

      const typeNames = { 2: '日', 3: '月', 4: '年' };

      try {
        uni.showLoading({
          title: '正在导出...'
        });

        const fixedTime = this.formatQueryTime();
        if (!fixedTime) {
          uni.hideLoading();
          return;
        }

        // 构建导出参数
        const exportParams = {
          deptId: this.deptId,
          startTime: fixedTime,
          endTime: fixedTime,
          timeType: this.type || 2
        };

        await exportExcel({
          url: exportApiMap[this.type],
          params: exportParams
        });

        uni.hideLoading();
      } catch (error) {
        console.error(`导出${typeNames[this.type]}费用失败:`, error);
        uni.hideLoading();
        uni.showToast({
          title: `导出${typeNames[this.type]}费用失败，请稍后重试`,
          icon: 'none'
        });
      }
    },


  }
}
</script>

<style lang="scss">
.energy-consumption {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}



.dept-section {
  position: relative;
  width: 100%;
  z-index: 999;
  margin-bottom: 20rpx;

  .tree-select {
    width: 100%;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx;
    background: #fff;
    border: 2rpx solid #f0f0f0;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:active {
      border-color: #2A5EFF;
      box-shadow: 0 4rpx 16rpx rgba(42, 94, 255, 0.15);
    }

    .selected-text {
      font-size: 30rpx;
      color: #333;
      font-weight: 400;
    }
  }

  .tree-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #fff;
    border: 2rpx solid #f0f0f0;
    border-radius: 12rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    z-index: 1000;
    margin-top: 8rpx;

    .tree-scroll {
      max-height: 400rpx;
      padding: 20rpx;
    }
  }
}

.performance-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 32rpx;
  margin-bottom: 20rpx;
  padding: 32rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  gap: 24rpx;
  flex-wrap: wrap;

  .performance-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    min-width: 160rpx;

    &.result {
      .performance-value {
        background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
        border: 2rpx solid rgba(82, 196, 26, 0.2);
        box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.15);
      }
    }

    .performance-label {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
    }

    .performance-value {
      font-size: 32rpx;
      font-weight: bold;
      padding: 12rpx 20rpx;
      border-radius: 8rpx;
      background: #f8f9fa;
      border: 1rpx solid #e9ecef;
      min-width: 120rpx;
      text-align: center;

      &.income {
        color: #1890ff;
        background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
        border-color: rgba(24, 144, 255, 0.2);
      }

      &.expense {
        color: #ff4d4f;
        background: linear-gradient(135deg, #fff2f0 0%, #fff1f0 100%);
        border-color: rgba(255, 77, 79, 0.2);
      }

      &.performance {
        color: #52c41a;
        font-size: 36rpx;
      }
    }
  }

  .performance-operator {
    font-size: 40rpx;
    font-weight: bold;
    color: #666;
    margin: 0 8rpx;
  }
}


.asset_list {
  margin-top: 20rpx;
  display: grid;
  gap: 24rpx;

  .asset_info {
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    }

    .tips {
      color: #fff;
      background: linear-gradient(135deg, #3296fa 0%, #2A5EFF 100%);
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
    }

    .just-sbet {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }
    }

    .key {
      flex-basis: 180rpx;
      margin-right: 30rpx;
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
    }
  }

  .section-title {
    text-align: center;
    position: relative;
    padding: 24rpx 0 20rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);

    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 120rpx;
      height: 4rpx;
      background: linear-gradient(135deg, #2A5EFF 0%, #3296fa 100%);
      border-radius: 2rpx;
    }
  }
}

  .type-selector {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    margin-bottom: 20rpx;
  }

  .radio-group {
    display: flex;
    gap: 20rpx;
    margin-right: 20rpx;
  }

  .radio-label {
    display: flex;
    align-items: center;
    gap: 8rpx;
    font-size: 28rpx;
    color: #333;
  }
  .form-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    gap: 20rpx;

    .label {
      font-size: 30rpx;
      color: #333;
      font-weight: 500;
      white-space: nowrap;
    }

    .picker-container {
      flex: 1;
      min-width: 0; // 防止flex子项溢出
      max-width: 400rpx; // 压缩时间选择器宽度
    }

    .button-group {
      display: flex;
      gap: 16rpx;
      flex-shrink: 0;
    }

    .query-button, .export-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      padding: 16rpx 24rpx;
      border-radius: 50rpx;
      box-shadow: 0 4rpx 12rpx rgba(42, 94, 255, 0.3);
      transition: all 0.3s ease;
      cursor: pointer;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(42, 94, 255, 0.4);
      }
    }

    .query-button {
      background: linear-gradient(135deg, #2A5EFF 0%, #1E4AE6 100%);

      &.loading {
        opacity: 0.8;
        pointer-events: none; // 禁用点击
      }

      .query-text {
        font-size: 28rpx;
        color: #fff;
        font-weight: 500;
      }

      .loading-icon {
        animation: spin 1s linear infinite;
      }
    }

    .export-button {
      background: linear-gradient(135deg, #52C41A 0%, #389E0D 100%);
      box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);

      &:active {
        box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);
      }

      .export-text {
        font-size: 28rpx;
        color: #fff;
        font-weight: 500;
      }
    }
  }

  // 旋转动画
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
