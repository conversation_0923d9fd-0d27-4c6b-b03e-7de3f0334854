<template>
  <view class="container">
    <view class="card">
      <view class="filter-section">
        <view class="filter-row">
          <text>统计周期：</text>
          <view class="segmented-control">
            <view
                v-for="(item, index) in timePeriodOptions"
                :key="index"
                :class="['segment', { active: timeType === item.value }]"
                @tap="handleTimeTypeChange(item.value)"
            >
              {{ item.text }}
            </view>
          </view>
        </view>
        <view class="filter-row date-row">
          <view class="date-picker-container">
            <uni-datetime-picker
                v-if="timeType === 1"
                :type="datePickerType"
                v-model="dateRange"
                @change="fetchData"
                :clear-icon="false"
                :format="dateFormat"
                :value-format="valueFormat"
                :start-placeholder="startPlaceholder"
                :end-placeholder="endPlaceholder"
                class="date-picker"
            />
          </view>
          <view v-if="timeType === 2" class="month-picker-wrapper">
            <month-picker 
              v-model="selectedMonth" 
              :max-date="maxMonth" 
              :single-mode="false"
              @change="onMonthChange"
            />
          </view>
          <view v-if="timeType === 3" class="year-picker-wrapper">
            <year-picker 
              v-model="selectedYear" 
              :max-year="maxYear" 
              :single-mode="false"
              @change="onYearChange"
            />
          </view>
          <view class="button-group">
            <template v-if="timeType === 1">
              <button type="primary" size="mini" @click="setLastSevenDays">最近7天</button>
              <button type="primary" size="mini" @click="setCurrentMonthRange">本月</button>
            </template>
            <template v-else-if="timeType === 2">
              <button type="primary" size="mini" @click="setLastThreeMonths">前三月</button>
              <button type="primary" size="mini" @click="setLastSixMonths">前六月</button>
            </template>
            <template v-else-if="timeType === 3">
              <button type="primary" size="mini" @click="setLastYear">前一年</button>
              <button type="primary" size="mini" @click="setLastThreeYears">前三年</button>
            </template>
          </view>
        </view>
      </view>
    </view>

    <view class="card">
      <view class="chart-container">
        <view v-if="!chartData.categories || chartData.categories.length === 0" class="no-data">
          暂无数据
        </view>
        <qiun-data-charts
            v-else
            type="column"
            :opts="chartOpts"
            :chartData="chartData"
            canvasId="energyChart"
        />
      </view>
    </view>

    <view class="card">
      <view class="table-title">公共区域用电均摊用量列表</view>
      <view class="table">
        <view class="table-header">
          <view class="th">时间</view>
          <view class="th">使用量</view>
          <view class="th">同比</view>
          <view class="th">环比</view>
          <view class="th">均摊量</view>
          <view class="th">单价</view>
          <view class="th">总价</view>
        </view>
        <scroll-view scroll-y class="table-body" :style="{ height: tableHeight + 'px' }">
          <view v-for="(item, index) in paginatedTableData" :key="index" class="tr">
            <view class="td">{{ item.statisticsTime }}</view>
            <view class="td">{{ item.usage }}</view>
            <view class="td" :class="{'up': Number(item.onYear) > 0, 'down': Number(item.onYear) < 0}">
              {{ item.onYear }}%
            </view>
            <view class="td" :class="{'up': Number(item.chainRate) > 0, 'down': Number(item.chainRate) < 0}">
              {{ item.chainRate }}%
            </view>
            <view class="td">{{ item.shareUsage }}</view>
            <view class="td">¥{{ item.unitAmt }}</view>
            <view class="td">¥{{ item.allAmt }}</view>
          </view>
        </scroll-view>
      </view>
      <view class="pagination">
        <uni-pagination
            :total="total"
            :pageSize="pageSize"
            :current="currentPage"
            @change="handleCurrentChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
import { qryStatisticsByType } from '@/api/HRP/weg';
import { getPerSquarePubEnergyPeakValue } from '@/api/portal/statistics';
import MonthPicker from '@/components/MyFormComponents/time-picker/MonthPicker.vue'
import YearPicker from '@/components/MyFormComponents/time-picker/YearPicker.vue'

export default {
  components: {
    MonthPicker,
    YearPicker
  },
  data() {
    return {
      energyTypeOptions: [
        { value: 1, text: '用电' },
        { value: 2, text: '用水' },
        { value: 3, text: '用气' }
      ],
      energyType: 1,
      deptId: -1,
      dateRange: [],
      chartData: {
        categories: [],
        series: []
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      peakValue: {
        dayElectricityPeakValue: null,
        dayWaterPeakValue: null,
        dayGasPeakValue: null
      },
      chartOpts: {
        color: ['#409EFF', '#67C23A', '#E6A23C'],
        padding: [15, 15, 0, 15],
        legend: {
          show: true
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2
        },
        extra: {
          column: {
            width: 30
          }
        }
      },
      tableHeight: 400, // 表格高度
      timeType: 1,
      selectedMonth: [],
      selectedYear: [],
      timePeriodOptions: [
        { value: 1, text: '日' },
        { value: 2, text: '月' },
        { value: 3, text: '年' }
      ]
    }
  },
  computed: {
    paginatedTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    },
    datePickerType() {
      switch (Number(this.timeType)) {
        case 1: // 日
          return 'daterange';
        case 2: // 月
          return 'monthrange';
        case 3: // 年
          return 'yearrange';
        default:
          return 'daterange';
      }
    },
    dateFormat() {
      switch (Number(this.timeType)) {
        case 1: // 日
          return 'yyyy-MM-dd';
        case 2: // 月
          return 'yyyy-MM';
        case 3: // 年
          return 'yyyy';
        default:
          return 'yyyy-MM-dd';
      }
    },
    valueFormat() {
      return this.dateFormat;
    },
    startPlaceholder() {
      switch (Number(this.timeType)) {
        case 1: // 日
          return '开始日期';
        case 2: // 月
          return '开始月份';
        case 3: // 年
          return '开始年份';
        default:
          return '开始日期';
      }
    },
    endPlaceholder() {
      switch (Number(this.timeType)) {
        case 1: // 日
          return '结束日期';
        case 2: // 月
          return '结束月份';
        case 3: // 年
          return '结束年份';
        default:
          return '结束日期';
      }
    },
    maxMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      return `${year}-${month}`;
    },
    maxYear() {
      return new Date().getFullYear();
    }
  },
  onLoad() {
    this.setLastSevenDays();
    // 计算表格高度
    const systemInfo = uni.getSystemInfoSync();
    this.tableHeight = systemInfo.windowHeight * 0.5; // 设置为屏幕高度的50%
  },
  methods: {
    handleTimeTypeChange(value) {
      const newTimeType = Number(value);
      if (isNaN(newTimeType)) {
        console.error('无效的timeType值：', value);
        return;
      }
      console.log('切换统计周期：', newTimeType);
      this.timeType = newTimeType;
      
      // 根据时间类型重置日期选择
      if (this.timeType === 1) { // 日
        this.setLastSevenDays();
      } else if (this.timeType === 2) { // 月
        this.setLastThreeMonths();
      } else if (this.timeType === 3) { // 年
        this.setLastYear();
      }
    },
    setLastSevenDays() {
      const end = new Date();
      const start = new Date();
      start.setDate(end.getDate() - 6);
      this.dateRange = [
        this.formatDate(start),
        this.formatDate(end)
      ];
      this.fetchData();
    },
    setCurrentMonthRange() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const start = new Date(year, month - 1, 1);
      this.dateRange = [
        this.formatDate(start),
        this.formatDate(now)
      ];
      this.fetchData();
    },
    setLastThreeMonths() {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth() - 2, 1);
      // 设置日期范围，使用完整日期格式
      const startDate = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-01`;
      const endDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      this.dateRange = [startDate, endDate];
      
      // 更新月份选择器的值
      const startYear = start.getFullYear();
      const startMonth = String(start.getMonth() + 1).padStart(2, '0');
      const endYear = now.getFullYear();
      const endMonth = String(now.getMonth() + 1).padStart(2, '0');
      this.selectedMonth = [`${startYear}-${startMonth}`, `${endYear}-${endMonth}`];
      this.fetchData();
    },
    setLastSixMonths() {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth() - 5, 1);
      // 设置日期范围，使用完整日期格式
      const startDate = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-01`;
      const endDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      this.dateRange = [startDate, endDate];
      
      // 更新月份选择器的值
      const startYear = start.getFullYear();
      const startMonth = String(start.getMonth() + 1).padStart(2, '0');
      const endYear = now.getFullYear();
      const endMonth = String(now.getMonth() + 1).padStart(2, '0');
      this.selectedMonth = [`${startYear}-${startMonth}`, `${endYear}-${endMonth}`];
      this.fetchData();
    },
    setLastYear() {
      const now = new Date();
      const start = new Date(now.getFullYear() - 1, 0, 1);
      const end = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      // 设置日期范围，使用完整日期格式
      const startDate = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')}`;
      const endDate = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}-${String(end.getDate()).padStart(2, '0')}`;
      this.dateRange = [startDate, endDate];
      
      // 更新年份选择器的值
      this.selectedYear = [
        String(start.getFullYear()),
        String(now.getFullYear())
      ];
      this.fetchData();
    },
    setLastThreeYears() {
      const now = new Date();
      const start = new Date(now.getFullYear() - 3, 0, 1);
      const end = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      // 设置日期范围，使用完整日期格式
      const startDate = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')}`;
      const endDate = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}-${String(end.getDate()).padStart(2, '0')}`;
      this.dateRange = [startDate, endDate];
      
      // 更新年份选择器的值
      this.selectedYear = [
        String(start.getFullYear()),
        String(now.getFullYear())
      ];
      this.fetchData();
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      switch (Number(this.timeType)) {
        case 1: // 日
          return `${year}-${month}-${day}`;
        case 2: // 月
          return `${year}-${month}`;
        case 3: // 年
          return `${year}`;
        default:
          return `${year}-${month}-${day}`;
      }
    },
    handleCurrentChange(e) {
      this.currentPage = e.current;
    },
    async fetchData() {
      if (!this.dateRange || this.dateRange.length !== 2) return;
      const params = {
        timeType: this.timeType,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
        typeId: 3, // 固定为3
        orgId: -1, // 固定为-1
        qryType: 1 // 固定为1（用电）
      };

      try {
        const [res, peakRes] = await Promise.all([
          qryStatisticsByType(params),
          getPerSquarePubEnergyPeakValue()
        ]);

        if (peakRes && peakRes.code === 200 && peakRes.data) {
          this.peakValue = peakRes.data;
        }

        if (res.code === 200 && res.data && res.data.length > 0) {
          const dayMap = new Map();
          res.data.forEach(item => {
            const calcTime = String(item.calcTime);
            const year = calcTime.substring(0, 4);
            const month = calcTime.substring(4, 6);
            const day = calcTime.substring(6, 8);
            const statisticsTime = `${year}-${month}-${day}`;

            if (!dayMap.has(statisticsTime)) {
              dayMap.set(statisticsTime, {
                statisticsTime,
                usage: item.usage.toFixed(4),
                onYear: item.onYear !== null ? (item.onYear * 100).toFixed(2) : '-',
                chainRate: item.chainRate !== null ? (item.chainRate * 100).toFixed(2) : '-',
                area: item.area.toFixed(4),
                shareUsage: item.shareUsage !== null ? item.shareUsage.toFixed(4) : '-',
                unitAmt: item.unitAmt !== null ? item.unitAmt.toFixed(2) : '-',
                allAmt: item.allAmt !== null ? item.allAmt.toFixed(2) : '-'
              });
            }
          });

          this.tableData = Array.from(dayMap.values()).sort((a, b) =>
              new Date(a.statisticsTime) - new Date(b.statisticsTime)
          );
          this.total = this.tableData.length;
          this.currentPage = 1;

          // 更新图表数据
          this.updateChartData();
        } else {
          // 清空数据
          this.tableData = [];
          this.total = 0;
          this.currentPage = 1;
          this.chartData = {
            categories: [],
            series: []
          };
        }
      } catch (error) {
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
        // 清空数据
        this.tableData = [];
        this.total = 0;
        this.currentPage = 1;
        this.chartData = {
          categories: [],
          series: []
        };
      }
    },
    updateChartData() {
      const categories = this.tableData.map(item => item.statisticsTime);
      const usageData = this.tableData.map(item => Number(item.usage));
      const onYearData = this.tableData.map(item => Number(item.onYear));
      const chainRateData = this.tableData.map(item => Number(item.chainRate));

      // 获取当前能耗类型的峰值
      let peak = 0;
      if (this.energyType === 1 && this.peakValue.dayElectricityPeakValue != null)
        peak = this.peakValue.dayElectricityPeakValue;
      if (this.energyType === 2 && this.peakValue.dayWaterPeakValue != null)
        peak = this.peakValue.dayWaterPeakValue;
      if (this.energyType === 3 && this.peakValue.dayGasPeakValue != null)
        peak = this.peakValue.dayGasPeakValue;

      // 为超出峰值的数据设置不同的颜色
      const usageDataWithColor = usageData.map(value => ({
        value: value,
        color: value > peak ? '#ff4d4f' : '#409EFF'  // 超出峰值为红色，否则为蓝色
      }));

      this.chartData = {
        categories: categories,
        series: [
          {
            name: '使用量',
            data: usageDataWithColor,
            type: 'column'
          },
          {
            name: '同比',
            data: onYearData,
            type: 'line'
          },
          {
            name: '环比',
            data: chainRateData,
            type: 'line'
          }
        ]
      };

      // 更新图表配置
      this.chartOpts = {
        ...this.chartOpts,
        title: {
          name: '公共区域用电均摊用量图表',
          fontSize: 16,
          color: '#333'
        },
        markLine: {
          data: [{
            yAxis: peak,
            lineStyle: {
              color: '#faad14',
              type: 'dashed',
              width: 2
            },
            label: {
              formatter: `峰值: ${peak}`,
              position: 'end',
              color: '#faad14'
            }
          }]
        }
      };
    },
    onMonthChange(value) {
      if (value && value.length === 2 && value[0] && value[1]) {
        this.selectedMonth = value;
        const [startYear, startMonth] = value[0].split('-');
        const [endYear, endMonth] = value[1].split('-');
        const startDate = `${startYear}-${startMonth}-01`;
        const endDate = `${endYear}-${endMonth}-${new Date(endYear, endMonth, 0).getDate()}`;
        this.dateRange = [startDate, endDate];
        this.fetchData();
      }
    },
    onYearChange(value) {
      if (value && value.length === 2 && value[0] && value[1]) {
        this.selectedYear = value;
        const [startYear, endYear] = value;
        const startDate = `${startYear}-01-01`;
        const endDate = `${endYear}-12-31`;
        this.dateRange = [startDate, endDate];
        this.fetchData();
      }
    },

  }
}
</script>

<style lang="scss">
.container {
  padding: 20rpx;

  .card {
    background-color: #fff;
    border-radius: 8rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.1);
  }

  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .filter-row {
      display: flex;
      align-items: center;
      gap: 20rpx;
      width: 100%;

      .segmented-control {
        display: flex;
        background-color: #f0f2f5;
        border-radius: 8rpx;
        padding: 2px;
        margin-left: 10px;

        .segment {
          padding: 6px 12px;
          font-size: 14px;
          //color: #606266;
          //cursor: pointer;
          transition: all 0.3s;
          //border-radius: 8rpx;
          text-align: center;
          min-width: 60px;

          &.active {
            background-color: #409EFF;
            color: white;
            font-weight: 500;
          }

          &:hover:not(.active) {
            color: #666;
            background-color: #e0e0e0;
          }
        }
      }
    }

    .date-row {
      margin-top: 10rpx;
      display: flex;
      align-items: center;
      gap: 20rpx;
      
      .date-picker-container {
        flex: 1;
        min-width: 0; // 防止flex子项溢出
        
        .date-picker {
          width: 100%;
        }
      }

      .button-group {
        display: flex;
        gap: 20rpx;
        flex-shrink: 0; // 防止按钮被压缩

        button {
          margin: 0;
          font-size: 24rpx;
          padding: 0 20rpx;
          height: 60rpx;
          line-height: 60rpx;
          white-space: nowrap; // 防止按钮文字换行
        }
      }
    }
  }

  .chart-container {
    width: 100%;
    height: 800rpx;
    position: relative;

    .no-data {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      color: #999;
      background: rgba(255, 255, 255, 0.8);
      z-index: 2;
    }
  }

  .table-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20rpx;
    color: #333;
  }

  .table {
    width: 100%;
    background: #fff;
    border-radius: 8rpx;
    overflow: hidden;

    .table-header {
      display: flex;
      background-color: #f8f9fa;
      position: sticky;
      top: 0;
      z-index: 1;

      .th {
        flex: 1;
        padding: 24rpx 12rpx;
        text-align: center;
        font-weight: 500;
        font-size: 26rpx;
        color: #666;
        white-space: nowrap;
      }
    }

    .table-body {
      .tr {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.3s;

        &:active {
          background-color: #f5f5f5;
        }

        .td {
          flex: 1;
          padding: 24rpx 12rpx;
          text-align: center;
          font-size: 26rpx;
          color: #333;
          white-space: nowrap;

          &.up {
            color: #f56c6c;
          }

          &.down {
            color: #67c23a;
          }
        }
      }
    }
  }

  .pagination {
    margin-top: 20rpx;
    display: flex;
    justify-content: center;
    padding: 20rpx 0;
  }
}

.month-picker-wrapper,
.year-picker-wrapper {
  width: 100%;
}
</style>
