<template>
	<view class="container">
		<DaHuaHrpDeviceList
			:show-type="showType" 
			:batch-id="queryParams.assistRepairBatchinfoId" 
		/>
		<RepairList
			:batch-id="queryParams.assistRepairBatchinfoId" 
			@detail="handleDetail" 
		/>
		<InspectList
			:batch-id="queryParams.assistRepairBatchinfoId" 
			@detail="handleDetail" 
		/>
	</view>
</template>

<script>
	import DaHuaHrpDeviceList from '@/components/assistantDecision/DaHuaHrpDeviceList.vue'
	import RepairList from '@/components/assistantDecision/RepairList.vue'
	import InspectList from '@/components/assistantDecision/InspectList.vue'

	export default {
		components: {
      DaHuaHrpDeviceList,
      RepairList,
      InspectList
		},
		data() {
			return {
				showType: 'hrp', // 默认显示类型
				queryParams: {
					assistRepairBatchinfoId: 0
				}
			}
		},
		onLoad(options) {
			console.log("options", options)
			// 接收页面传参，决定显示哪种类型的卡片
			const id = parseInt(options.id)
			this.queryParams.assistRepairBatchinfoId = parseInt(options.assistRepairBatchinfoId)
      console.log("this.queryParams.assistRepairBatchinfoId",this.queryParams.assistRepairBatchinfoId)
	   // 确保立即更新 batchId
	      
			// 根据id判断类型
			if (id === 1) {
				this.showType = 'hrp'
			} else if (id === 3) {
				this.showType = 'dahua'
			}
		},
		methods: {
			handleDetail(item) {
				// 处理详情点击事件
				console.log('Detail item:', item)
				// uni.navigateTo({
				//   url: '/portal/statistics/detail',
				// })
			}
		}
	}
</script>

<style lang="scss">
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}
</style>
