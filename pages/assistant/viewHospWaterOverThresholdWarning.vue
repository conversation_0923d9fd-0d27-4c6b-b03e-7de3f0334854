<template>
  <view class="energy-consumption">
    <view class="data-card">
      <energy-statistics
          :energy-type="params.qryType"
          :type-id="params.typeId"
          :dept-id="params.orgId"
          :start="params.startTime"
          :end="params.endTime"
          :time-type="params.timeType"
      />

    </view>
    <InspectList
        :batch-id="assistRepairBatchinfoId"
        :content-type="0"
    />
  </view>
</template>

<script>
import { getHospElectricityWaterGasWarning } from "@/api/portal/statistics"
import ViewEnergy from '@/components/MyFormComponents/viewEnergy.vue'
import InspectList from '@/components/assistantDecision/InspectList.vue'
import EnergyStatistics from "@/components/MyFormComponents/viewEnergyData/energyStatistics.vue";
export default {
  name: "viewHospWaterOverThresholdWarning",
  components: {EnergyStatistics, ViewEnergy, InspectList},
  data() {
    return {
      // showType: 'hrp', // 默认显示类型
      assistRepairBatchinfoId: 0,
      params:{
        "qryType": 0,
        "typeId": null,
        "orgId": 0,
        "startTime": "",
        "endTime": "",
        "timeType": 0,
        "messageServiceType": 0,
        "assistantDecisionThresholdSetDictCode": 0
      }
    }
  },
  onLoad(options) {
    console.log("options", options)
    this.assistRepairBatchinfoId = options.assistRepairBatchinfoId;
    this.getWarning();
  },
  methods: {
    async getWarning() {
      try {
        const res = await getHospElectricityWaterGasWarning({
          assistRepairBatchinfoId: this.assistRepairBatchinfoId
        })
        if (res.code === 200) {
          console.log('API返回的原始数据:', res.data)
          const newParams = res.data.getHospElectricityWaterGasStatDto;
          console.log('新的params数据:', newParams)

          // 使用Vue.set来确保响应性
          this.$set(this, 'params', {
            ...newParams,
            qryType: Number(newParams.qryType) // 确保是数字类型
          })

          console.log('更新后的params:', this.params)
        } else {
          uni.showToast({
            title: res.msg || '获取预警信息失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取预警信息失败:', error)
        uni.showToast({
          title: '获取预警信息失败',
          icon: 'none'
        })
      }
    },
  }
}
</script>
<style scoped lang="scss">
.energy-consumption {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.data-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  padding: 20rpx;
}
</style>