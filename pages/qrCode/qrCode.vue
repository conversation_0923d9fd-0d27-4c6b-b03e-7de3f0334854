<template>
	<view class="p20">
		<view class="card bf center" @click="paymentBtn">
			<image mode="aspectFit" src="../../static/paymentCode.jpg"></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			};
		},
		methods: {
			paymentBtn() {
				uni.previewImage({
					urls: [require('../../static/paymentCode.jpg')]
				})
			}
		}
	}
</script>

<style lang="scss">

</style>