<template>
  <view class="container">
    <uni-list>
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'person-filled'}" title="昵称" :rightText="user.nickName" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'phone-filled'}" title="手机号码" :rightText="user.phonenumber" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'email-filled'}" title="邮箱" :rightText="user.email" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'auth-filled'}" title="岗位" :rightText="postGroup" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'staff-filled'}" title="角色" :rightText="roleGroup" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'calendar-filled'}" title="创建日期" :rightText="user.createTime" />
    </uni-list>
    <view style="margin: 20rpx 0;">
      <FormHandSign
        :field="signField"
        :formData="formData"
        @update:value="onSignChange"
      />
    </view>
  </view>
</template>

<script>
  import { getUserProfile, updateEsign } from "@/api/system/user"
  import FormHandSign from '@/components/parser-nuoyi-form/child-components/FormHandSign.vue'

  export default {
    components: {
      FormHandSign
    },
    data() {
      return {
        user: {},
        roleGroup: "",
        postGroup: "",
        formData: {
          esign: {
            value: ''
          }
        },
        signField: {
          __vModel__: 'esign',
          __config__: {
            required: false,
            buttonText: '手写签名',
            defaultValue: ''
          },
          disabled: false
        },
        isInitSign: true,
        pendingSignature: null // 用于存储待处理的签名
      }
    },
    onLoad() {
      this.isInitSign = true
      this.getUser()
      
      // 2秒后自动重置isInitSign标志，避免一直阻止事件
      setTimeout(() => {
        console.log('自动重置 isInitSign')
        this.isInitSign = false
      }, 2000)
    },
    methods: {
      getUser() {
        getUserProfile().then(response => {
          this.user = response.data
          this.roleGroup = response.roleGroup
          this.postGroup = response.postGroup
          
          // 设置初始签名值
          if (response.data.esignUrl) {
            console.log('设置初始签名值:', response.data.esignUrl)
            this.formData.esign.value = response.data.esignUrl
            // 同时更新 signField 的 defaultValue
            this.signField.__config__.defaultValue = response.data.esignUrl
          }
          
          // 处理之前保存的待处理签名
          if (this.pendingSignature) {
            console.log('处理待处理的签名:', this.pendingSignature)
            this.onSignChange({ 
              curField: this.signField, 
              curVal: this.pendingSignature 
            })
            this.pendingSignature = null
          }
        })
      },
      onSignChange({ curField, curVal }) {
        console.log('onSignChange 触发:', curVal)
        
        // 如果用户ID还没准备好，先保存签名值，等用户信息加载完再处理
        if (!this.user || !this.user.userId) {
          console.log('用户ID未准备好，保存签名值')
          this.pendingSignature = curVal
          return
        }
        
        // 初始化阶段不上传签名
        if (this.isInitSign) {
          console.log('初始化阶段，不上传签名')
          this.isInitSign = false
          this.formData.esign.value = curVal
          return
        }
        
        console.log('上传签名图片:', curVal)
        updateEsign({ userId: this.user.userId, esignUrl: curVal }).then(response => {
          if (response.code === 200) {
            console.log('签名更新成功')
            const url = this.$replaceUrlDomain(curVal)
            this.formData.esign.value = url;
          }
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }
</style>
