<template>
    <view style="width: 100%;height: 100%;">
        <piaoyiSignature @onSuccess="onSuccess" @onError="onError"/>
    </view>
</template>
<script>
import piaoyiSignature from '@/uni_modules/piaoyi-signature/components/piaoyi-signature/piaoyi-signature.vue'
export default {
    components: {
        piaoyiSignature
    },
    data() {
        return {
        }
    },
    methods: {
        onSuccess(e) {
            console.log(e)
        },
        onError(e) {
            console.log(e)
        }
    }
}
</script>
<style scoped>

</style>