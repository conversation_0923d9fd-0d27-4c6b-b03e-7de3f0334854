
<template>
	<view>
		
	   
	   <uni-forms-item label="请选择日期" label-width="200">
	   	<view @click="showPicker" class="datetime-picker">
	   		<uni-icons custom-prefix="custom-icon" class="icon-calendar" type="calendar" size="20" color="#c2c6cd"/>
	   		{{date || '请选择日期'}}
	   	</view>
	   </uni-forms-item>

	   <uni-card title="日能耗排名">
	   <qiun-data-charts
		 type="pie"
		
		 :chartData="chartDayData"
	   />
	  </uni-card>
	  <uni-card title="月能耗排名">
	   <qiun-data-charts
	  		 type="pie"
	  		
	  		 :chartData="chartMonthData"
	   />
	  </uni-card>
	  <uni-card title="年能耗排名">
	   <qiun-data-charts
	  		 type="pie"
	  		
	  		 :chartData="chartYearData"
	   />
	  </uni-card>
  </view>
</template>

<script>
import {getEnergyRankData} from '../../api/energy.js';
export default {
  data() {
	  const currentDate = this.getDate({
	              format: true
	          })
    return {
	  date: currentDate,
      chartDayData: {},
	  chartMonthData: {},
	  chartYearData: {},
	  dayRank:[],
	  monthRank:[],
	  yearRank:[],
      //您可以通过修改 config-ucharts.js 文件中下标为 ['pie'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
   
    };
  },
  onReady() {
    // this.getServerData();
	this.showEnergyRankDate(this.getDate({
	              format: true
	          }),1)
  },
  
  methods: {
	  showPicker() {
	  		dd.datePicker({
	  			format: 'yyyy-MM-dd',
	  			// currentDate: '2012-12-12',
	  			success: (res) => {
	  				if (res) {
					  console.log(res)
	  				  this.date = res.date;
					  this.showEnergyRankDate(res.date,2)
					 

					}
	  			},
	  		});
	  	},
	showEnergyRankDate(params,type){
		console.log(params)
		let date
		if(type==1)
		    date= params.split('-')
		else
			date= params.split('/')				      
		let queryDay={}
		queryDay.dateType=0
		queryDay.date=date[0]+'-'+date[1]+'-'+date[2]
		//日能耗
		console.log(queryDay)
		this.getEnergyRankData(queryDay)
		//月能耗
		let queryMonth={}
		queryMonth.dateType=1
		queryMonth.date=date[0]+'-'+date[1]
								   
		this.getEnergyRankData(queryMonth)
		//年能耗
		let queryYear={}
		queryYear.dateType=2
		queryYear.date=date[0]
		console.log(queryYear)
		this.getEnergyRankData(queryYear)
		
	},
	 async getEnergyRankData(query){
			  await getEnergyRankData(query).then(response => {
				console.log(response)
				var newArr=[] 
				response.data.forEach(element => {
				  newArr.push({
					value:element.totalEnergy,
					name:element.deptName,
				  })
				  
				});
				console.log(newArr)
				if(query.dateType==0){
			  
				  this.dayRank=newArr
	  
				}
				else if (query.dateType==1) {
				  this.monthRank=newArr
				  
				} else {
				  this.yearRank=newArr
				}
				this.show(query.dateType)
				
			  })
		  },
   show(type){
	   let res = {
	       series: [
	         {
	           data: this.dayRank
	         }
	       ]
	     };
	   if(type==0){
		  this.chartDayData=JSON.parse(JSON.stringify(res));
	   }
	   else if(type==1){
		   res.series[0].data=this.monthRank
		   this.chartMonthData=JSON.parse(JSON.stringify(res));
	   }
	   else{
		   res.series[0].data=this.yearRank
		   this.chartYearData=JSON.parse(JSON.stringify(res));
	   }
	  
	 
   },
 
   getDate(type) {
			  const date = new Date();
			  let year = date.getFullYear();
			  let month = date.getMonth() + 1;
			  let day = date.getDate();
  
			  if (type === 'start') {
				  year = year - 60;
			  } else if (type === 'end') {
				  year = year + 2;
			  }
			  month = month > 9 ? month : '0' + month;
			  day = day > 9 ? day : '0' + day;
			  return `${year}-${month}-${day}`;
		  },
    getServerData() {
      // //模拟从服务器获取数据时的延时
      // setTimeout(() => {
      //   //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
       
      // }, 500);
    },
  }
};
</script>

<style scoped>
  /* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
  .charts-box {
    width: 100%;
    height: 300px;
  }
  .tabBox {
  	display: flex;
  	justify-content: space-between;
  	font-size: 30rpx;
  	background-color: #fff;
  	padding: 30rpx;
  	color: #8c8c8c;
  	line-height: 20rpx;
  	margin: 15rpx 30rpx;
  	border-radius: 15rpx;
  	border: 1px solid #EBEEF5;
  	box-shadow: rgba(0, 0, 0, 0.08) 0px 0px 3px 1px;
  }
  
  .active {
  	color: #3f6af8;
  }
  .datetime-picker,.datetime-picker-placeholder{
  	width: 100%;
  	flex: 1;
  	line-height: 1;
  	font-size: 14px;
  	height: 40px;
  	display: flex;
  	box-sizing: border-box;
  	flex-direction: row;
  	align-items: center;
  	border: 1px solid #dcdfe6;
  	border-radius: 4px;
  	padding-left:3px;
  }
  
  .icon-calendar{
  	margin-right:2px;
  }

</style>

