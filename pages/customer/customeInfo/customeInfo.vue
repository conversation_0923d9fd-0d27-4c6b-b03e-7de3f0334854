<template>
	<view class="p20">

		<view class="customer_info p20 bf card">
			<view class="customer_item just-sbet bf" v-for="(item,key,index) in customerInfoKey" :key="key">
				<view class="content" v-if="customerInfoKey[key]!=undefined || customerInfoKey[key]!=null ">
					{{item}}:
				</view>
				<view class="key" @click="makePhoneCall(key)"
					v-if="customerInfoKey[key]!=undefined || customerInfoKey[key]!=null ">
					{{customerInfo[key]==null?'':customerInfo[key]}}
				</view>

			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				customerInfo: {},
				customerInfoKey: {
					clientName: "客户姓名",
					phoneNumber: "联系方式",
					company: "公司名称",
					companyAddress: "公司地址",
					idCard: "发票统一信用号",
					quota: "备注",
					createTime: "创建时间",
					updateTime: "修改时间",
				}
			};
		},
		onLoad(option) {
			this.customerInfo = JSON.parse(option.info)
			console.log(this.customerInfo);
		},
		methods: {
			makePhoneCall(key) {
				if (key != 'phoneNumber') return
				uni.makePhoneCall({
					phoneNumber: this.customerInfo.phoneNumber,
					success: (res) => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.log('拨打电话失败');
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.customer_info {
		// display: grid;
		// gap: 45rpx;

		.customer_item {
			padding: 30rpx 0;

			.content {
				flex-basis: 130rpx;
				margin-right: 30rpx;
			}

			.key {
				flex: 1;
				text-align: right;

			}
		}


	}
</style>