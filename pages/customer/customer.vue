<template>
	<view class=" customer">


		<view class="nav_fixed bf p20 flex center">
			<uni-search-bar v-model="searchValue" class="w-full" @blur="flag=false" radius="5" placeholder="请输入客户姓名"
				clearButton="auto" @confirm="search" />
			<view class="center searchBtn" @click="searchBtn">
				搜索
			</view>
		</view>



		<view class="p20 customer_list grid gap">
			<view class="customer_item bf " v-for="(item,index) in customList" :key="index" >
				<view class="title just-sbet ">
					<view class="idCard">
						{{item.idCard==null?'发票统一信用号未知':item.idCard}}
					</view>
					<view class="tips">
						客户台账
					</view>
				</view>
				<view class="customer_info just-sbet">
					<view class="company">
						{{item.company==null?'客户姓名':item.company}}
					</view>
					<view class="clientName">
						{{item.clientName==null?'客户姓名未知':item.clientName}}
					</view>
				</view>
				<view class="customer_info just-sbet">
					<view class="company">
						联系方式
					</view>
					<view class="clientName" @click.stop="makePhoneCall(item.phoneNumber)">
						{{item.phoneNumber}}
					</view>
				</view>
				<view class="customer_btn" @click="customerBtnClick(item)">
					查看详情
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getList,
		getOneClientInfo
	} from '../../api/customer';
	import {
		show,
		topage
	} from '../../common/common_methods';
	export default {
		data() {
			return {
				customList: [],
				query: {
					pageNum: 1,
					pageSize: 15
				},
				searchValue: "",
				flag: false
			};
		},
		onReachBottom() {
			this.query.pageNum++
			this.getCustomList()

		},
		onLoad() {
			this.getCustomList()
		},
		methods: {
			async getCustomList() {
				let {
					list
				} = await getList(this.query)
				this.customList = [...this.customList, ...list]
				console.log(list);
			},
			customerBtnClick(item) {
				topage('./customeInfo/customeInfo', item)
			},
			makePhoneCall(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber: phoneNumber,
					success: (res) => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.log('拨打电话失败');
					}
				})
			},
			async searchBtn() {
				if (this.searchValue.length == 0) {
					return show('请输入客户姓名', 1)
				}
				// 搜索按钮
				let searchValue = await getOneClientInfo(this.searchValue)
				if (searchValue != '') {
					topage('./customeInfo/customeInfo', searchValue)
				} else {

					show('未找到该客户', 1)
					this.searchValue = ''
					this.flag = true
				} 

			},
			async search(res) {
				let searchValue = await getOneClientInfo(res.value)
				if (searchValue != '') {
					topage('./customeInfo/customeInfo', searchValue)
				} else {
					show('未找到该客户', 1)
				}
			},
		}
	}
</script> 

<style lang="scss">
	.searchBtn {
		margin-left: 20rpx;
		background-color: #5b73f9;	
		padding: 10rpx 20rpx;
		color: #fff;
		width: 160rpx;
		text-align: center;
		border-radius: 10rpx;
	}

	.customer {
		.customer_list {
			margin-top: 146rpx;

			.customer_item {
				box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
				padding: 30rpx;
				border-radius: 10rpx;


				.title {
					margin: 10rpx 0;

					.idCard {
						color: #000;
					}

					.tips {
						border-radius: 20rpx;
						padding: 5rpx 20rpx;
						background-color: rgba(91, 115, 249, 0.1);
						color: #5b73f9;
					}
				}

				.customer_info {
					border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
					padding-bottom: 20rpx;
					margin-top: 40rpx;
					color: #868686;

					.company {}

					.clientName {
						color: #333;
						font-weight: bold;
					}
				}

				.customer_btn {
					margin-top: 45rpx;
					padding: 20rpx;
					text-align: center;
					background-color: #5b73f9;
					color: #fff;
					border-radius: 10rpx;
				}
			}
		}


	}
</style>