<template>
	<view class="p20">

		<movable-area>
			<movable-view :x="x" :y="y" direction="all">
				<view class="add_opportunity center fl" @click="addOpportunity">
					<uni-icons type="cloud-upload" size="30" color="#fff"></uni-icons>
					<text>新增</text>
				</view>
			</movable-view>
			<view class=" customer_list grid gap" style="grid-template-columns: repeat(1,1fr);">
				<view class="customer_item bf " v-for="(item,index) in leaseAssetList" :key="index">
					<view class="title just-sbet ">
						<view class="idCard">
							{{getNum(item.assetId)}}

						</view>
						<view class="tips">
							掌上租控
						</view>
					</view>
					<view class="customer_info just-sbet">
						<view class="company">
							地址
						</view>
						<view class="clientName">
							{{item.newAddress}}
						</view>
					</view>
					<view class="customer_info just-sbet">
						<view class="company">
							管理员
						</view>
						<view class="clientName">
							{{getUserName(item.administrators)}}
						</view>
					</view>
					<view class="customer_btn" @click="customerBtn(item)">
						编辑信息
					</view>
				</view>
			</view>
		</movable-area>

		<uni-popup ref="popup" type="bottom" backgroundColor="#fff" animation>
			<view class="" style="position: relative; z-index: 11;">
				<view class="center" style="padding: 20rpx; font-weight: bold;font-size: 36rpx;">
					请选择资产编号
				</view>
				<picker-view :indicator-style="indicatorStyle" @change="pickerChange" class="picker-view">
					<picker-view-column>
						<view class="item" v-for="(item,index) in assetInfoList" :key="index">{{getNum(item.id)}}</view>
					</picker-view-column>
				</picker-view>
				<view class="btn" @click="close">
					确定
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="alertDialog" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确定" title="通知" :content="contentMsg"
				@confirm="dialogConfirm">
				<view v-if="isAdd">
					确认新增<text class="tipsWord"> {{getNum(formData.assetId)}}</text>吗？
				</view>
				<view v-else>
					确认将 <text class="tipsWord">{{getNum(updateData.assetId)}}</text>修改为<text
						class="tipsWord">{{getNum(formData.assetId)}}</text> 吗？
				</view>
			</uni-popup-dialog>
		</uni-popup>


	</view>
</template>

<script>
	import {
		assetInfo
	} from '../../api/assetInfo';
	import {
		getList
	} from '../../api/customer';
	import {
		addLeaseAsset,
		getLeaseAssetList,
		updateLeaseAsset
	} from '../../api/leaseAsset';
	import {
		listUser
	} from '../../api/system/user';
	import {
		getAssetNum,
		show
	} from '../../common/common_methods';
	export default {
		data() {
			return {
				leaseAssetList: [],
				assetInfoList: [],
				userList: [],
				clientList: [],
				params: {
					pageNum: 1,
					pageSize: 10
				},
				x: 300,
				y: 480,
				indicatorStyle: `height: 50px;`,
				formData: {
					assetId: ""
				},
				updateData: {

				},
				updateId: "", // 编辑的资产id
				contentMsg: "",
				isAdd: true, // 是否新增

			};
		},
		onLoad() {
			this.initUser()
			this.initClientList()
			this.initAssetInfoList()
		},
		onReachBottom() {
			this.params.pageNum++
			this.initLeaseAssetList()
		},
		methods: {
			customerBtn(item) {

				this.updateData = item

				this.isAdd = false
				// 编辑信息
				this.$refs.popup.open()

			},
			addOpportunity() {
				// 新增租控
				this.$refs.popup.open()
				this.isAdd = true

			},
			async close() {
				// if (this.isAdd) {
				// 	this.contentMsg = '确认新增' + this.getNum(this.formData.assetId) + '吗？'
				// 	// this.$refs.popup.close()
				// } else {
				// 	this.contentMsg = '确认将'+ this.getNum(this.updateData.assetId)+'修改为'+ this.getNum(this.formData.assetId) + '吗？'
				// }
				this.$refs.alertDialog.open()
			},
			async dialogConfirm() {
				if (this.isAdd) {
					let res = await addLeaseAsset(this.formData)
					show('新增成功', 200)
					this.leaseAssetList = []
				} else {
					this.updateData.assetId = this.formData.assetId
					let res = await updateLeaseAsset(this.updateData)
					show('编辑成功', 200)
				}
				this.$refs.popup.close()
				this.formData.assetId = ""
				this.initAssetInfoList()
			},
			pickerChange({
				detail
			}) {
				console.log(detail.value);
				this.formData.assetId = this.assetInfoList[detail.value].id

			},
			async initAssetInfoList() {
				uni.showLoading({
					title: "加载中"
				})
				// 获取所有资产列表
				const param = {
					pageNum: 1,
					pageSize: 99999
				}
				let {
					list
				} = await assetInfo(param)
				this.assetInfoList = list
				this.formData.assetId = this.assetInfoList[0].id

				this.initLeaseAssetList()
			},
			async initLeaseAssetList() {
				// 获取掌上租控数据
				let {
					list
				} = await getLeaseAssetList(this.params)

				let assetInfoList = this.assetInfoList
				list.map(item => {
					return assetInfoList.map(jtem => {
						if (jtem.id == item.assetId) {
							let leaseAssetItem = {
								newAddress: jtem.newAddress, // 地址
								id: item.id, // 掌上租控id
								assetId: item.assetId, // 资产id
								administrators: jtem.administrators // 管理员id
							}
							this.leaseAssetList = [...this.leaseAssetList, leaseAssetItem]
						}
					})
				})
				console.log(this.leaseAssetList, 'this.leaseAssetList');
				uni.hideLoading()
			},
			async initClientList() {
				// 获取所有客户信息
				const param = {
					pageNum: 1,
					pageSize: 99999
				}
				let {
					list
				} = await getList(param);
				this.clientList = list
			},
			initUser() {
				const param = {
					pageNum: 1,
					pageSize: 10
				}
				this.userList = []
				listUser(param).then(res => {
					res.rows.map(u => {
						const user = {
							id: u.userId,
							username: u.nickName ? u.nickName : u.userName
						}
						this.userList.push(user)
					})
				})
			},
			getNum(id) {
				return getAssetNum(id)
			},
			getUserName(userIds) {
				let userName = ''
				const userIdList = userIds && userIds.length ? userIds.split(',') : []
				userIdList.map(id => {
					this.userList.map(item => {
						if (Number(id) === item.id) {
							if (userName !== '') {
								userName += ','
							}
							userName += item.username
						}
					})
				})
				if (userName === '') {
					userName = '未知管理员'
				}
				return userName
			},
		}
	}
</script>

<style lang="scss">
	.tipsWord {
		color: #3296fa;
		padding: 0 10rpx;
		font-weight: bold;
	}

	.picker-view {
		width: 750rpx;
		height: 600rpx;
		margin-top: 20rpx;
	}

	movable-area {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: -1;
		width: 100%;
		height: 100%;
	}

	movable-view {
		position: fixed;
		z-index: 111;
		background-color: #5b73f9;
		border-radius: 50%;
		color: #fff;
		width: 150rpx;
		height: 150rpx;
	}

	.item {
		line-height: 100rpx;
		text-align: center;
	}

	.add_opportunity {
		background-color: #5b73f9;
		border-radius: 50%;
		color: #fff;
		width: 150rpx;
		height: 150rpx;
		padding: 30rpx;
		text-align: center;
	}

	.btn {
		margin-top: 30rpx;
		padding: 20rpx;
		text-align: center;
		background-color: #5b73f9;
		color: #fff;
		border-radius: 10rpx;
	}

	.searchBtn {
		margin-left: 20rpx;
		background-color: #5b73f9;
		padding: 10rpx 20rpx;
		color: #fff;
		width: 160rpx;
		text-align: center;
		border-radius: 10rpx;
	}

	.customer_list {
		padding: 20rpx;

		.customer_item {
			box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
			padding: 30rpx;
			border-radius: 10rpx;


			.title {
				margin: 10rpx 0;

				.idCard {
					color: #000;
				}

				.tips {
					border-radius: 20rpx;
					padding: 5rpx 20rpx;
					background-color: rgba(91, 115, 249, 0.1);
					color: #5b73f9;
				}
			}

			.customer_info {
				border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
				padding-bottom: 20rpx;
				margin-top: 40rpx;
				color: #868686;

				.company {
					width: 20%;
				}

				.clientName {

					color: #333;
					font-weight: bold;
				}
			}

			.customer_btn {
				margin-top: 45rpx;
				padding: 20rpx;
				text-align: center;
				background-color: #5b73f9;
				color: #fff;
				border-radius: 10rpx;
			}
		}
	}
</style>