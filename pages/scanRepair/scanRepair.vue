<template>
  <view class="scan-container">
    <!-- 扫描结果展示 -->
    <scroll-view class="result-container" scroll-y v-if="item">
      <!-- 核心信息 -->
      <view class="info-card">
        <view class="card-title">核心信息</view>
        <view class="info-item">
          <text class="label">资产编号：</text>
          <text class="value highlight">{{ item.cardNumber }}</text>
        </view>
        <view class="info-item">
          <text class="label">资产名称：</text>
          <text class="value highlight">{{ item.cardName }}</text>
        </view>
        <view class="info-item">
          <text class="label">卡片编号：</text>
          <text class="value cardId">{{ item.cardId }}</text>
        </view>
        <view class="info-item">
          <text class="label">旧资产编号：</text>
          <text class="value">{{ item.oldCardNumber }}</text>
        </view>
      </view>

      <!-- 规格信息 -->
      <view class="info-card">
        <view class="card-title">规格信息</view>
        <view class="info-item">
          <text class="label">型号规格：</text>
          <text class="value">{{ item.cardSpec || '-' }}</text>
        </view>
        <view class="info-item">
          <text class="label">计量单位：</text>
          <text class="value">{{ item.unitName }}</text>
        </view>
        <view class="info-item">
          <text class="label">出厂编号：</text>
          <text class="value">{{ item.factoryNumber }}</text>
        </view>
        <view class="info-item">
          <text class="label">序列号：</text>
          <text class="value">{{ item.serialNumber }}</text>
        </view>
        <view class="info-item">
          <text class="label">生产日期：</text>
          <text class="value">{{ item.produceDate }}</text>
        </view>
      </view>

      <!-- 存放信息 -->
      <view class="info-card">
        <view class="card-title">存放信息</view>
        <view class="info-item">
          <text class="label">仓库：</text>
          <text class="value">{{ item.warehouse }}</text>
        </view>
        <view class="info-item">
          <text class="label">存放地点：</text>
          <text class="value">{{ item.storagePlace }}</text>
        </view>
        <view class="info-item">
          <text class="label">数量：</text>
          <text class="value">{{ item.number }}</text>
        </view>
      </view>

      <!-- 使用部门 -->
      <view class="info-card">
        <view class="card-title">使用部门</view>
        <view class="info-item">
          <text class="label">科室编号：</text>
          <text class="value highlight">{{ item.deptCode }}</text>
        </view>
        <view class="info-item">
          <text class="label">使用科室：</text>
          <text class="value highlight">{{ item.deptName }}</text>
        </view>
        <view class="info-item">
          <text class="label">报修联系人：</text>
          <text class="value">{{ item.repairContact }}</text>
        </view>
        <view class="info-item">
          <text class="label">报修电话：</text>
          <text class="value">{{ item.repairContactPhone }}</text>
        </view>
      </view>

      <!-- 时间信息 -->
      <view class="info-card">
        <view class="card-title">时间信息</view>
        <view class="info-item">
          <text class="label">使用日期：</text>
          <text class="value">{{ formatDate(item.useDate) }}</text>
        </view>
        <view class="info-item">
          <text class="label">报废时间：</text>
          <text class="value">{{ formatDate(item.scrapDate) }}</text>
        </view>
        <view class="info-item">
          <text class="label">到期日期：</text>
          <text class="value">{{ formatDate(item.expireDate) }}</text>
        </view>
      </view>

      <!-- 分类信息 -->
      <view class="info-card">
        <view class="card-title">分类信息</view>
        <view class="info-item">
          <text class="label">一级分类：</text>
          <text class="value">{{ item.class1Id }}</text>
        </view>
        <view class="info-item">
          <text class="label">一级分类名称：</text>
          <text class="value">{{ item.class1Name }}</text>
        </view>
        <view class="info-item">
          <text class="label">二级分类：</text>
          <text class="value">{{ item.class2Id }}</text>
        </view>
        <view class="info-item">
          <text class="label">二级分类名称：</text>
          <text class="value">{{ item.class2Name }}</text>
        </view>
      </view>

      <!-- 财务信息 -->
      <view class="info-card">
        <view class="card-title">财务信息</view>
        <view class="info-item">
          <text class="label">金额：</text>
          <text class="value">{{ formatCurrency(item.price) }}</text>
        </view>
        <view class="info-item">
          <text class="label">累计折旧：</text>
          <text class="value">{{ item.depreciation }}</text>
        </view>
        <view class="info-item">
          <text class="label">账面净值：</text>
          <text class="value status-tag">{{ item.netValue }}</text>
        </view>
        <view class="info-item">
          <text class="label">月折旧额：</text>
          <text class="value">{{ item.monthDepreciation }}</text>
        </view>
        <view class="info-item">
          <text class="label">累计折旧月数：</text>
          <text class="value">{{ item.monthDepreciationNum }}</text>
        </view>
        <view class="info-item">
          <text class="label">折旧状态：</text>
          <text class="value">{{ item.depreciationStatus }}</text>
        </view>
        <view class="info-item">
          <text class="label">折旧年限：</text>
          <text class="value">{{ item.depreciationLife }}年</text>
        </view>
      </view>

      <!-- 供应商信息 -->
      <view class="info-card">
        <view class="card-title">供应商信息</view>
        <view class="info-item">
          <text class="label">供应商：</text>
          <text class="value">{{ item.venderName }}</text>
        </view>
        <view class="info-item">
          <text class="label">厂家名称：</text>
          <text>{{ item.factoryName }}</text>
        </view>
        <view class="info-item">
          <text class="label">供货人：</text>
          <text class="value">{{ item.supplier }}</text>
        </view>
        <view class="info-item">
          <text class="label">供货人联系方式：</text>
          <text class="value">{{ item.supplierContact }}</text>
        </view>
      </view>

      <!-- 故障描述 -->
      <view class="info-card">
        <view class="card-title">故障描述</view>
        <uni-forms-item class="flex align-center p20" label-width="80px" label="故障描述" name="faultDetail">
          <uni-easyinput type="text" v-model="formData.faultDetail" placeholder="请输入故障描述" />
        </uni-forms-item>
      </view>

      <button class="btn_big" @click="submitRepair">报修</button>
    </scroll-view>
  </view>
</template>
<script>
import {getCard} from '../../api/HRP/card'
import {addHrpRepairRecord} from '@/api/repair'
export default {
  data() {
    return {
      item:null,
      formData: {
        recordTitle: null,
        deviceName: null,
        cardNumber: null,
        deviceUseDeptName: null,
        deviceLocation: null,
        repairImages: [],
        faultDetail: null
      }
    }
  },
  onLoad(options) {
      const cardNumber = decodeURIComponent(options.cardNumber || '');
      console.log('接收到的卡号:', cardNumber);
      this.cardNumber = cardNumber;
	  this.getCard();
    },
  methods: {
    async getCard() {
      const res = await getCard(this.cardNumber)
      console.log("res",res)
      this.item = res.data;
      // 初始化报修表单数据
      if (this.item) {
        this.formData.recordTitle = this.item.cardName + '报修';
        this.formData.deviceName = this.item.cardName;
        this.formData.cardNumber = this.item.cardNumber;
        this.formData.deviceUseDeptName = this.item.deptName;
        this.formData.deviceLocation = this.item.storagePlace;
      }
    },
    formatDate(dateStr) {
      return dateStr ? dateStr.replace(/-/g, '/') : '-'
    },
    formatCurrency(amount) {
      return amount ? `¥${amount.toLocaleString()}` : '-'
    },
    async submitRepair() {
      let res = await addHrpRepairRecord(this.formData);
      console.log(res)
      if(res) {
        uni.navigateTo({
          url: '/pages/repairList/myRepair'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.scan-container {
  height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
  .scan-area { /* 保持原有扫描框样式 */ }
  .result-container {
    height: calc(100vh - 40rpx);
  }
  .info-card {
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      padding-bottom: 20rpx;
      border-bottom: 2rpx solid #eee;
      margin-bottom: 25rpx;
    }
    .info-item {
      display: flex;
      justify-content: space-between;
      padding: 18rpx 0;
      .label {
        color: #666;
        font-size: 28rpx;
        width: 200rpx;
      }
      .value {
        flex: 1;
        color: #333;
        font-size: 28rpx;
        text-align: right;
        word-wrap: break-word;
        word-break: break-word;
        min-width: 0;
        &.highlight {
          color: #007AFF;
          font-weight: 500;
        }
        &.status-tag {
          background: #f0f0f0;
          padding: 6rpx 16rpx;
          border-radius: 8rpx;
        }
      }
    }
  }
  .rescan-btn {
    width: 70%;
    height: 80rpx;
    line-height: 80rpx;
    background: #007AFF;
    color: white;
    border-radius: 40rpx;
    margin: 40rpx auto;
  }
  .btn_big {
    padding: 20rpx;
    font-size: 50rpx;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #5b73f9;
    color: #fff;
    border-radius: 10rpx;
    margin: 40rpx auto;
    width: 90%;
  }
}
</style>
