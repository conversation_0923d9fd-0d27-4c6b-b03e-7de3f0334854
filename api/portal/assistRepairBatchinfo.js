import request from '@/utils/request'

// 查询设备报修分批统计列表
export function listAssistRepairBatchinfo(query) {
  return request({
    url: '/portal/ads/assistRepairBatchinfo/list',
    method: 'get',
    params: query
  })
}

// 查询设备报修分批统计详细
export function getAssistRepairBatchinfo(id) {
  return request({
    url: '/portal/ads/assistRepairBatchinfo/' + id,
    method: 'get'
  })
}

// 新增设备报修分批统计
export function addAssistRepairBatchinfo(data) {
  return request({
    url: '/portal/ads/assistRepairBatchinfo',
    method: 'post',
    data: data
  })
}

// 修改设备报修分批统计
export function updateAssistRepairBatchinfo(data) {
  return request({
    url: '/portal/ads/assistRepairBatchinfo',
    method: 'put',
    data: data
  })
}

// 删除设备报修分批统计
export function delAssistRepairBatchinfo(id) {
  return request({
    url: '/portal/ads/assistRepairBatchinfo/' + id,
    method: 'delete'
  })
}
