import request from '@/utils/request'

// 查询门户菜单列表
export function listMenu(query) {
  return request({
    url: '/portal/menu/list',
    method: 'get',
    params: query
  })
}

// 查询门户菜单详细
export function getMenu(id) {
  return request({
    url: '/portal/menu/' + id,
    method: 'get'
  })
}

export function getMenu4Dahua(data) {
  return request({
    url: '/portal/menu/dahuaMenu',
    data: data,
    method: 'post'
  })
}

// 新增门户菜单
export function addMenu(data) {
  return request({
    url: '/portal/menu',
    method: 'post',
    data: data
  })
}

// 修改门户菜单
export function updateMenu(data) {
  return request({
    url: '/portal/menu',
    method: 'put',
    data: data
  })
}

// 删除门户菜单
export function delMenu(id) {
  return request({
    url: '/portal/menu/' + id,
    method: 'delete'
  })
}
