import request from '@/utils/request'
// 大华告警详情
export function alarmDetail(query) {
  return request({
    url: '/portal/messageCenter/alarmDetail',
    method: 'get',
    params: query
  })
}

export function deviceStatusDetail(query){
  return request({
    url: '/portal/messageCenter/deviceStatusDetail',
    method: 'get',
    params: query
  })
}
//获取告警消息列表
export function MessageList(query){
  return request({
    url: '/portal/messageCenter/MessageList',
    method: 'get',
    params: query
  })
}

// 批量删除消息
export function deleteMessageList(midList) {
  return request({
    url: '/portal/messageCenter/deleteMessageList',
    method: 'post',
    data: midList
  })
}

/**
 * 批量标记消息为已读
 * @param {Array} messageIdList 消息ID列表
 */
export function readMessageList(messageIdList) {
  return request({
    url: "/portal/messageCenter/readMessageList",
    method: "post",
    data: messageIdList,
  });
}
/**
 * 标记消息为已读
 * @param {number} messageId 消息ID
 */
export function readMessage(messageId) {
  return request({
    url: "/portal/messageCenter/readMessageList", // 保持 URL 不变
    method: "post",
    data: [messageId], // 将单个消息 ID 封装在数组中
  });
}

/**
 * 删除消息
 * @param {number} messageId 消息ID
 */
export function deleteMessage(messageId) {
  return request({
    url: "/portal/messageCenter/deleteMessageList", // 保持 URL 不变
    method: "post",
    data: [messageId], // 将单个消息 ID 封装在数组中
  });
}
// 批量转发消息
export function batchForwarding(dto) {
  return request({
    url: '/portal/messageCenter/batchForwarding',
    method: 'post',
    data: dto
  })
}

