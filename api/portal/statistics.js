import request from '@/utils/request'




//新增三个接口
// 统计科室（全院）日费用
export function getDeptHospCostStat4Day(data) {
  return request({
    url: '/portal/statistics/ads/depthosp/getDeptHospCostStat4Day',
    method: 'post',
    // ...formRequestConfig,
    data: data
  })
}

// 统计科室（全院）月费用
export function getDeptHospCostStat4Month(data) {
  return request({
    url: '/portal/statistics/ads/depthosp/getDeptHospCostStat4Month',
    method: 'post',
    // ...formRequestConfig,
    data: data
  })
}

// 统计科室（全院）年费用
export function getDeptHospCostStat4Year(data) {
  return request({
    url: '/portal/statistics/ads/depthosp/getDeptHospCostStat4Year',
    method: 'post',
    // ...formRequestConfig,
    data: data
  })
}

// 个人完成巡检计划、巡检设备的巡检结果统计
export function userStatList4InspectResult(data) {
  return request({
    url: '/portal/statistics/user/statlist4inspectresult',
    method: 'post',
    data: data
  })
}
// 科室(全院)巡检计划、巡检设备的巡检结果统计
export function deptStatList4InspectResult(data) {
  return request({
    url: '/portal/statistics/dept/statlist4inspectresult',
    method: 'post',
    data: data
  })
}


// 个人完成工单评价统计 | 个人(巡检 报修)统计
export function userStatList(data) {
  return request({
    url: '/portal/statistics/user/statlist',
    method: 'post',
    data: data
  })
}

// 科室(全院)完成工单评价统计 | 科室(全院)(巡检 报修)统计
export function deptStatList(data) {
  return request({
    url: '/portal/statistics/dept/statlist',
    method: 'post',
    data: data
  })
}

// 统计类型对应页面数据项标题
export function getStatType(query) {
  return request({
    url: `/portal/statistics/type/getstattype/${query}`,
    method: 'get',
  })
}

// 获取所有的巡检类型
export function listAllInspectPlanType() {
  return request({
    url: '/portal/statistics/inspectplantype/listall',
    method: 'get',
  })
}

// 获取个人待办 已办 过期 统一巡检列表
export function listUserInspectPlanByUserId(query) {
  return request({
    url: '/portal/statistics/userinspectplan/list',
    method: 'get',
    params: query
  })
}
// 获取科室待办 已办 过期 统一巡检列表
export function listDeptInspectPlanByDeptId(query) {
  return request({
    url: '/portal/statistics/deptinspectplan/list',
    method: 'get',
    params: query
  })
}

// 获取用户报修列表（分页）
export function listUserRepairRecord(query) {
  return request({
    url: '/portal/statistics/userrepairrecords/list',
    method: 'get',
    params: query
  })
}

// 获取用户发起的流程列表（分页）
export function listUserTaskProcess(query) {
  return request({
    url: '/portal/statistics/userflowtask/myprocess',
    method: 'get',
    params: query
  })
}

// 获取用户待办任务列表（分页）
export function listUserTaskTodoList(query) {
  return request({
    url: '/portal/statistics/userflowtask/todoList',
    method: 'get',
    params: query
  })
}

// 用户已办流程列表（分页）
export function listUserTaskFinishedList(query) {
  return request({
    url: '/portal/statistics/userflowtask/finishedList',
    method: 'get',
    params: query
  })
}

// 获取科室（全院）报修列表（分页）
export function listDeptRepairRecord(query) {
  return request({
    url: '/portal/statistics/deptrepairrecords/list',
    method: 'get',
    params: query
  })
}
// 获取科室（全院）发起的流程列表（分页）
export function listDeptTaskProcess(query) {
  return request({
    url: '/portal/statistics/deptflowtask/myprocess',
    method: 'get',
    params: query
  })
}

// 获取科室（全院）待办任务列表（分页）
export function listDeptTaskTodoList(query) {
  return request({
    url: '/portal/statistics/deptflowtask/todoList',
    method: 'get',
    params: query
  })
}

// 获取科室（全院）已办任务列表（分页）
export function listDeptTaskFinishedList(query) {
  return request({
    url: '/portal/statistics/deptflowtask/finishedList',
    method: 'get',
    params: query
  })
}

// 科室结果返回(科室巡检报修数 完成数 超时数)
export function getDeptRepairResultCount(query) {
  return request({
    url: '/portal/statistics/dept/deptstatisticsresult',
    method: 'get',
    params: query
  })
}
// 根据一个统计编号获取这个统计对应的Hrp设备列表
export function getSameBatchHrpList(query) {
  return request({
    url: '/portal/statistics/ads/getSameBatchHrpCardDahuaDeviceChannel/list',
    method: 'get',
    params: query
  })
}
// 根据一个统计编号获取这个统计对应的自动巡检计划
export function getSameBatchPlanList(query){
  return request({
    url: '/portal/statistics/ads/getSameBatchInspectPlans/list',
    method: 'get',
    params: query
  })
}
// 根据一个统计编号获取这个统计对应的报修列表
export function getSameBatchRepairList(query){
  return request({
    url: '/portal/statistics/ads/getSameBatchRepairRecords/list',
    method: 'get',
    params: query
  })
}
// // 导出统计数据
// export function exportAssistantStat(data) {
//   return request({
//     url: '/portal/statistics/ads/export4MsgCenterAssistantStat/export',
//     method: 'POST',
//     data: data,
//     responseType: 'arraybuffer',
//   });
// }
//获取用电用气用水告警信息
export function getHospElectricityWaterGasWarning(query){
  return request({
    url: '/portal/statistics/ads/hosp/getHospElectricityWaterGasWarning',
    method: 'get',
    params: query
  })
}
// 获取全院日能耗峰谷值（电 水 气）
export function getDayPeakValue() {
  return request({
    url: '/portal/statistics/ads/hospEnergy/dayDayPeakValue',
    method: 'get'
  })
}

// 获取全院能耗(日 月 年）每平方峰谷值（电 水 气）
export function getPerSquareEnergyPeakValue() {
  return request({
    url: '/portal/statistics/ads/hospEnergy/perSquareEnergyPeakValue',
    method: 'get'
  })
}

// 获取全院公共能耗(日 月 年）每平方峰谷值（电 ）
export function getPerSquarePubEnergyPeakValue() {
  return request({
    url: '/portal/statistics/ads/hospEnergy/perSquarePubEnergyPeakValue',
    method: 'get'
  })
}
// App导出统计科室(全院)日费用到Excel文件
export function appExportCostStat4Day() {
  return request({
    url: '/portal/statistics/ads/depthosp/appExport/deptHospCostStat4Day',
    method: 'get'
  })
}
// App导出统计科室(全院)月费用到Excel文件
export function appExportCostStat4Month() {
  return request({
    url: '/portal/statistics/ads/depthosp/appExport/deptHospCostStat4Month',
    method: 'get'
  })
}
// App导出统计科室(全院)年费用到Excel文件
export function appExportCostStat4Year() {
  return request({
    url: '/portal/statistics/ads/depthosp/appExport/deptHospCostStat4Year',
    method: 'get'
  })
}


