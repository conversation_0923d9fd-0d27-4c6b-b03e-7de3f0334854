import request from '@/utils/request'

// 获取科室员工信息
export  function  getEmpInfo(query){
  return request({
    url: '/portal/deptInfo/getEmpInfo',
    method:'get',
    params: query
  })
}

// 获取科室员工列表
export function getEmpList(query) {
  return request({
    url: '/portal/deptInfo/getEmpList',
    method: 'get',
    params: query
  })
}

// 获取科室设备信息
export function getDeviceInfo(query) {
  return request({
    url: '/portal/deptInfo/getDeviceInfo',
    method: 'get',
    params: query
  })
}

// 获取科室hrp设备列表
export function getDeviceList(query) {
  return request({
    url: '/portal/deptInfo/getDeviceList',
    method: 'get',
    params: query
  })
}

// 获取科室巡检信息
export function getInspectInfo(query) {
  return request({
    url: '/portal/deptInfo/getInspectInfo',
    method: 'get',
    params: query
  })
}

// 获取科室巡检列表
export function getInspectList(query) {
  return request({
    url: '/portal/deptInfo/getInspectList',
    method: 'get',
    params: query
  })
}

// 获取科室报修信息
export function getRepairInfo(query) {
  return request({
    url: '/portal/deptInfo/getRepairInfo',
    method: 'get',
    params: query
  })
}

// 获取科室报修列表
export function getRepairList(query) {
  return request({
    url: '/portal/deptInfo/getRepairList',
    method: 'get',
    params: query
  })
}

// 获取科室水电能耗信息
export function getEnergyInfo(query) {
  return request({
    url: '/portal/deptInfo/getEnergyInfo',
    method: 'get',
    params: query
  })
}

