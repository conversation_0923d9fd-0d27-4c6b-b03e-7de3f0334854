import upload from '@/utils/upload'
import request from '@/utils/request'

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return upload({
    url: '/system/user/profile/avatar',
    name: data.name,
    filePath: data.filePath
  })
}
// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 上传签名
export function uploadMinio(data){
  return upload({
    url:"/common/uploadMinio",
    name: data.name,
    filePath: data.filePath,
    fileType: data.fileType || 'image'
  })
}

// 上传图片
export function uploadImage(data){
	return upload({
	url:"/common/uploadMinio",
    name: data.name,
    filePath: data.filePath,
    fileType: data.fileType || 'image'
	})
}

// 上传音频
export function uploadAudio(data){
  return upload({
    url:"/common/uploadMinio",
    name: data.name,
    filePath: data.filePath,
    fileType: data.fileType || 'audio'
  })
}

// 上传视频
export function uploadVideo(data){
  return upload({
    url:"/common/uploadMinio",
    name: data.name,
    filePath: data.filePath,
    fileType: data.fileType || 'video'
  })
}

// 上传用户签名
export function updateEsign(data) {
  return request({
    url: '/system/user/profile/updateEsign',
    method: 'post',
    data: data
  })
}
// 根据用户ID查询用户签名信息
export function getUserEsignUrl(userId) {
  return request({
    url: '/system/user/getUserEsignUrl/' + userId,
    method: 'get'
  })
}