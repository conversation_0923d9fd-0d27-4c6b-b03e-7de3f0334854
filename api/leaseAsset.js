import request from "../utils/request";
const prefix = '/api/v_1_0/lease_asset'
export function getLeaseAssetList(data) {
  return request({
    url: `${prefix}/query`,
    method: 'post',
    data
  })
}

export function addLeaseAsset(data) {
  return request({
    url: `${prefix}/add`,
    method: 'post',
    data
  })
}

export function updateLeaseAsset(data) {
  return request({
    url: `${prefix}/update`,
    method: 'post',
    data
  })
}

export function deleteLeaseAsset(id) {
  return request({
    url: `${prefix}/delete`,
    method: 'post',
    params: {
      id: id
    }
  })
}