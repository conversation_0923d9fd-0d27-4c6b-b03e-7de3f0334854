import { createWebSocket } from '@/utils/websocket'

// 小程序端用所有的消息业务类型（消息 告警 辅助决策）有新消息提醒
export function getAllMessageWs(query) {
  return createWebSocket({
    url: '/ws/auth/allmessageservicetype4app/messageSend',
    params: query
  })
}
// 小程序端测试用所有的消息业务类型（消息 告警 辅助决策）有新消息提醒
export function getAllMessageWb(query) {
  return createWebSocket({
    url: '/ws/allmessageservicetype/messageSend',
    params: query
  })
}