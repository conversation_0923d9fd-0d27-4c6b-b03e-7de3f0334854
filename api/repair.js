import request from "../utils/request";
const prefix = '/repair'

export function listHrpRepairRecord(query) {
  return request({
    url:prefix+'/record/repairRecord/hrpListByUser',
    method: 'get',
    params: query
  })
}

export function addHrpRepairRecord(data) {
  return request({
    url:prefix+'/record/repairRecord/addHrp',
    method: 'post',
    data: data
  })
}


export function getDeptNameAndAddress() {
  return request({
    url:'/repair/record/repairRecord/queryuserdept',
    method: 'get',
    
  })
}
// 查询已办任务列表
export function finishedList(query) {
  return request({
    url: '/flowable/task/finishedList',
    method: 'get',
    params: query
  })
}
// 我的发起的流程
export function myProcessList(query) {
  return request({
    url: '/flowable/task/myProcess',
    method: 'get',
    params: query
  })
}
// 新增其他类型的维修记录
export function addOtherRepairRecord(data) {
  return request({
    url: '/repair/record/repairRecord/addOther',
    method: 'post',
    data: data
  })
}
export function getCategory2Options(query) {
  return request({
    url:'/commservice/comDeviceOtherClass/listbyParent/'+query,
    method: 'get',
    
  })
}
// 查询耗材类型列表
export function listRepairMaterialTypes(query) {
  return request({
    url:prefix+'/materialTypes/repairMaterialTypes/list',
    method: 'get',
    params: query
  })
}
// 新增耗材类型
export function addRepairMaterialTypes(data) {
  return request({
    url:prefix+'/materialTypes/repairMaterialTypes',
    method: 'post',
    data: data
  })
}
// 查询耗材列表
export function listRepairMaterials(query) {
  return request({
    url:prefix+'/materials/repairMaterials/list',
    method: 'get',
    params: query
  })
}
// 新增耗材
export function addRepairMaterials(data) {
    return request({
        url:prefix+'/materials/repairMaterials',
        method: 'post',
        data: data
    })
}
// 查询所有维修记录列表
export function listRepairRecord(query) {
  return request({
    url: '/repair/record/repairRecord/list',
    method: 'get',
    params: query
  })
}

// 1.获取水电工工程师列表
export function getWaterElectricENG(query) {
  return request({
    url: '/repair/bizDeptStaffRole/getWaterElectricENG',
    method: 'get',
    params: query
  })
}
// 2.获取IT工程师列表
export function getITENG(query) {
  return request({
    url: '/repair/bizDeptStaffRole/getITENG',
    method: 'get',
    params: query
  })
}
// 3.获取设备科工程师列表
export function getDeviceDeptENG(query) {
  return request({
    url: '/repair/bizDeptStaffRole/getDeviceDeptENG',
    method: 'get',
    params: query
  })
}
// 4.获取保卫科保卫人员列表
export function getSecurityStaff(query) {
  return request({
    url: '/repair/bizDeptStaffRole/getSecurityStaff',
    method: 'get',
    params: query
  })
}
// 5.获取总务科木工列表
export function getCarpenter(query) {
  return request({
    url: '/repair/bizDeptStaffRole/getCarpenter',
    method: 'get',
    params: query
  })
}
// 6.获取总务科电梯氧气维护人员列表
export function getElevatorO2Staff(query) {
  return request({
    url: '/repair/bizDeptStaffRole/getElevatorO2Staff',
    method: 'get',
    params: query
  })
}
// 7.获取信息科大华工程师列表
export function getDahuaENG(query) {
  return request({
    url: '/repair/bizDeptStaffRole/getDahuaENG',
    method: 'get',
    params: query
  })
}

// 1.新增新的其它设备报修记录(水电家具设备)
export function addNewOther(data) {
  return request({
    url: '/repair/record/repairRecord/addNewOther',
    method: 'post',
    data: data
  })
}