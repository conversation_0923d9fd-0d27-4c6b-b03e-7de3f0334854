import request from "../utils/request";

//获取部门树列表
export function getDeptTree() {
	return request({
		url: '/ds/energy/dahuaOrganizationLink/deptTree',
		method: 'get'
	})
}

// 获取部门下的能耗设备
export function getDahuaOrganizationLinkById(data) {
	return request({
		url: '/ds/energy/dahuaApi/electricNow?deptId=' + data.deptId + '&pageSize=' + data.pageSize +
			'&pageNumber=' + data.pageNum,
		method: 'get'
	})
}
// 大华电历史数据获取
export function getElectricHistory(depId, dateTime) {
	return request({
		url: `/ds/energy/dahuaApi/electricHistory?depId=${depId}&dateTime=${dateTime}`,
		method: 'get',
	})
}

// 报警数据获取
export function electricWarn() {
	return request({
		url: '/ds/energy/dahuaApi/electricWarn',
		method: 'get'

	})
}

export function getEnergyRankData(query) {
  return request({
    url: '/ds/energy/rank/electricityRank/total',
    method: 'get',
    params: query
  })
}
//报警数据列表获取
export function electricWarnList(params) {
	return request({
		url: '/ds/energy/dahuaApi/electricWarnList',
		method: 'post',
		data: params
	})
}

//报警处理接口
export function electricWarnHandle(data) {
	return request({
		url: '/ds/energy/dahuaApi/electricWarnHandle',
		method: 'post',
		data: data
	})
}

// 大华电数据实时获取
export function dahuaElectricityHour({
	deptId,
	dateType,
	dateTime
}) {
	return request({
		url: `/ds/energy/dahuaElectricityHour/dataList?deptId=${deptId}&dateType=${dateType}&dateTime=${dateTime}`,
		method: 'get'
	})
}