import request from "../../utils/request";

// 1.1查询个人（维修人）登记的Hrp耗材列表
export function getPerHrpmat(query) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/search',
        method: 'get',
        params: query
    })
}
// 1.2导出个人（维修人）登记的Hrp耗材列表
export function addPerHrpmat(data) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/export4search',
        method: 'post',
        data: data
    })
}
// 2.1(暂时不用）查询普通科室登记的Hrp耗材列表
export function getUsDeptHrpmat(query) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/search',
        method: 'get',
        params: query
    })
}
// 2.2(暂时不用）导出普通科室登记的Hrp耗材列表
export function addUsuDeptHrpmat(data) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/export4search',
        method: 'post',
        data: data
    })
}
// 3.1查询业务科室登记的Hrp耗材列表
export function getBusDeptHrpmat(query) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/search',
        method: 'get',
        params: query
    })
}
// 3.2导出普通科室登记的Hrp耗材列表
export function addBusDeptHrpmat(data) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/export4search',
        method: 'post',
        data: data
    })
}
//4.1更新选择的Hrp耗材为下载标记
export function addUpdateDowloadFlag(data) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/updateDowloadFlag',
        method: 'post',
        data: data
    })
}
//4.2更新选择的Hrp耗材为未下载标记
export function addUpdateUnDowloadFlag(data) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/updateUnDowloadFlag',
        method: 'post',
        data: data
    })
}
// 5.1 导出选择的Hrp耗材
export function addExportSelectHrpmat(data) {
    return request({
        url: '/repair/hrpmatRecords/repairHrpmatRecords/export4Select',
        method: 'post',
        data: data
    })
}