import request from "../utils/request";

const prefix = '/api/v_1_0/work_order'

export function workOrderQuery(data) {
	return request({
		url: prefix + '/query',
		method: 'post',
		data: data
	})
}
// 处理工单流转
export function workOrderUpdate(data) {
	return request({
		url: prefix + '/update',
		method: 'post',
		data: data
	})
}
// 获取单个工单详情
export function getworkOrderInfo(id) {
	return request({
		url: prefix + '/get?id='+id,
		method: 'get',
	})
}