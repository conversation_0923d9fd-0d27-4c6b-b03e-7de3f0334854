import request from "../../utils/request";
const prefix = '/api/v_1_0/asset_info'

// 查询待办任务列表
export function todoList(query) {
  return request({
    url: '/flowable/task/todoList',
    method: 'get',
    params: query
  })
}

// 完成任务
export function complete(data) {
  return request({
    url: '/flowable/task/complete',
    method: 'post',
    data: data
  })
}

// 委派任务
export function delegate(data) {
  return request({
    url: '/flowable/task/delegate',
    method: 'post',
    data: data
  })
}

// 退回任务
export function returnTask(data) {
  return request({
    url: '/flowable/task/return',
    method: 'post',
    data: data
  })
}

// 驳回任务
export function rejectTask(data) {
  return request({
    url: '/flowable/task/reject',
    method: 'post',
    data: data
  })
}

// 可退回任务列表
export function returnList(data) {
  return request({
    url: '/flowable/task/returnList',
    method: 'post',
    data: data
  })
}

// 下一节点
export function getNextFlowNode(data) {
  return request({
    url: '/flowable/task/nextFlowNode',
    method: 'post',
    data: data
  })
}

// 下一节点
export function getNextFlowNodeByStart(data) {
  return request({
    url: '/flowable/task/nextFlowNodeByStart',
    method: 'post',
    data: data
  })
}

// 部署流程实例
export function deployStart(deployId) {
  return request({
    url: '/flowable/process/startFlow/' + deployId,
    method: 'get',
  })
}

// 查询流程定义详细
export function getDeployment(id) {
  return request({
    url: '/system/deployment/' + id,
    method: 'get'
  })
}

// 新增流程定义
export function addDeployment(data) {
  return request({
    url: '/system/deployment',
    method: 'post',
    data: data
  })
}

// 修改流程定义
export function updateDeployment(data) {
  return request({
    url: '/system/deployment',
    method: 'put',
    data: data
  })
}

// 删除流程定义
export function delDeployment(id) {
  return request({
    url: '/system/deployment/' + id,
    method: 'delete'
  })
}

// 导出流程定义
export function exportDeployment(query) {
  return request({
    url: '/system/deployment/export',
    method: 'get',
    params: query
  })
}
// 流程节点表单
export function flowTaskForm(query) {
  return request({
    url: '/flowable/task/flowTaskForm',
    method: 'get',
    params: query
  })
}
// 任务流转记录
// export function flowRecord(query) {
//   return request({
//     url: '/flowable/task/flowRecord',
//     method: 'get',
//     params: query,
//   })
// }
// // 获取流程变量
// export function getProcessVariables(taskId) {
//   return request({
//     url: '/flowable/task/processVariables/' + taskId,
//     method: 'get',
//   })
// }
// // 流程节点数据
// export function flowXmlAndNode(query) {
//   return request({
//     url: '/flowable/task/flowXmlAndNode',
//     method: 'get',
//     params: query
//   })
// }
// // 获取流程执行节点
// export function getFlowViewer(procInsId, executionId) {
//   return request({
//     url: '/flowable/task/flowViewer/' + procInsId + '/' + executionId,
//     method: 'get'
//   })
// }