import request from "../../utils/request";
// 查询已办任务列表
export function finishedList(query) {
  return request({
    url: '/flowable/task/finishedList',
    method: 'get',
    params: query
  })
}

// 任务流转记录
export function flowRecord(query, token) {
  let headers = {}
  if (token) { // 判断是否手动传入token
    // 获取小程序端传过来的token,兼容动态表单
    headers = {
      isToken: false,
      repeatSubmit: false,
      Authorization: 'Bearer ' + token
    }
  }
  return request({
    url: '/flowable/task/flowRecord',
    method: 'get',
    params: query,
    headers: headers
  })
}

// 撤回任务
export function revokeProcess(data) {
  return request({
    url: '/flowable/task/revokeProcess',
    method: 'post',
    data: data
  })
}

// 部署流程实例
export function deployStart(deployId) {
  return request({
    url: '/flowable/process/startFlow/' + deployId,
    method: 'get',
  })
}

// 查询流程定义详细
export function getDeployment(id) {
  return request({
    url: '/system/deployment/' + id,
    method: 'get'
  })
}

// 新增流程定义
export function addDeployment(data) {
  return request({
    url: '/system/deployment',
    method: 'post',
    data: data
  })
}

// 修改流程定义
export function updateDeployment(data) {
  return request({
    url: '/system/deployment',
    method: 'put',
    data: data
  })
}

// 删除流程定义
export function delDeployment(id) {
  return request({
    url: '/flowable/instance/delete/' + id,
    method: 'delete'
  })
}

// 导出流程定义
export function exportDeployment(query) {
  return request({
    url: '/system/deployment/export',
    method: 'get',
    params: query
  })
}
// 根据流程实例编号生成旋转90度Base64编码流程图
export function getDiagram4RotateBase64(processId) {
  return request({
    url: '/flowable/task/diagram4RotateBase64/' + processId,
    method: 'get'
  })
}
// 根据流程实例编号生成Base64编码流程图
export function getDiagram4Base64(processId) {
  return request({
    url: '/flowable/task/diagram4Base64/' + processId,
    method: 'get'
  })
}