import request from '@/utils/request'
// import { tansParams } from "@/utils/ruoyi";

// 创建通用的 form 请求配置
// const formRequestConfig = {
//   header: {
//     'Content-Type': 'application/x-www-form-urlencoded'
//   },
//   transformRequest: [(params) => { return tansParams(params) }]
// }

// 查询设备用途列表
export function qryDeviceUse(data) {
  return request({
    url: '/ds/weg/qryDeviceUse',
    method: 'post',
    data: data
  })
}

// 查询设备列表
export function qryWegDevice(data) {
  return request({
    url: '/ds/weg/qryWegDevice',
    method: 'post',
    data: data
  })
}

// 历史水电气数据查询
export function wegHisQry(data) {
  return request({
    url: '/ds/weg/wegHisQry',
    method: 'post',
    data: data
  })
}

// 查询水电气实时数据
export function qryRealData(data) {
  return request({
    url: '/ds/weg/qryRealData',
    method: 'post',
    data: data
  })
}

// 水电气汇总统计查询
export function qryStatisticsByType(data) {
  return request({
    url: '/ds/weg/qryStatisticsByType',
    method: 'post',
    data: data
  })
}

// 水电气排名查询
export function qryRankList(data) {
  return request({
    url: '/ds/weg/qryRankList',
    method: 'post',
    data: data
  })
}

