import request from '@/utils/request'

// 1.查询Hrp耗材仓库列表
export function listWareHouse(query) {
    return request({
        url: '/ds/hrpmat/product/listWareHouse',
        method: 'get',
        params: query
    })
}

// 2.获取Hrp耗材分类树结构
export function setHrpMatTree(query) {
    return request({
        url: '/ds/hrpmat/sort/deptTree',
        method: 'get',
        params: query
    })
}

// 3.查询Hrp耗材产品
export function listHrpMat(query) {
    return request({
        url: '/ds/hrpmat/product/list',
        method: 'get',
        params: query
    })
}