import request from '@/utils/request'

// 查询hrp资产列表
export function listCard(query) {
    return request({
        url: '/ds/hrp/card/list',
        method: 'get',
        params: query
    })
}

// 查询hrp资产详细
export function getCard(cardNumber) {
    return request({
        url: '/ds/hrp/card/' + cardNumber,
        method: 'get'
    })
}

// 新增hrp资产
export function addCard(data) {
    return request({
        url: '/ds/hrp/card',
        method: 'post',
        data: data
    })
}

// 修改hrp资产
export function updateCard(data) {
    return request({
        url: '/ds/hrp/card',
        method: 'put',
        data: data
    })
}

// 删除hrp资产
export function delCard(cardNumber) {
    return request({
        url: '/ds/hrp/card/' + cardNumber,
        method: 'delete'
    })
}

export function updateHrpCardClass3(data) {
    return request({
        url: '/ds/hrp/card/updateHrpCardClass3',
        method: 'post',
        data: data
    })
}
