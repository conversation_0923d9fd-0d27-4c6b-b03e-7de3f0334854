import request from "../utils/request";
const prefix = '/api/v_1_0/business_oppo'

export function getList(data) {
	return request({
		url: prefix + '/query',
		method: 'post',
		data: data
	})
}
// update
export function update(data) {
	return request({
		url: prefix + '/update',
		method: 'post',
		data: data
	})
}
// add
export function addOpportunity(data) {
	return request({
		url: prefix + '/add',
		method: 'post',
		data: data
	})
}

// business_oppo/name
export function getOneOpportunityInfo(data) {
	// 根据名称来模糊查询
	return request({
		url: prefix + '/name',
		method: 'post',
		data: data
	})
}