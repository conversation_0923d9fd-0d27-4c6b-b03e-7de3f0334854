import request from '@/utils/request'

// 查询消息中心-发送列表
export function listSend(query) {
  return request({
    url: '/messagecenter/send/list',
    method: 'get',
    params: query
  })
}

// 查询消息中心-发送详细
export function getSend(id) {
  return request({
    url: '/messagecenter/send/' + id,
    method: 'get'
  })
}

// 新增消息中心-发送
export function addSend(data) {
  return request({
    url: '/messagecenter/send',
    method: 'post',
    data: data
  })
}

// 修改消息中心-发送
export function updateSend(data) {
  return request({
    url: '/messagecenter/send',
    method: 'put',
    data: data
  })
}

// 删除消息中心-发送
export function delSend(id) {
  return request({
    url: '/messagecenter/send/' + id,
    method: 'delete'
  })
}
