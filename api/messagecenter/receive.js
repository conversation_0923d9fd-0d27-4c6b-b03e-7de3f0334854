import request from '@/utils/request'

// 查询消息中心-接收列表
export function listReceive(query) {
  return request({
    url: '/messagecenter/receive/list',
    method: 'get',
    params: query
  })
}

// 查询消息中心-接收详细
export function getReceive(id) {
  return request({
    url: '/messagecenter/receive/' + id,
    method: 'get'
  })
}

// 新增消息中心-接收
export function addReceive(data) {
  return request({
    url: '/messagecenter/receive',
    method: 'post',
    data: data
  })
}

// 修改消息中心-接收
export function updateReceive(data) {
  return request({
    url: '/messagecenter/receive',
    method: 'put',
    data: data
  })
}

// 删除消息中心-接收
export function delReceive(id) {
  return request({
    url: '/messagecenter/receive/' + id,
    method: 'delete'
  })
}
