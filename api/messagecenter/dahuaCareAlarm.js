import request from '@/utils/request'

// 查询大华告警关注列表
export function listDahuaCareAlarm(query) {
  return request({
    url: '/messagecenter/dahuaCareAlarm/list',
    method: 'get',
    params: query
  })
}

// 查询大华告警关注详细
export function getDahuaCareAlarm(id) {
  return request({
    url: '/messagecenter/dahuaCareAlarm/' + id,
    method: 'get'
  })
}

// 新增大华告警关注
export function addDahuaCareAlarm(data) {
  return request({
    url: '/messagecenter/dahuaCareAlarm',
    method: 'post',
    data: data
  })
}

// 修改大华告警关注
export function updateDahuaCareAlarm(data) {
  return request({
    url: '/messagecenter/dahuaCareAlarm',
    method: 'put',
    data: data
  })
}

// 删除大华告警关注
export function delDahuaCareAlarm(id) {
  return request({
    url: '/messagecenter/dahuaCareAlarm/' + id,
    method: 'delete'
  })
}
