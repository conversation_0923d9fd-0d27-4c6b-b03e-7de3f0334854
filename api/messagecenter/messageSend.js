import request from '@/utils/request'

// 查询已发消息存储记录列表
export function listMessageSend(query) {
  return request({
    url: '/messagecenter/messageSend/list',
    method: 'get',
    params: query
  })
}

// 查询已发消息存储记录详细
export function getMessageSend(id) {
  return request({
    url: '/messagecenter/messageSend/' + id,
    method: 'get'
  })
}

// 新增已发消息存储记录
export function addMessageSend(data) {
  return request({
    url: '/messagecenter/messageSend',
    method: 'post',
    data: data
  })
}

// 修改已发消息存储记录
export function updateMessageSend(data) {
  return request({
    url: '/messagecenter/messageSend',
    method: 'put',
    data: data
  })
}

// 删除已发消息存储记录
export function delMessageSend(id) {
  return request({
    url: '/messagecenter/messageSend/' + id,
    method: 'delete'
  })
}
