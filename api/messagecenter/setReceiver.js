import request from '@/utils/request'

// 查询消息接收人员设置列表
export function listSetReceiver(query) {
  return request({
    url: '/messagecenter/setReceiver/list',
    method: 'get',
    params: query
  })
}

// 查询消息接收人员设置详细
export function getSetReceiver(id) {
  return request({
    url: '/messagecenter/setReceiver/' + id,
    method: 'get'
  })
}

// 新增消息接收人员设置
export function addSetReceiver(data) {
  return request({
    url: '/messagecenter/setReceiver',
    method: 'post',
    data: data
  })
}

// 修改消息接收人员设置
export function updateSetReceiver(data) {
  return request({
    url: '/messagecenter/setReceiver',
    method: 'put',
    data: data
  })
}

// 删除消息接收人员设置
export function delSetReceiver(id) {
  return request({
    url: '/messagecenter/setReceiver/' + id,
    method: 'delete'
  })
}
