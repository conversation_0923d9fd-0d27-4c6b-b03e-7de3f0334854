import request from '@/utils/request'

// 查询消息业务类型列表
export function listServiceType(query) {
  return request({
    url: '/messagecenter/serviceType/list',
    method: 'get',
    params: query
  })
}

// 查询消息业务类型列表---根据消息告警标识和消息业务类型名称查询
/* query
 * messageServiceName 消息告警标识
 * msgAlarmMark 消息告警标识
 * pageSize 每页条数
 * pageNum 当前页码
 */
export function listbymark(query) {
  return request({
    url: '/messagecenter/serviceType/listbymark',
    method: 'get',
    params: query
  })
}
// 查询消息业务类型详细
export function getServiceType(messageServiceType) {
  return request({
    url: '/messagecenter/serviceType/' + messageServiceType,
    method: 'get'
  })
}

// 新增消息业务类型
export function addServiceType(data) {
  return request({
    url: '/messagecenter/serviceType',
    method: 'post',
    data: data
  })
}

// 修改消息业务类型
export function updateServiceType(data) {
  return request({
    url: '/messagecenter/serviceType',
    method: 'put',
    data: data
  })
}

// 删除消息业务类型
export function delServiceType(messageServiceType) {
  return request({
    url: '/messagecenter/serviceType/' + messageServiceType,
    method: 'delete'
  })
}
