import request from "../utils/request";


export function listInspectRecord(query) {
	return request({
		url: '/inspect/inspectPlan/xcxList',
		method: 'get',
		params: query
	})
}

export function listOverTimeInspectRecord(query) {
	return request({
		url: '/inspect/inspectPlan/xcxRecordList',
		method: 'get',
		params: query
	})
}
export function InspectedPlan(query) {
	return request({
		url: '/inspect/inspectPlan',
		method: 'put',
		data: query
	})
}

export function listInspectLocation(query) {
	return request({
		url: '/inspect/inspectRoute/placeList',
		method: 'get',
		params: query
	})
}

// 新增其他类型的维修记录
export function addInspectRecord(data) {
	return request({
		url: '/inspect/inspectPlaceRecord/xcxAddTwo',
		method: 'post',
		data: data
	})
}

// 获取巡检地点记录信息带图片音频和视频
export function getInspectRecord(query) {
	return request({
		url: '/inspect/inspectPlaceRecord/getInfoTwo',
		method: 'get',
		params: query
	})
}

// 新增其他类型的维修记录
export function alterInspectRecord(data) {
	return request({
		url: '/inspect/inspectPlaceRecord/xcxUpdateTwo',
		method: 'post',
		data: data
	})
}

// 获取大华视频地址
export function getVideoUrl(data) {
	return request({
		url: '/inspect/inspectApi/getVideoUrl',
		method: 'get',
		data: data
	})
}
// 获取大华视频图片
export function getVideoImage(query) {
	return request({
		url: '/inspect/inspectApi/getVideoImage',
		method: 'get',
		params: query
	})
}
//根据巡检类型id获取巡检类型对应的巡检表单
export function getFormByInspectId(query) {
	return request({
		url: '/inspect/inspectType/getFormByInspectId',
		method: 'get',
		params: query
	})
}

//根据扫码的设备编码获取巡检计划
export function getInspectPlanByQRecode(query) {
	return request({
		url:'/inspect/inspectPlan/qrCode',
		method: 'get',
		params: query
	})
}

//批量新增巡检地点记录（用于扫码巡检）
export function addInspectPlanScan(data) {
	return request({
		url:'/inspect/inspectPlaceRecord/addBatch',
		method: 'post',
		data: data,

	})
}

// 获取根据当前的用户获取当前用户的巡检用户信息
export function getInspectUser(inspectPlanId) {
	return request({
		url: `/inspect/inspectUser/getInspectUser4LoginUser/${inspectPlanId}`,  // 使用路径参数传递
		method: 'get'
	});
}