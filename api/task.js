import request from "../utils/request";
// inspection_info
const prefix = '/api/v_1_0/inspection_info'
// 资产盘点 
const prefix1 = '/api/v_1_0/asset_inspection'
export function getInspectionList(data) {
	return request({
		url: prefix + '/query',
		method: 'post',
		data: data
	})
}
export function getAssetInspection(id) {
	return request({
		url:prefix1+ '/query?inspectionId=' + id,
		method: 'get'
	})
}
// /asset_inspection/deal
export function assetInspectionDeal(data) {
	return request({
		url:prefix1+ '/deal',
		method: 'post',
		data:data
	})
}
// /work_order/add 提交工单
export function workOrderAdd(data) {
	return request({
		url:'/api/v_1_0/work_order/add',
		method: 'post',
		data:data
	})
}
// 获取快速巡查列表
export function getAssetSuperInspectionList(data) {
	return request({
		url: prefix + '/super_query',
		method: 'post',
		data: data
	})
}
// 获取快速巡检列表盘点列表
export function getAssetSuperInspectionInfo(params) {
  return request({
    url: `${prefix1}/super_query`,
    method: 'get',
    params
  })
}