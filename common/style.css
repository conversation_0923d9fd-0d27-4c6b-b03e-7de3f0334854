.p20 {
	padding: 20rpx;
}

.b20 {
	border-radius: 20rpx;
}

.bf {
	background-color: #fff;
}

.nav_fixed {
	position: fixed;
	width: 100%;
	top: 0;
	z-index: 1024;
	box-shadow: 0 1upx 6upx rgba(0, 0, 0, 0.1);
}

.btn_big {
	padding: 20rpx;
	text-align: center;
	background-color: #5b73f9;
	color: #fff;
	border-radius: 10rpx;
}

.flex {
	display: flex;
}

.fl {
	flex-direction: column;
}

.js {
	display: flex;
	justify-content: flex-start;
}

.just-sbet {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.card {
	box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
	padding: 30rpx;
	border-radius: 10rpx;
}

.justify-center {
	justify-content: center;
}

.align-center {
	align-items: center;
}

.as {
	display: flex;
	align-items: flex-start;
}

.center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.as {
	display: flex;
	align-items: flex-start;
}

.grid {
	display: grid
}

.gap {
	display: grid;
	gap: 20rpx;
}

.grid-2 {
	display: grid;
	gap: 20rpx;
	grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
	display: grid;
	gap: 20rpx;
	grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
	display: grid;
	gap: 20rpx;
	grid-template-columns: repeat(4, 1fr);
}

.w-full {
	width: 100%;
}

.h-full {
	height: 100%;
}