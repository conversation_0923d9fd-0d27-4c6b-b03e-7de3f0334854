export function show(tips, code = 200) {
	// 提示
	if (code == 200) {
		uni.showToast({
			title: tips
		})
	} else {
		uni.showToast({
			icon: 'none',
			title: tips
		})
	}
}
export function topage(url, info = {}) {
	if(url==''){
		uni.showToast({
			title:'功能开发中',
			icon:'none'
		})
	}
	// 路由跳转
	uni.navigateTo({
		url: url + '?info=' + JSON.stringify(info)
	})
}

export function getAssetNum(id) {
	if (id) {
		return 'JAZC800' + prefixInteger(id, 6)
	} else {
		return '未知资产编号'
	}

}

export function prefixInteger(num, n) {
	return (Array(n).join(0) + num).slice(-n);
}