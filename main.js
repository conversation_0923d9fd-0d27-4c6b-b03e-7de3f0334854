import Vue from 'vue'
import App from './App'
import store from './store' // store
import plugins from './plugins' // plugins
import './permission' // permission
// 引入elementUI开发
// import ElementUI from 'element-ui';
//import 'element-ui/lib/theme-chalk/index.css';
import TreeItem from './components/tree-item.vue'
import * as echarts from 'echarts';
// 导入 config.js 中的 baseUrl
import config from './config.js'
import {CommUtil} from "./utils/commUtil"
// 定义全局函数
//任何 Vue 组件中使用 this.$replaceUrlDomain(url) 来调用这个函数
//当前访问的为内网地址，且当前资源地址不是内网地址，则替换为内网地址
//当前访问的为外网地址，且当前资源地址内网地址，则替换为外网地址
Vue.prototype.$replaceUrlDomain = CommUtil.replaceUrlDomain;
Vue.prototype.$isInternalNetwork = CommUtil.isInternalNetwork;

// 引入全局 uview 框架
import uView from 'uview-ui';
Vue.use(uView);

Vue.use(plugins)
//Vue.use(ElementUI);
Vue.config.productionTip = false
Vue.prototype.$store = store
Vue.component('TreeItem', TreeItem)
Vue.prototype.$echarts = echarts;



App.mpType = 'app'

const app = new Vue({
	...App
})

app.$mount()