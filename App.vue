<script>
  import config from './config'
  import store from '@/store'
  import { getToken } from '@/utils/auth'
  import loginCheckConfig, { getCurrentConfig, getPlatformInfo } from '@/utils/loginCheckConfig'

  export default {
    data() {
      return {
        // 记录应用状态
        isFirstLaunch: true,
        lastHideTime: 0,
        // 动态加载的配置
        loginCheckConfig: getCurrentConfig(),
        // 平台信息
        platformInfo: getPlatformInfo()
      }
    },

    onLaunch: function() {
      this.debugLog('🚀 应用启动', `平台: ${this.platformInfo.platformName}`)
      this.initApp()
    },

    onShow: function() {
      this.debugLog('👀 应用进入前台')
      this.handleAppShow()
    },

    onHide: function() {
      this.debugLog('🙈 应用进入后台')
      this.lastHideTime = Date.now()
      // 持久化存储，防止应用被回收
      uni.setStorageSync('APP_HIDE_TIME', this.lastHideTime)
    },

    methods: {
      // 初始化应用
      initApp() {
        this.initConfig()
        this.checkLogin('应用启动')
        this.isFirstLaunch = false
      },

      // 处理应用显示
      handleAppShow() {
        // 首次启动不需要重复检查
        if (this.isFirstLaunch) {
          return
        }

        if (!this.loginCheckConfig.basic.enableOnShowCheck) {
          return
        }

        // 计算后台时长
        const hideTime = this.lastHideTime || uni.getStorageSync('APP_HIDE_TIME') || 0
        const backgroundDuration = Date.now() - hideTime
        const minutes = Math.round(backgroundDuration / 1000 / 60)
        const seconds = Math.round(backgroundDuration / 1000)

        if (backgroundDuration > this.loginCheckConfig.timing.recheckThreshold) {
          this.debugLog(`⏰ 后台时间过长 (${minutes}分钟)，重新检查登录`)
          this.checkLogin('长时间后台后重新进入', true)
        } else {
          this.debugLog(`✅ 后台时间较短 (${seconds}秒)，快速检查登录`)
          this.checkLogin('短时间后台后重新进入')
        }
      },

      initConfig() {
        this.globalData.config = config
      },

      // 友好的登录检查
      checkLogin(source = '未知来源', showLoading = false) {
        this.debugLog(`🔐 登录检查 - 来源: ${source}`)

        const token = getToken()
        if (!token) {
          this.debugLog('❌ 未找到登录凭证')
          this.handleNoToken(source)
          return
        }

        // 检查Token格式是否正确
        if (!this.isValidTokenFormat(token)) {
          this.debugLog('❌ 登录凭证格式异常')
          this.handleInvalidToken(source)
          return
        }

        this.debugLog('✅ 登录凭证检查通过')

        // 对于长时间后台的情况，可以选择验证Token有效性
        if (showLoading && source.includes('长时间后台')) {
          this.validateTokenWithServer(source)
        }
      },

      // 处理无Token情况
      handleNoToken(source) {
        if (this.loginCheckConfig.basic.showFriendlyTips) {
          // 根据不同场景显示不同提示
          let message = this.loginCheckConfig.messages.generalNoToken
          if (source.includes('后台')) {
            message = this.loginCheckConfig.messages.backgroundNoToken
          } else if (source.includes('启动')) {
            message = this.loginCheckConfig.messages.launchNoToken
          }

          // 显示友好提示
          uni.showToast({
            title: message,
            icon: 'none',
            duration: this.loginCheckConfig.timing.toastDuration
          })

          // 延迟跳转，让用户看到提示
          setTimeout(() => {
            this.$tab.reLaunch('/pages/login')
          }, this.loginCheckConfig.timing.redirectDelay)
        } else {
          // 直接跳转
          this.$tab.reLaunch('/pages/login')
        }
      },

      // 处理无效Token情况
      handleInvalidToken(source) {
        this.debugLog('🧹 清理无效的登录凭证')
        // 清理无效Token
        this.$store.dispatch('LogOut').then(() => {
          this.handleNoToken(source)
        }).catch(() => {
          // 即使清理失败也要跳转登录页
          this.handleNoToken(source)
        })
      },

      // 验证Token格式
      isValidTokenFormat(token) {
        if (!token || typeof token !== 'string') {
          return false
        }

        // 基本长度检查
        if (token.length < 10) {
          return false
        }

        // 如果是JWT格式，检查结构
        if (token.includes('.')) {
          const parts = token.split('.')
          if (parts.length !== 3) {
            return false
          }
        }

        return true
      },

      // 与服务器验证Token有效性
      async validateTokenWithServer(source) {
        if (!this.loginCheckConfig.basic.showFriendlyTips) {
          return
        }

        this.debugLog('🌐 验证登录状态...')

        // 显示验证中提示
        uni.showLoading({
          title: this.loginCheckConfig.messages.validating,
          mask: true
        })

        try {
          // 调用获取用户信息接口验证Token
          await this.$store.dispatch('GetInfo')
          this.debugLog('✅ 服务器验证通过')

          uni.hideLoading()
          uni.showToast({
            title: this.loginCheckConfig.messages.validSuccess,
            icon: 'success',
            duration: 1500
          })

        } catch (error) {
          this.debugLog('❌ 服务器验证失败:', error)
          uni.hideLoading()

          // 显示友好的错误提示
          uni.showModal({
            title: this.loginCheckConfig.messages.validFailed,
            content: this.loginCheckConfig.messages.needRelogin,
            showCancel: false,
            confirmText: '重新登录',
            success: () => {
              this.$store.dispatch('LogOut').then(() => {
                this.$tab.reLaunch('/pages/login')
              })
            }
          })
        }
      },

      // 调试日志输出
      debugLog(message, extra = '') {
        if (!this.loginCheckConfig.basic.enableDebugLog) {
          return
        }

        const timestamp = new Date().toLocaleTimeString()
        const platform = this.platformInfo.platformName
        const logMessage = `[${timestamp}] [${platform}] ${message} ${extra}`

        console.log(logMessage)
      }
    }
  }
</script>

<style lang="scss">
  @import 'uview-ui/index.scss';
  @import '@/static/scss/index.scss';
	@import url('@/static/icon/iconfont.css');
	@import url('common/style.css');

</style>
