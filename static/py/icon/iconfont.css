@font-face {
  font-family: "iconfont"; /* Project id 4816510 */
  src: url('/static/py/icon/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.py-icon-flow-completed:before {
  content: "\e62f";
}

.py-icon-flow-todo:before {
  content: "\e602";
}

.py-icon-flow-candidate:before {
  content: "\ea0a";
}

.py-icon-flow-time:before {
  content: "\e606";
}

.py-icon-flow-department:before {
  content: "\e7d8";
}

.py-icon-flow-calendar:before {
  content: "\e61d";
}

.py-icon-flow-opinion:before {
  content: "\e63f";
}

.py-icon-flow-user:before {
  content: "\e6c3";
}

.py-icon-transfer:before {
  content: "\e69b";
}

.py-icon-circleo:before {
  content: "\e68d";
}

.py-icon-user:before {
  content: "\e7ae";
}

.py-icon-circle:before {
  content: "\e600";
}

.py-icon-exclusive-gateway:before {
  content: "\ea5a";
}

