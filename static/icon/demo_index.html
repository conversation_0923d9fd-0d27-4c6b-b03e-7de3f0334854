<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4263516" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">物业档案</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">向右</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe708;</span>
                <div class="name">省略号</div>
                <div class="code-name">&amp;#xe708;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">合同</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">任务</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe695;</span>
                <div class="name">商机监控</div>
                <div class="code-name">&amp;#xe695;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">shuse lishichaxun</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe973;</span>
                <div class="name">首页,屋子,主页,房子</div>
                <div class="code-name">&amp;#xe973;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">菜单-客户档案</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ca;</span>
                <div class="name">待付款</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">盖章</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe693;</span>
                <div class="name">个人中心</div>
                <div class="code-name">&amp;#xe693;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">租赁单位</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">资产</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1695524238036') format('woff2'),
       url('iconfont.woff?t=1695524238036') format('woff'),
       url('iconfont.ttf?t=1695524238036') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-wuyedangan"></span>
            <div class="name">
              物业档案
            </div>
            <div class="code-name">.icon-wuyedangan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangyou"></span>
            <div class="name">
              向右
            </div>
            <div class="code-name">.icon-xiangyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenglvehao"></span>
            <div class="name">
              省略号
            </div>
            <div class="code-name">.icon-shenglvehao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hetong"></span>
            <div class="name">
              合同
            </div>
            <div class="code-name">.icon-hetong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-task"></span>
            <div class="name">
              任务
            </div>
            <div class="code-name">.icon-task
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-business_monitor"></span>
            <div class="name">
              商机监控
            </div>
            <div class="code-name">.icon-business_monitor
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuselishichaxun"></span>
            <div class="name">
              shuse lishichaxun
            </div>
            <div class="code-name">.icon-shuselishichaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-home"></span>
            <div class="name">
              首页,屋子,主页,房子
            </div>
            <div class="code-name">.icon-home
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kehudangan"></span>
            <div class="name">
              菜单-客户档案
            </div>
            <div class="code-name">.icon-kehudangan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daifukuan"></span>
            <div class="name">
              待付款
            </div>
            <div class="code-name">.icon-daifukuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weibiaoti-1_huaban1fuben28"></span>
            <div class="name">
              盖章
            </div>
            <div class="code-name">.icon-a-weibiaoti-1_huaban1fuben28
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenzhongxin"></span>
            <div class="name">
              个人中心
            </div>
            <div class="code-name">.icon-gerenzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zulindanwei"></span>
            <div class="name">
              租赁单位
            </div>
            <div class="code-name">.icon-zulindanwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zichan"></span>
            <div class="name">
              资产
            </div>
            <div class="code-name">.icon-zichan
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wuyedangan"></use>
                </svg>
                <div class="name">物业档案</div>
                <div class="code-name">#icon-wuyedangan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangyou"></use>
                </svg>
                <div class="name">向右</div>
                <div class="code-name">#icon-xiangyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenglvehao"></use>
                </svg>
                <div class="name">省略号</div>
                <div class="code-name">#icon-shenglvehao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hetong"></use>
                </svg>
                <div class="name">合同</div>
                <div class="code-name">#icon-hetong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-task"></use>
                </svg>
                <div class="name">任务</div>
                <div class="code-name">#icon-task</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-business_monitor"></use>
                </svg>
                <div class="name">商机监控</div>
                <div class="code-name">#icon-business_monitor</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuselishichaxun"></use>
                </svg>
                <div class="name">shuse lishichaxun</div>
                <div class="code-name">#icon-shuselishichaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-home"></use>
                </svg>
                <div class="name">首页,屋子,主页,房子</div>
                <div class="code-name">#icon-home</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kehudangan"></use>
                </svg>
                <div class="name">菜单-客户档案</div>
                <div class="code-name">#icon-kehudangan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daifukuan"></use>
                </svg>
                <div class="name">待付款</div>
                <div class="code-name">#icon-daifukuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weibiaoti-1_huaban1fuben28"></use>
                </svg>
                <div class="name">盖章</div>
                <div class="code-name">#icon-a-weibiaoti-1_huaban1fuben28</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenzhongxin"></use>
                </svg>
                <div class="name">个人中心</div>
                <div class="code-name">#icon-gerenzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zulindanwei"></use>
                </svg>
                <div class="name">租赁单位</div>
                <div class="code-name">#icon-zulindanwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zichan"></use>
                </svg>
                <div class="name">资产</div>
                <div class="code-name">#icon-zichan</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
