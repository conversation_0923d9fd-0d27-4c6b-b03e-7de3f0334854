<template>
	<view class="piaoyi-signature">
		<view class="piaoyi-signature-box pr">
			<view class="sign-box">
				<canvas class="mycanvas" :style="{width:width +'px',height:height +'px'}" id="mycanvas"
					canvas-id="mycanvas" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"></canvas>

				<canvas canvas-id="camCacnvs" id="camCacnvs" :style="{width:height +'px',height:width +'px'}"
					class="canvsborder"></canvas>

			</view>
			<!-- #ifdef APP-PLUS -->
			<view class="sigh-btns pa">
				<button class="btn1" @tap="handleConfirm">确定</button>
			</view>
			<view class="sigh-btns1 pa">
				<button class="btn" @tap="handleReset">重新签名</button>
			</view>
			<!-- #endif -->
			<!-- #ifndef APP-PLUS -->
			<view class="btn-list" style="">
				<cover-view class="sigh-btns pa">
					<button class="btn1" @tap="handleConfirm">确定</button>
				</cover-view>
				<cover-view class="sigh-btns1 pa">
					<button class="btn" @tap="handleReset">重新签名</button>
				</cover-view>
			</view>
			<!-- #endif -->
		</view>
	</view>
</template>

<script>
	var x = 20;
	var y = 20;
	var tempPoint = []; //用来存放当前画纸上的轨迹点
	var id = 0;
	var type = '';
	let that;
	let canvasw;
	let canvash;
	export default {
		data() {
			return {
				//签名
				ctx: '', //绘图图像
				points: [], //路径点集合,
				width: 0,
				height: 0
			}
		},
		mounted() {
			var that = this
			this.ctx = uni.createCanvasContext('mycanvas', this); //创建绘图对象
			//设置画笔样式
			this.ctx.lineWidth = 3;
			this.ctx.lineCap = 'round';
			this.ctx.lineJoin = 'round';
			this.ctx.setFillStyle('#000000')
			uni.getSystemInfo({
				success: function(res) {
					console.log(res)
					that.width = res.windowWidth;
					that.height = res.windowHeight;
				}
			});
		},
		methods: {
			//签名
			//触摸开始，获取到起点
			touchstart: function(e) {
				let startX = e.changedTouches[0].x;
				let startY = e.changedTouches[0].y;
				let startPoint = {
					X: startX,
					Y: startY
				};

				this.points.push(startPoint);

				//每次触摸开始，开启新的路径
				this.ctx.beginPath();
			},

			touchmove: function(e) {
				let moveX = e.changedTouches[0].x;
				let moveY = e.changedTouches[0].y;
				let movePoint = {
					X: moveX,
					Y: moveY
				};
				this.points.push(movePoint); //存点
				let len = this.points.length;
				if (len >= 2) {
					this.draw(); //绘制路径
				}
				tempPoint.push(movePoint);
			},
			touchend: function() {
				this.points = [];
			},
			draw: function() {
				let point1 = this.points[0];
				let point2 = this.points[1];
				this.points.shift();
				this.ctx.moveTo(point1.X, point1.Y);
				this.ctx.lineTo(point2.X, point2.Y);
				this.ctx.stroke();
				this.ctx.draw(true);
			},
			//清空画布
			handleReset: function() {
				let that = this
				that.ctx.clearRect(0, 0, that.width, that.height);
				that.ctx.draw(true);
				tempPoint = [];
			},

			handleConfirm: function() {
				let that = this
				if (tempPoint.length == 0) {
					uni.showToast({
						title: '请先签名',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				setTimeout(() => {
					uni.canvasToTempFilePath({
						canvasId: 'mycanvas',
						success: (res) => {
							let tempPath = res.tempFilePath;
							const ctx = uni.createCanvasContext('camCacnvs', that);
							ctx.translate(0, that.width);
							ctx.rotate((-90 * Math.PI) / 180);
							ctx.drawImage(tempPath, 0, 0, that.width, that.height);
							ctx.draw();
							setTimeout(() => {
								uni.canvasToTempFilePath({
										canvasId: 'camCacnvs',
										success: function(res) {
											console.log(res.tempFilePath)
											let path = res.tempFilePath;
											that.$emit('onSuccess', path)
											// console.log(path, 'path')
										},
										fail: err => {
											that.$emit('onError', '生成失败')
											// console.log('fail', err);
										}
									},
									this
								);
							}, 200);
						},
						fail(err) {
							console.log(err)
						}
					}, this);
				}, 200)
			},
		}
	}
</script>

<style scoped lang="scss">
	.btn-list {
		position: absolute;
		bottom: 50rpx;
		left: 80rpx;
		width: 80%;
		display: flex;
		justify-content: space-between;
	}

	.piaoyi-signature {
		display: flex;
		flex-direction: row;
		background-color: #fff;
		border-radius: 10rpx;

		&-box {
			height: 100%;
			background: #FFFFFF;
			position: relative;
		}
	}

	// .signYu{
	// 	&-t{
	// 		width:150rpx;
	// 		height: 60rpx;
	// 		font-size: 30rpx;
	// 		font-family: PingFang;
	// 		font-weight: 500;
	// 		color: #AAAAAA;
	// 		transform: rotate(90deg);
	// 		text-align: center;
	// 	}
	// }
	.signTips {
		border-left: 1rpx solid #ededed;
		position: relative;

		&-t {
			width: 80rpx;
			transform: rotate(90deg);
			text-align: center;
		}
	}

	.sign-box {
		height: 100%;
		margin: auto;
		display: flex;
		flex-direction: column;
		text-align: center;
		margin-left: 0rpx;
		position: relative;
	}

	.sign-view {
		height: 100%;
	}

	.sigh-btns {
		left: 20rpx;
		position: absolute;
		bottom: 500rpx;
		transform: rotate(90deg);
	}

	.sigh-btns1 {
		left: 160rpx;
		position: absolute;
		bottom: 500rpx;
		transform: rotate(90deg);
	}

	.btn {
		width: 280rpx;
		height: 75rpx;
		line-height: 75rpx;
		border-radius: 10rpx;
		background-color: #fff;
		font-size: 36rpx;
		font-family: PingFang;
		font-weight: 500;
		color: #DEBC00;
		box-sizing: border-box;
	}

	.btn1 {
		width: 280rpx;
		height: 75rpx;
		line-height: 75rpx;
		background: #D9BD25;
		box-shadow: 0px 1px 9rpx 0px rgba(84, 84, 84, 0.42);
		border-radius: 10rpx;
		font-size: 36rpx;
		font-family: PingFang;
		font-weight: 500;
		color: #fff;
		text-align: center;
	}

	.mycanvas {
		margin: auto 0rpx;
		background-color: #ececec;
		// background-image: url('/static/image/index/write.png');
		// background-size: 100% 100%;
	}

	.canvsborder {
		border: 1rpx solid #333;
		position: fixed;
		top: 0;
		left: 10000rpx;
	}
</style>