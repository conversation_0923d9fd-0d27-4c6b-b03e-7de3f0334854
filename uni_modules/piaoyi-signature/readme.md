### piaoyi-signature 手写签名

**使用方法：**

支付宝小程序会报一个```TypeError: this.callback is not a function```,原因是因为uni.canvasToTempFilePath(function, this)后面这个这个this报错,微信小程序需要这个this,请自行适配哈,这不是本插件里面的错误,经过反复测试,不管这个报错也不影响任何功能使用

```
<piaoyiSignature @onSuccess="onSuccess" @onError="onError"/>
```

```
import piaoyiSignature from '@/uni_modules/piaoyi-signature/components/piaoyi-signature/piaoyi-signature.vue'
export default {
    components: {
        piaoyiSignature
    },
    data() {
        return {
        }
    },
    methods: {
    	onSuccess(e) {
    		console.log(e)
    	},
    	onError(e) {
    		console.log(e)
    	}
    }
}
```

#### 事件说明

|   事件名    | 返回值 |      描述      |
| :---------: | :----: | :------------: |
| @onSuccess |   图片信息   | 点击确定的成功回调 |
| @onError | 失败的回调 | 失败的回调 |

### 可接定制化组件开发
### 右侧有本人代表作小程序二维码，可以扫码体验
### 如使用过程中有问题或有一些好的建议，欢迎加QQ群互相学习交流：120594820